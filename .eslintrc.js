module.exports = {
  root: true,
  env: {
    browser: true,
    node: true,
    es6: true
  },

  extends: [
    'plugin:vue/recommended',
    '@vue/standard'
  ],

  parserOptions: {
    parser: 'babel-eslint',
    ecmaVersion: 2021,
    ecmaFeatures: {
      jsx: true
    },
    sourceType: 'module'
  },

  rules: {
    'no-console': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'no-debugger': process.env.NODE_ENV === 'production' ? 'warn' : 'off',
    'vue/max-attributes-per-line': ['error', {
      singleline: 10, // 单行属性超过十个必须换行显示
      multiline: 1 // 多行属性不能超过一个
    }],
    'vue/singleline-html-element-content-newline': 'off', // 单行文本必须折行
    'no-useless-escape': 'off', // 转义字符串，模板文字和正则表达式中的非特殊字符不会产生任何影响
    'no-mixed-operators': 'off', // 用括号括起复杂的表达式可以明确开发人员的意图
    'no-multiple-empty-lines': ['error', { max: 1 }], // 空格对于分隔代码的逻辑部分非常有用，但是多余的空白占用了更多的屏幕。
    'semi': ['error', 'never'], // 每个语句的末尾是否需要分号
    'camelcase': ['off', { 'properties': 'always' }], // 命名风格指南 camelcase 和 underscores
    'quote-props': 'off', // 对象字面值属性名称可以用两种方式定义：使用文字或使用字符串
    'dot-notation': 'off', // 可以使用点符号（foo.bar）或方括号表示法（foo["bar"]）来访问属性
    'semi-spacing': ['error', { before: false, after: true }], // JavaScript允许你在分号之前或之后放置不必要的空格。
    'space-before-function-paren': ['error', 'never'], // 格式化函数时，函数名称或function关键字与开始参数之间允许有空格。
    'object-curly-spacing': ['error', 'always', { objectsInObjects: false }], // 允许在大括号之间留出空格
    'prefer-promise-reject-errors': 'off', // 只将内置Error对象的实例传递给reject()为了Promises中的用户定义错误的函数是一种很好的做法
    'vue/no-unused-vars': 'warn', // 临时关闭
    'no-unused-vars': 'warn', // 临时关闭
    'eqeqeq': 'warn' // 临时关闭
  },

  overrides: [
    {
      files: [
        '**/__tests__/*.{j,t}s?(x)',
        '**/tests/unit/**/*.spec.{j,t}s?(x)'
      ],
      env: {
        jest: true
      }
    }
  ]
}
