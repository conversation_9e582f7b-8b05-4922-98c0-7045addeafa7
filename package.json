{"name": "packages_base", "version": "1.0.0", "description": "满意度项目", "author": "SNZK", "scripts": {"test:unit": "vue-cli-service test:unit", "dev": "vue-cli-service serve --mode localdev", "build:prod": "vue-cli-service build --mode production", "build:uat": "vue-cli-service build --mode staging", "build:test": "vue-cli-service build --mode test", "build:dev": "vue-cli-service build --mode development", "new": "plop", "svgo": "svgo -f src/icons/svg --config=src/icons/svgo.yml", "prepare": "husky install"}, "dependencies": {"@popperjs/core": "^2.11.6", "axios": "0.18.1", "big.js": "^6.2.1", "core-js": "3.6.5", "cropperjs": "1.5.9", "element-ui": "^2.15.13", "js-cookie": "2.2.0", "lodash": "^4.17.21", "moment": "^2.29.4", "normalize.css": "7.0.0", "nprogress": "0.2.0", "operation-tree-node": "^1.0.10", "paper": "0.12.11", "path-to-regexp": "2.4.0", "qrcode": "^1.5.1", "qs": "6.9.4", "uuid": "^9.0.0", "v-contextmenu": "2.9.0", "vue": "2.6.10", "vue-router": "3.0.2", "vue-runtime-helpers": "1.1.2", "vuedraggable": "2.20.0", "vuex": "3.1.0"}, "devDependencies": {"@vue/cli-plugin-babel": "4.4.4", "@vue/cli-plugin-eslint": "4.4.4", "@vue/cli-plugin-unit-jest": "^4.5.19", "@vue/cli-service": "4.4.4", "@vue/eslint-config-standard": "5.1.2", "@vue/test-utils": "^1.0.3", "autoprefixer": "9.5.1", "babel-eslint": "10.1.0", "babel-helper-vue-jsx-merge-props": "^2.0.3", "babel-plugin-dynamic-import-node": "2.3.3", "babel-plugin-syntax-jsx": "^6.18.0", "babel-plugin-transform-remove-console": "6.9.4", "babel-plugin-transform-vue-jsx": "^3.7.0", "babel-preset-env": "^1.7.0", "chalk": "2.4.2", "chokidar": "2.1.5", "compression-webpack-plugin": "6.0.5", "eslint": "7.12.1", "eslint-plugin-import": "2.20.2", "eslint-plugin-node": "11.1.0", "eslint-plugin-promise": "4.2.1", "eslint-plugin-standard": "4.0.0", "eslint-plugin-vue": "7.20.0", "filemanager-webpack-plugin": "2.0.5", "html-webpack-plugin": "3.2.0", "husky": "^7.0.4", "lint-staged": "8.1.5", "node-sass": "^4.14.1", "plop": "2.3.0", "sass-loader": "8.0.2", "script-ext-html-webpack-plugin": "2.1.3", "svg-sprite-loader": "4.1.3", "svgo": "1.2.0", "vue-template-compiler": "2.6.10"}, "browserslist": ["> 1%", "last 2 versions"], "engines": {"node": ">=8.9 <15", "npm": ">= 3.0.0"}, "license": "ISC", "repository": {"type": "git", "url": "********************************:estate/satisfaction-service/satisfaction-web-pc.git"}}