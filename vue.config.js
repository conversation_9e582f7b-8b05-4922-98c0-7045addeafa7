'use strict'
const path = require('path')

// const FileManagerPlugin = require('filemanager-webpack-plugin') // 压缩为 zip
// const CompressionPlugin = require('compression-webpack-plugin') // 压缩gzip
// const packageInfo = require('./package.json')

function resolve(dir) {
  return path.join(__dirname, dir)
}

const name = '华发股份满意度评价管理系统' // page title

const port = process.env.port || process.env.npm_config_port || 9600 // dev port

// All configuration item explanations can be find in https://cli.vuejs.org/config/
module.exports = {
  publicPath: '/pc',
  outputDir: 'dist',
  assetsDir: 'static/v' + process.env.npm_package_version + '.' + new Date().getHours(),
  lintOnSave: process.env.NODE_ENV === 'localdev',
  productionSourceMap: process.env.NODE_ENV === 'localdev',
  devServer: {
    port: port,
    open: true,
    overlay: {
      warnings: false,
      errors: true
    },
    headers: {
      'Access-Control-Allow-Origin': '*'
    },
    proxy: {
      '/dev-api': {
        target: process.env.VUE_APP_AUTH_API,
        changeOrigin: true,
        pathRewrite: {
          ['^' + '/dev-api']: ''
        }
      }
    }
  },
  configureWebpack: config => {
    const obj = {
      name: name,
      resolve: {
        alias: {
          '@': resolve('src'),
          '@root': resolve('./')
        }
      }
    }

    // 排除依赖
    const externals = {
      echarts: 'echarts',
      moment: 'moment'
    }
    // 华发这边不需要zip包
    // if (process.env.NODE_ENV === 'production') {
    //   Object.assign(obj, {
    //     plugins: [
    //       new CompressionPlugin(), // 打包gzip
    //       new FileManagerPlugin({
    //         onEnd: {
    //           archive: [{ source: './dist', destination: `./myd_${packageInfo.version}.zip` }]
    //         }
    //       })
    //     ]
    //   })
    // }
    return {
      ...obj,
      externals
    }
  },
  chainWebpack(config) {
    // it can improve the speed of the first screen, it is recommended to turn on preload
    // it can improve the speed of the first screen, it is recommended to turn on preload
    config.plugin('preload').tap(() => [
      {
        rel: 'preload',
        // to ignore runtime.js
        // https://github.com/vuejs/vue-cli/blob/dev/packages/@vue/cli-service/lib/config/app.js#L171
        fileBlacklist: [/\.map$/, /hot-update\.js$/, /runtime\..*\.js$/],
        include: 'initial'
      }
    ])

    // when there are many pages, it will cause too many meaningless requests
    config.plugins.delete('prefetch')

    config.module
      .rule('eslint')
      .exclude
      .add(/youma-ui/)
      .end()

    config.module
      .rule('thejs')
      .test(/\.js$/)
      .include
      .add(path.resolve('src'))
      .add(path.resolve('node_modules/element-ui/packages'))
      .end()
      .use('babel-loader')
      .loader('babel-loader')
      .end()
    // set svg-sprite-loader
    config.module
      .rule('svg')
      .exclude.add(resolve('src/icons'))
      .end()
    config.module
      .rule('icons')
      .test(/\.svg$/)
      .include.add(resolve('src/icons'))
      .end()
      .use('svg-sprite-loader')
      .loader('svg-sprite-loader')
      .options({
        symbolId: 'icon-[name]'
      })
      .end()

    config
      .when(process.env.NODE_ENV !== 'localdev',
        config => {
          config
            .plugin('ScriptExtHtmlWebpackPlugin')
            .after('html')
            .use('script-ext-html-webpack-plugin', [{
              // `runtime` must same as runtimeChunk name. default is `runtime`
              inline: /runtime\..*\.js$/
            }])
            .end()
          // 汇总依赖
          config
            .optimization.splitChunks({
              chunks: 'all',
              cacheGroups: {
                libs: {
                  name: 'chunk-libs',
                  test: /[\\/]node_modules[\\/]/,
                  priority: 10,
                  chunks: 'initial' // only package third parties that are initially dependent
                },
                umUI: {
                  name: 'chunk-umUI', // split elementUI into a single package
                  priority: 20, // the weight needs to be larger than libs and app or it will be packaged into libs or app
                  test: /[\\/]node_modules[\\/]@um(.*)/ // in order to adapt to cnpm
                },
                commons: {
                  name: 'chunk-commons',
                  test: resolve('src/components'), // can customize your rules
                  minChunks: 3, //  minimum common number
                  priority: 5,
                  reuseExistingChunk: true
                }
              }
            })
          // https:// webpack.js.org/configuration/optimization/#optimizationruntimechunk
          config.optimization.runtimeChunk('single')
        }
      )
  },
  css: {
    loaderOptions: {
      sass: {
        prependData: (loaderContext) => {
          return '@import "@/styles/element-variables.scss";'
        }
      }
    }
  }
}
