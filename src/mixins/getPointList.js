import { getPointOption } from '@/api/common'
import { POINT_RULE } from '@/enum'
const OHTER_CODE = 'other'

export default {
  data() {
    return {
      pointOptions: []
    }
  },
  methods: {
    // 获取触点（适用场景）相关数据
    // type为true，则查询出数据字典关联的触点信息名称
    // ruleCode 如果传值，则指查询对应code的数据
    getPointOption(type = false, ruleCode = undefined) {
      return getPointOption({ all: false, type, ruleCode }).then(res => {
        const data = res.data || []
        const obj = {}
        if (type) {
          data.filter(item => item.ruleCode !== POINT_RULE.SPECIAL).forEach(item => {
            if (item.pointTypeName) {
              this.formatData(obj, item, item.pointTypeName, item.pointTypeId)
            } else {
              this.formatData(obj, item, '其他', OHTER_CODE)
            }
          })
        } else {
          data.forEach(item => {
            if (this.needSpecial && item.ruleCode === POINT_RULE.SPECIAL) { // 专项调研的单独处理
              this.formatData(obj, item, item.pointName, item.ruleCode)
            } if (this.needDeep && item.ruleCode === POINT_RULE.DEEP) { // 深访调研的单独处理
              this.formatData(obj, item, item.pointName, item.ruleCode)
            } else if (!item.ownerStageCode && item.ruleCode !== POINT_RULE.SPECIAL && item.ruleCode !== POINT_RULE.DEEP) { // 归类为其它,随便定义一个code
              this.formatData(obj, item, '其他', OHTER_CODE)
            } else if (item.ruleCode !== POINT_RULE.SPECIAL && item.ruleCode !== POINT_RULE.DEEP) {
              this.formatData(obj, item, item.ownerStageName, item.ownerStageCode)
            }
          })
        }
        this.pointOptions = Object.values(obj)
        this.afterGetOptions && this.afterGetOptions()
      })
    },
    // 处理触点相关数据
    formatData(obj, item, parentLabel, parentValue) {
      if (obj[parentValue]) {
        obj[parentValue].children.push({
          label: item.pointName,
          value: item.pointId,
          ...item
        })
      } else {
        obj[parentValue] = {
          label: parentLabel,
          value: item[parentValue] || parentValue,
          ...item,
          children: [{
            label: item.pointName,
            value: item.pointId,
            ...item
          }]
        }
      }
    }
  }
}
