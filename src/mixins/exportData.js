import { mapMutations } from 'vuex'
export default {
  data() {
    return {
      exportApi: null,
      exportLoading: false
    }
  },
  created() {
  },
  methods: {
    ...mapMutations(['globalData/SET_EXPORT_SHOW']),
    formatExportParams() {
      return this.searchForm || this.searchForm1
    },
    exportData() {
      const params = this.formatExportParams()
      this.exportLoading = true
      this.exportApi(params).then(res => {
        this.$notify({
          title: '导出提示',
          message: '请注意右上角导出进度，导出成功后，可以下载文件！',
          position: 'top-left'
        })
        this['globalData/SET_EXPORT_SHOW'](true)
      }).finally(() => {
        this.exportLoading = false
      })
    }
  }
}
