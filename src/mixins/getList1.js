export default {
  data() {
    return {
      listApi1: null,
      tableData1: [],
      searchForm1: {
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      tableLoading1: false,
      tableTotal1: 0
    }
  },
  components: {
  },
  watch: {
  },
  created() {
  },
  methods: {
    // 获取列表
    getList1(state = '') {
      this.tableLoading1 = true
      // const load = this.$load()
      if (state !== 'page') {
        // 为了兼容formData分页格式
        if (this.searchForm1.number) {
          this.searchForm1.number = 1
        } else {
          this.searchForm1.page.pageNum = 1
        }
      }
      if (!this.listApi1) return false
      this.beforeApiCallBack1 && this.beforeApiCallBack1()
      this.$emit('getList1')
      this.listApi1({
        ...this.searchForm1
      }).then(d => {
        this.tableData1 = d.data.list
        this.tableTotal1 = d.data.page.total
        this.tableLoading1 = false
        // 统计
        this.tableCount1 = d.data.stateCount || {}
        this.$nextTick(() => {
          this.$refs.table?.el?.doLayout()
        })
        this.tableCallBack1 && this.tableCallBack1(d.data)
        // load.close()
      }).catch(e => {
        this.tableTotal1 = 0
        this.tableLoading1 = false
        this.$errorHandle(e)
        this.tableData1 = []
        // load.close()
      })
    }
  }
}
