import { getPageSize } from '@/utils/cache'

export default {
  data() {
    return {
      listApi: null,
      tableData: [],
      searchForm: {
        page: {
          pageNum: 1,
          pageSize: getPageSize()
        }
      },
      tableLoading: false,
      tableTotal: 0,
      payAmountList: [],
      tableCount: {}
    }
  },
  components: {
  },
  watch: {
    'searchForm.page.pageSize'(val) {
      this.$setPageSize(val)
    }
  },
  created() {
  },
  methods: {
    beforeApiCallBack(params) {
      return params
    },
    afterApiCallBack(data) {

    },
    // 获取列表
    async getList(state = '') {
      // const load = this.$load()
      if (state !== 'page') {
        // 为了兼容formData分页格式
        if (this.searchForm.number) {
          this.searchForm.number = 1
        } else {
          this.searchForm.page.pageNum = 1
        }
      }
      if (!this.listApi) return false
      this.$emit('getList')
      const params = this.beforeApiCallBack({ ...this.searchForm })
      if (params === 'ABORT') return
      this.tableLoading = true
      try {
        const d = await this.listApi({
          ...params
        })
        this.tableData = d.data.list || []
        this.tableTotal = d.data.page?.total
        this.tableLoading = false
        // load.close()
        if (this.tableData.length !== 0) {
          if (this.tableData.length === 1 && this.tableData[0].sumFlag === 1) {
            this.payAmountList = []
          } else {
            this.payAmountList = this.tableData[0].payAmountList
          }
        }

        if (d.data.stateCount) {
          this.tableCount = d.data.stateCount
        } else {
          this.tableCount = {}
        }
        this.$nextTick(() => {
          this.$refs.table?.el?.doLayout()
        })
        this.afterApiCallBack(d.data)
      } catch (e) {
        this.tableTotal = 0
        this.tableLoading = false
        // load.close()
        this.tableData = []
        this.$errorHandle(e)
      }
    }
  }
}
