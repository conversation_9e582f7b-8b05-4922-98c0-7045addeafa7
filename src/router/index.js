import Vue from 'vue'
import Router from 'vue-router'
import EleUi from '@/layout/eleUi'
/* Layout */
import Layout from '@/layout'
import baseRouter from './modules/base' // 基础设置
import questionRouter from './modules/question' // 问卷管理
import planRouter from './modules/plan' // 调研计划
import specialRouter from './modules/special' // 特殊事项
import telRouter from './modules/tel' // 特殊事项
import satisfactionRouter from './modules/satisfaction' // 满意度分析
Vue.use(Router)
/**
 * Note: sub-menu only appear when route children.length >= 1
 * Detail see: https://panjiachen.github.io/vue-element-admin-site/guide/essentials/router-and-nav.html
 *
 * hidden: true                   if set true, item will not show in the sidebar(default is false)
 * alwaysShow: true               if set true, will always show the root menu
 *                                if not set alwaysShow, when item has more than one children route,
 *                                it will becomes nested mode, otherwise not show the root menu
 * redirect: noRedirect           if set noRedirect will no redirect in the breadcrumb
 * name:'router-name'             the name is used by <keep-alive> (must set!!!)
 * meta : {
    roles: ['admin','editor']    control the page roles (you can set multiple roles)
    title: 'title'               the name show in sidebar and breadcrumb (recommend set)
    icon: 'svg-name'/'el-icon-x' the icon show in the sidebar
    keepAlive: true              如果设置为 true 则开启缓存
    affix: true                  if set true, the tag will affix in the tags-view
    breadcrumb: false            if set false, the item will hidden in breadcrumb(default is true)
    activeMenu: '/example/list'  if set path, the sidebar will highlight the path you set
  }
 */

/**
 * constantRoutes
 * a base page that does not have permission requirements
 * all roles can be accessed
 */
export const constantRoutes = [
  {
    path: '/redirect',
    component: Layout,
    hidden: true,
    children: [
      {
        path: '/redirect/:path(.*)',
        component: () => import('@/views/redirect/index')
      }
    ]
  },
  {
    path: '/login',
    component: () => import('@/views/login/auth'),
    hidden: true
  },
  {
    path: '/oa/special/apply',
    component: () => import('@/views/oa/special/apply'),
    hidden: true
  },
  {
    path: '/oa/special/reverse',
    component: () => import('@/views/oa/special/reverse'),
    hidden: true
  },
  {
    path: '/404',
    component: () => import('@/views/error-page/404'),
    hidden: true
  },
  {
    path: '/error',
    component: () => import('@/views/error-page/error'),
    hidden: true
  },
  {
    path: '/',
    component: Layout,
    meta: { title: '工作台' },
    hidden: true,
    children: [
      {
        path: '',
        component: EleUi,
        children: [{
          path: '',
          name: 'Home',
          meta: { title: '首页', icon: 'dashboard', keepAlive: true },
          component: () => import('@/views/home')
        }]
      }
    ]
  }
]

/**
 * asyncRoutes
 * the routes that need to be dynamically loaded based on user roles
 */
export const asyncRoutes = [
  // 404 page must be placed at the end !!!
  ...questionRouter,
  ...planRouter,
  ...specialRouter,
  ...telRouter,
  ...satisfactionRouter,
  ...baseRouter,
  { path: '*', redirect: '/404', hidden: true }
]

const createRouter = () => new Router({
  mode: 'history', // require service support
  scrollBehavior: () => ({ y: 0 }),
  routes: constantRoutes,
  base: '/pc'
})

const router = createRouter()

// Detail see: https://github.com/vuejs/vue-router/issues/1234#issuecomment-357941465
export function resetRouter() {
  const newRouter = createRouter()
  router.matcher = newRouter.matcher // reset router
}

export default router
