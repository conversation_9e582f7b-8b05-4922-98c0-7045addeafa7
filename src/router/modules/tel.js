import EleUi from '@/layout/eleUi'
import Link from '@/layout/link'

export default [
  {
    path: '/telephoneInterviews',
    component: Link,
    meta: { title: '电话回访', icon: 'dhhf', roles: ['MYD_DHHF'] },
    children: [
      {
        path: '/telephoneInterviews/list',
        component: EleUi,
        meta: { title: '电话回访', roles: ['MYD_DHHF_DHHF'] },
        children: [
          {
            path: '',
            component: () => import('@/views/telephoneInterviews/list/index.vue'),
            name: 'TelephoneInterviewsList',
            meta: { title: '电话回访', keepAlive: true, roles: ['DHHF_DHHF_CK'] }
          }
        ]
      },
      {
        path: '/TelephoneInterviews/customervoice',
        component: EleUi,
        meta: { title: '客户原声', roles: ['MYD_DHHF_KHYS'] },
        children: [
          {
            path: '',
            component: () => import('@/views/telephoneInterviews/customervoice/index.vue'),
            name: 'TelephoneInterviewsVoice',
            meta: { title: '客户原声', keepAlive: true, roles: ['DHHF_KHYS_CK'] }
          },
          {
            path: 'preview',
            hidden: true,
            component: () => import('@/views/telephoneInterviews/customervoice/preview'),
            name: 'TelephoneInterviewsVoicePreview',
            meta: { title: '客户原声-问卷查看', keepAlive: false, activeMenu: '/TelephoneInterviews/customervoice', roles: ['DHHF_KHYS_CKWJ'] }
          }
        ]
      }
    ]
  }
]
