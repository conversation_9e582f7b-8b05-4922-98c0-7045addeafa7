import EleUi from '@/layout/eleUi'
import Link from '@/layout/link'

export default [
  {
    path: '/specialmatters',
    component: Link,
    meta: { title: '特殊事项', icon: 'tssx', roles: ['MYD_TSSX'] },
    children: [
      {
        path: '/specialmatters/apply',
        component: EleUi,
        meta: { title: '特殊事项申请', roles: ['MYD_TSSX_TSSXSQ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/specialmatters/apply'),
            name: 'SpecialmattersApply',
            meta: { title: '特殊事项申请', keepAlive: true, roles: ['TSSX_TSSXSQ_CK'] }
          },
          {
            path: 'add',
            hidden: true,
            component: () => import('@/views/specialmatters/apply/edit/index.vue'),
            name: 'SpecialmattersApplyAdd',
            meta: { title: '特殊事项申请-新增', keepAlive: false, activeMenu: '/specialmatters/apply', roles: ['TSSX_TSSXSQ_TSSXSQ'] }
          },
          {
            path: 'edit',
            hidden: true,
            component: () => import('@/views/specialmatters/apply/edit/index.vue'),
            name: 'SpecialmattersApplyEdit',
            meta: { title: '特殊事项申请-编辑', keepAlive: false, activeMenu: '/specialmatters/apply', roles: ['TSSX_TSSXSQ_BJ'] }
          },
          {
            path: 'detail',
            hidden: true,
            component: () => import('@/views/specialmatters/apply/detail/index.vue'),
            name: 'SpecialmattersApplyDetail',
            meta: { title: '特殊事项详情', keepAlive: false, activeMenu: '/specialmatters/apply', roles: ['TSSX_TSSXSQ_XQ'] }
          }
        ]
      },
      {
        path: '/specialmatters/reversepenalty',
        component: EleUi,
        meta: { title: '反向处罚', roles: ['MYD_TSSX_FXCF'] },
        children: [
          {
            path: '',
            component: () => import('@/views/specialmatters/reversepenalty'),
            name: 'SpecialmattersPenalty',
            meta: { title: '反向处罚', keepAlive: true, roles: ['TSSX_FXCF_CK'] }
          },
          {
            path: 'add',
            hidden: true,
            component: () => import('@/views/specialmatters/reversepenalty/edit/index.vue'),
            name: 'SpecialmattersPenaltyAdd',
            meta: { title: '反向处罚-新增', keepAlive: false, activeMenu: '/specialmatters/reversepenalty', roles: ['TSSX_FXCF_XZ'] }
          },
          {
            path: 'edit',
            hidden: true,
            component: () => import('@/views/specialmatters/reversepenalty/edit/index.vue'),
            name: 'SpecialmattersPenaltyEdit',
            meta: { title: '反向处罚-编辑', keepAlive: false, activeMenu: '/specialmatters/reversepenalty', roles: ['TSSX_FXCF_BJ'] }
          },
          {
            path: 'detail',
            hidden: true,
            component: () => import('@/views/specialmatters/reversepenalty/detail/index.vue'),
            name: 'SpecialmattersPenaltyDetail',
            meta: { title: '反向处罚详情', keepAlive: false, activeMenu: '/specialmatters/reversepenalty', roles: ['TSSX_FXCF_XQ'] }
          }
        ]
      }
    ]
  }
]
