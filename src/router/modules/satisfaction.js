import EleUi from '@/layout/eleUi'
import Link from '@/layout/link'

export default [
  {
    path: '/satisfaction',
    component: Link,
    meta: { title: '满意度分析', icon: 'pkg39_3', roles: ['MYD_MYDFX'] },
    children: [
      {
        path: '/satisfaction/returnVisit',
        component: EleUi,
        meta: { title: '回访概览', roles: ['MYD_MYDFX_HFGL'] },
        children: [
          {
            path: '',
            component: () => import('@/views/satisfaction/returnVisit/index.vue'),
            name: 'SatisfactionReturnVisit',
            meta: { title: '回访概览', keepAlive: true, roles: ['MYDFX_HFGL_CK'] }
          }
        ]
      },
      {
        path: '/satisfaction/goal',
        component: EleUi,
        meta: { title: '满意度目标', roles: ['MYD_MYDFX_MYDMB'] },
        children: [
          {
            path: '',
            component: () => import('@/views/satisfaction/goal/index.vue'),
            name: 'SatisfactionGoal',
            meta: { title: '满意度目标', keepAlive: true, roles: ['MYDFX_MYDMB_CK'] }
          },
          {
            path: 'edit',
            component: () => import('@/views/satisfaction/goal/edit/index.vue'),
            hidden: true,
            name: 'SatisfactionGoalEdit',
            meta: { title: '满意度目标-编辑', keepAlive: false, activeMenu: '/satisfaction/goal', roles: ['MYDFX_MYDMB_BJ'] }
          },
          {
            path: 'detail',
            component: () => import('@/views/satisfaction/goal/edit/index.vue'),
            hidden: true,
            name: 'SatisfactionGoalDetail',
            meta: { title: '满意度目标-详情', keepAlive: false, activeMenu: '/satisfaction/goal', roles: ['MYDFX_MYDMB_XQ'] }
          },
          {
            path: 'log',
            component: () => import('@/views/satisfaction/goal/log/index.vue'),
            hidden: true,
            name: 'SatisfactionGoalLog',
            meta: { title: '满意度目标-日志', keepAlive: false, activeMenu: '/satisfaction/goal', roles: ['MYDFX_MYDMB_CZRZ'] }
          }
        ]
      },
      {
        path: '/satisfaction/mydyl',
        component: EleUi,
        meta: { title: '满意度概览', roles: ['MYD_MYDFX_MYDGL'] },
        children: [
          {
            path: '',
            component: () => import('@/views/satisfaction/mydyl'),
            name: 'SatisfactionMydyl',
            meta: { title: '满意度概览', keepAlive: true, roles: ['MYDFX_MYDGL_CK'] }
          },
          {
            path: 'questionDetail',
            hidden: true,
            component: () => import('@/views/satisfaction/mydyl/questionDetail'),
            name: 'SatisfactionMydylQuestionDetail',
            meta: { title: '满意度概览-问卷明细', keepAlive: false, activeMenu: '/satisfaction/mydyl', roles: ['MYDFX_MYDGL_CK'] }
          },
          {
            path: 'watch',
            hidden: true,
            component: () => import('@/views/satisfaction/mydyl/watch'),
            name: 'SatisfactionMydylWatch',
            meta: { title: '满意度概览-查看答卷', keepAlive: false, activeMenu: '/satisfaction/mydyl', roles: ['MYDFX_MYDGL_CK'] }
          },
          {
            path: 'analyse',
            hidden: true,
            component: () => import('@/views/satisfaction/mydyl/analyse'),
            name: 'SatisfactionMydylAnalyse',
            meta: { title: '满意度概览-统计分析', keepAlive: false, activeMenu: '/satisfaction/mydyl', roles: ['MYDFX_MYDGL_CK'] }
          },
          {
            path: 'pointAnalyse',
            hidden: true,
            component: () => import('@/views/satisfaction/mydyl/pointAnalyse'),
            name: 'SatisfactionMydylPointAnalyse',
            meta: { title: '满意度概览-统计分析', keepAlive: false, activeMenu: '/satisfaction/mydyl', roles: ['MYDFX_MYDGL_CK'] }
          },
          {
            path: 'targetAnalyse',
            hidden: true,
            component: () => import('@/views/satisfaction/mydyl/targetAnalyse'),
            name: 'SatisfactionMydylTargetAnalyse',
            meta: { title: '满意度概览-统计分析', keepAlive: false, activeMenu: '/satisfaction/mydyl', roles: ['MYDFX_MYDGL_CK'] }
          }
        ]
      }
      // {
      //   path: '/satisfaction/abnormal',
      //   component: EleUi,
      //   meta: { title: '异常号码明细表', roles: ['MYD_MYDFX_MYDGL'] },
      //   children: [
      //     {
      //       path: '',
      //       component: () => import('@/views/satisfaction/abnormal'),
      //       name: 'SatisfactionAbnormal',
      //       meta: { title: '异常号码明细表', keepAlive: true, roles: ['MYDFX_MYDGL_CK'] }
      //     }
      //   ]
      // }
    ]
  }
]
