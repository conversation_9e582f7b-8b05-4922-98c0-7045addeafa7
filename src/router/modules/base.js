import EleUi from '@/layout/eleUi'
import Link from '@/layout/link'

export default [
  {
    path: '/base',
    component: Link,
    meta: { title: '基础配置', icon: 'pkg39_6', roles: ['MYD_JCPZ'] },
    children: [
      {
        path: '/base/rule',
        component: EleUi,
        meta: { title: '系统通用规则配置', roles: ['MYD_JCPZ_XTTYGZPZ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/rule'),
            name: 'BaseRule',
            meta: { title: '系统通用规则配置', keepAlive: true, roles: ['JCPZ_XTTYGZ_CK'] }
          }
        ]
      },
      {
        path: '/base/dict',
        component: EleUi,
        meta: { title: '满意度数据字典', roles: ['MYD_JCPZ_MYDSJZD'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/dict'),
            name: 'BaseDict',
            meta: { title: '满意度数据字典列表', keepAlive: true, roles: ['JCPZ_MYDSJZD_CK'] }
          }
        ]
      },
      {
        path: '/base/indicator',
        component: EleUi,
        meta: { title: '指标配置', roles: ['MYD_JCPZ_ZBPZ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/indicator'),
            name: 'BaseIndicator',
            meta: { title: '指标配置', keepAlive: true, roles: ['JCPZ_ZBPZ_CK'] }
          },
          {
            path: 'log',
            component: () => import('@/views/base/indicator/log'),
            name: 'BaseIndicatorLog',
            hidden: true,
            meta: { title: '指标配置-操作日志', keepAlive: true, activeMenu: '/base/indicator', roles: ['JCPZ_ZBPZ_CKXGRZ'] }
          }
        ]
      },
      {
        path: '/base/surveyRule',
        component: EleUi,
        meta: { title: '调研规则设置', roles: ['MYD_JCPZ_DYGZSZ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/surveyRule'),
            name: 'BaseSurveyRule',
            meta: { title: '调研规则设置', keepAlive: true, roles: ['JCPZ_DYGZSZ_CK'] }
          },
          {
            path: 'event/edit',
            component: () => import('@/views/base/surveyRule/event/edit'),
            name: 'BaseEventEdit',
            hidden: true,
            meta: { title: '调研规则设置-事件型触点满意度-编辑', keepAlive: false, activeMenu: '/base/surveyRule', roles: ['JCPZ_DYGZSZ_BJ'] }
          },
          {
            path: 'event/detail',
            component: () => import('@/views/base/surveyRule/event/detail'),
            name: 'BaseEventDetail',
            hidden: true,
            meta: { title: '调研规则设置-事件型触点满意度-详情', keepAlive: false, activeMenu: '/base/surveyRule', roles: ['JCPZ_DYGZSZ_XQ'] }
          },
          {
            path: 'point/add',
            component: () => import('@/views/base/surveyRule/point/edit'),
            name: 'BasePointAdd',
            hidden: true,
            meta: { title: '调研规则设置-即时满意度-新增', keepAlive: false, activeMenu: '/base/surveyRule', roles: ['JCPZ_DYGZSZ_XZJD'] }
          },
          {
            path: 'point/edit',
            component: () => import('@/views/base/surveyRule/point/edit'),
            name: 'BasePointEdit',
            hidden: true,
            meta: { title: '调研规则设置-即时满意度-编辑', keepAlive: false, activeMenu: '/base/surveyRule', roles: ['JCPZ_DYGZSZ_BJ'] }
          },
          {
            path: 'point/detail',
            component: () => import('@/views/base/surveyRule/point/detail'),
            name: 'BasePointDetail',
            hidden: true,
            meta: { title: '调研规则设置-即时满意度-详情', keepAlive: false, activeMenu: '/base/surveyRule', roles: ['JCPZ_DYGZSZ_XQ'] }
          }
        ]
      },
      {
        path: '/base/math',
        component: EleUi,
        meta: { title: '算法配置', roles: ['MYD_JCPZ_SFPZ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/math'),
            name: 'BaseMath',
            meta: { title: '算法配置', keepAlive: true, roles: ['JCPZ_SFPZ_CK'] }
          },
          {
            path: 'edit',
            hidden: true,
            component: () => import('@/views/base/math/edit'),
            name: 'BaseMathEdit',
            meta: { title: '算法配置-编辑', keepAlive: false, activeMenu: '/base/math', roles: ['JCPZ_SFPZ_BJPZ'] }
          },
          {
            path: 'detail',
            hidden: true,
            component: () => import('@/views/base/math/edit'),
            name: 'BaseMathDetail',
            meta: { title: '算法配置-详情', keepAlive: false, activeMenu: '/base/math', roles: ['JCPZ_SFPZ_CK'] }
          }
        ]
      },
      {
        path: '/base/channel',
        component: EleUi,
        meta: { title: '推送渠道设置', roles: ['MYD_JCPZ_TSQDSZ'] },
        children: [
          {
            path: '',
            component: () => import('@/views/base/channel'),
            name: 'BaseChannel',
            meta: { title: '推送渠道设置', keepAlive: true, roles: ['JCPZ_TSQDSZ_CK'] }
          },
          {
            path: 'edit',
            hidden: true,
            component: () => import('@/views/base/channel/edit'),
            name: 'BaseChannelEdit',
            meta: { title: '推送渠道设置-编辑', keepAlive: false, activeMenu: '/base/channel', roles: ['JCPZ_TSQDSZ_BJ'] }
          }
        ]
      },
      {
        path: '/satisfaction/industryBenchmarking',
        component: EleUi,
        meta: { title: '行业对标', roles: ['MYD_MYDFX_HYDB', 'MYD_JCPZ_HYDB'] },
        children: [
          {
            path: '',
            component: () => import('@/views/satisfaction/industryBenchmarking/index.vue'),
            name: 'SatisfactionIndustryBenchmarking',
            meta: { title: '行业对标', keepAlive: true, roles: ['MYDFX_HYDB_CK', 'JCPZ_HYDB_CK'] }
          },
          {
            path: 'edit',
            component: () => import('@/views/satisfaction/industryBenchmarking/edit/index.vue'),
            hidden: true,
            name: 'SatisfactionIndustryBenchmarkingEdit',
            meta: { title: '行业对标-编辑', keepAlive: false, activeMenu: '/satisfaction/industryBenchmarking', roles: ['MYDFX_HYDB_BJ', 'JCPZ_HYDB_BJ'] }
          },
          {
            path: 'detail',
            component: () => import('@/views/satisfaction/industryBenchmarking/edit/index.vue'),
            hidden: true,
            name: 'SatisfactionIndustryBenchmarkingDetail',
            meta: { title: '行业对标-详情', keepAlive: false, activeMenu: '/satisfaction/industryBenchmarking', roles: ['MYDFX_HYDB_CK', 'JCPZ_HYDB_CK'] }
          }
        ]
      }
    ]
  }
]
