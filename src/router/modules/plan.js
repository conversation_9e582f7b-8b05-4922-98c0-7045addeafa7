import EleUi from '@/layout/eleUi'
import Link from '@/layout/link'

export default [
  {
    path: '/plan',
    component: Link,
    meta: { title: '调研计划管理', icon: 'pkg39_2', roles: ['MYD_DYJHGL'] },
    children: [
      {
        path: '/plan/satisfactionSurvey',
        component: EleUi,
        meta: { title: '满意度调研', roles: ['MYD_DYJHGL_MYDDY'] },
        children: [
          {
            path: '',
            component: () => import('@/views/plan/satisfactionSurvey'),
            name: 'PlanSatisfactionSurvey',
            meta: { title: '满意度调研', keepAlive: true, roles: ['DYJHGL_MYDDY_CK'] }
          },
          {
            path: 'add',
            component: () => import('@/views/plan/satisfactionSurvey/add'),
            name: 'PlanSatisfactionSurveyAdd',
            hidden: true,
            meta: { title: '满意度调研-新增计划', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_XZJH'] }
          },
          {
            path: 'edit',
            component: () => import('@/views/plan/satisfactionSurvey/add'),
            name: 'PlanSatisfactionSurveyEidt',
            hidden: true,
            meta: { title: '满意度调研-编辑计划', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_XG'] }
          },
          {
            path: 'surveyInfo',
            component: () => import('@/views/plan/satisfactionSurvey/surveyInfo'),
            name: 'PlanSatisfactionSurveyInfo',
            hidden: true,
            meta: { title: '满意度调研-调研详情', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_DYXQ'] }
          },
          {
            path: 'info',
            component: () => import('@/views/plan/satisfactionSurvey/info'),
            name: 'PlanSatisfactionInfo',
            hidden: true,
            meta: { title: '满意度调研-详情', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_JHXQ'] }
          },
          {
            path: 'analyse',
            component: () => import('@/views/plan/satisfactionSurvey/analyse'),
            name: 'PlanSatisfactionSurveyAnalyse',
            hidden: true,
            meta: { title: '满意度调研-统计分析', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_TJFX'] }
          },
          {
            path: 'preview',
            component: () => import('@/views/wenjuan/preview'),
            name: 'PlanSatisfactionSurveyPreview',
            hidden: true,
            meta: { title: '满意度调研-问卷浏览', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_WJLL'] }
          },
          {
            path: 'watch',
            component: () => import('@/views/plan/satisfactionSurvey/watch'),
            name: 'PlanSatisfactionSurveyWatch',
            hidden: true,
            meta: { title: '满意度调研-查看答卷', keepAlive: false, activeMenu: '/plan/satisfactionSurvey', roles: ['DYJHGL_MYDDY_WJLL'] }
          }
        ]
      },
      {
        path: '/plan/specialissues',
        component: EleUi,
        meta: { title: '专项问题调研', roles: ['MYD_DYJHGL_ZXWTDY'] },
        children: [
          {
            path: '',
            component: () => import('@/views/plan/specialissues'),
            name: 'PlanSpecialIssues',
            meta: { title: '专项问题调研', keepAlive: true, roles: ['DYJHGL_ZXWTDY_CK'] }
          },
          {
            path: 'add',
            component: () => import('@/views/plan/specialissues/add'),
            name: 'PlanSpecialIssuesAdd',
            hidden: true,
            meta: { title: '专项问题调研-新增计划', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_XZJH'] }
          },
          {
            path: 'edit',
            component: () => import('@/views/plan/specialissues/add'),
            name: 'PlanSpecialIssuesEdit',
            hidden: true,
            meta: { title: '专项问题调研-编辑计划', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_XG'] }
          },
          {
            path: 'surveyInfo',
            component: () => import('@/views/plan/specialissues/surveyInfo'),
            name: 'PlanSpecialIssuesSurveyInfo',
            hidden: true,
            meta: { title: '专项问题调研-调研详情', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_DYXQ'] }
          },
          {
            path: 'info',
            component: () => import('@/views/plan/specialissues/info'),
            name: 'PlanSpecialIssuesInfo',
            hidden: true,
            meta: { title: '专项问题调研-详情', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_JHXQ'] }
          },
          {
            path: 'analyse',
            component: () => import('@/views/plan/specialissues/analyse'),
            name: 'PlanSpecialIssuesAnalyse',
            hidden: true,
            meta: { title: '专项问题调研-统计分析', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_TJFX'] }
          },
          {
            path: 'preview',
            component: () => import('@/views/wenjuan/preview'),
            name: 'PlanSpecialIssuesAnalysePreview',
            hidden: true,
            meta: { title: '专项问题调研-问卷浏览', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_WJLL'] }
          },
          {
            path: 'watch',
            component: () => import('@/views/plan/specialissues/watch'),
            name: 'PlanSpecialIssuesSurveyWatch',
            hidden: true,
            meta: { title: '专项问题调研-查看答卷', keepAlive: false, activeMenu: '/plan/specialissues', roles: ['DYJHGL_ZXWTDY_WJLL'] }
          }
        ]
      },
      {
        path: '/plan/depth',
        component: EleUi,
        meta: { title: '深访调研', roles: ['MYD_DYJHGL_SFDY'] },
        children: [
          {
            path: '',
            component: () => import('@/views/plan/depth'),
            name: 'PlanDepth',
            meta: { title: '深访调研', keepAlive: true, roles: ['DYJHGL_SFDY_CK'] }
          },
          {
            path: 'add',
            component: () => import('@/views/plan/depth/add'),
            name: 'PlanDepthAdd',
            hidden: true,
            meta: { title: '深访调研-新增计划', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_XZJH'] }
          },
          {
            path: 'edit',
            component: () => import('@/views/plan/depth/add'),
            name: 'PlanDepthEdit',
            hidden: true,
            meta: { title: '深访调研-编辑计划', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_BJ'] }
          },
          {
            path: 'info',
            component: () => import('@/views/plan/depth/info'),
            name: 'PlanDepthInfo',
            hidden: true,
            meta: { title: '深访调研-详情', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_XQ'] }
          },
          {
            path: 'preview',
            component: () => import('@/views/wenjuan/preview'),
            name: 'PlanSpecialIssuesAnalysePreview',
            hidden: true,
            meta: { title: '深访调研-问卷浏览', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_WJLL'] }
          },
          {
            path: 'surveyInfo',
            component: () => import('@/views/plan/depth/surveyInfo'),
            name: 'PlanDepthSurveyInfo',
            hidden: true,
            meta: { title: '深访调研-调研详情', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_DYXQ'] }
          },
          {
            path: 'analyse',
            component: () => import('@/views/plan/depth/analyse'),
            name: 'PlanDepthAnalyse',
            hidden: true,
            meta: { title: '深访调研-统计分析', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_TJFX'] }
          },
          {
            path: 'surveyInfoLog',
            component: () => import('@/views/plan/depth/surveyInfo/log'),
            name: 'PlanDepthSurveyInfoLog',
            hidden: true,
            meta: { title: '调研详情-操作日志', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_DYXQ_CZRZ'] }
          },
          {
            path: 'watch',
            component: () => import('@/views/plan/depth/watch'),
            name: 'PlanDepthWatch',
            hidden: true,
            meta: { title: '深访调研-查看答卷', keepAlive: false, activeMenu: '/plan/depth', roles: ['DYJHGL_SFDY_DYXQ_CKWJ'] }
          }
        ]
      }
    ]
  }
]
