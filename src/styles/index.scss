@import './mixin.scss';
@import './font.scss';
@import './common.scss';

@import './layout.scss';
@import './element-ui.scss';

@import "~element-ui/packages/theme-chalk/src/index";

* {
  margin: 0;
  padding: 0;
  box-sizing: inherit;
}

p {
  margin-top: 0;
  margin-bottom: 0; // 覆盖 ant 的样式～
}

button,
input,
select,
textarea {
  font-family: inherit;
  font-size: inherit;
  line-height: inherit;
  color: inherit
}

body {
  min-height: 100%;
}

html,
body,
#app {
  width: 100%;
  box-sizing: border-box;
  height: 100%;
}

body {
  -moz-osx-font-smoothing: grayscale;
  -webkit-font-smoothing: antialiased;
  text-rendering: optimizeLegibility;
  font-family: BlinkMacSystemFont, Helvetica Neue, Helvetica, PingFang SC, Hiragino Sans GB, Microsoft YaHei, Arial, sans-serif;
  word-break: break-all;
}

#app {
  display: flex;
  min-width: 1260px;
  min-height: 100%;
  height: 100%;
  margin: 0 auto;
  color: #31415F;
  background: #F5F7FA;
}

.row_1,
.ellipsis {
  width: 100%;
  white-space: nowrap;
  text-overflow: ellipsis;
  overflow: hidden;
}

.row_2 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  white-space: normal;
}

.row_3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  white-space: normal;
}

// @import './element-ui.scss';

.flexStart {display: flex;justify-content: flex-start;}
.flexEnd {display: flex;justify-content: flex-end;}
.flexBetween {display: flex;justify-content: space-between;}
.flexBetweenCenter{display: flex;align-items: center;justify-content: space-between;}
.flexCenter{display: flex;align-items: center;}
.flexAlignJustifyCenter{display: flex;align-items: center;justify-content: center;}
.flex-column{
  flex-direction: column;
}

.flex{display: flex;}
.fr {float: right;}
.fl {float: left;}
.cl{zoom:1;clear: both;}
.dib{display: inline-block;vertical-align: middle;}
.cup{cursor: pointer;}
.text-center {text-align: center;}
.text-right {text-align: right;}
.text-left {text-align: left;}

.margin-l-25 {margin-left: 25px}
.margin-l-20 {margin-left: 20px}
.margin-l-140 {margin-left: 140px}
.margin-l-10 {margin-left: 10px}
.margin-b-20 {margin-bottom: 20px;}
.margin-b-30 {margin-bottom: 30px;}
.margin-b-6 {margin-bottom: 6px;}
.margin-b-10 {margin-bottom: 10px;}
.margin-t-20 {margin-top: 20px;}
.margin-t-16 {margin-top: 16px;}
.margin-t-30 {margin-top: 30px;}
.margin-t-10 {margin-top: 10px;}
.margin-t-60 {margin-top: 60px;}
.margin-t-36 {margin-top: 36px;}
.ml32{margin-left: 32px;}
.ml12{margin-left: 12px;}
.line-24 {line-height: 24px;}

.g-element {
  @import './main.scss';
}

// 部分需要放到全局的样式
.el-upload--picture-card p{
  width: max-content;
  position: absolute;
  bottom: -7px;
}
.choseUser .placeholder{
  padding: 0 15px;
}

// 可以让表格全屏的样式
.g-table_full {
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  background-color: #FFFFFF;
  overflow: hidden;
  color: #31415F;
  transition: 0.3s;
  padding: 20px;
  overflow-y: auto;
  flex: 1;
  display: flex;
  flex-direction: column;
}

.g-container_flex {
  flex: 1;
  display: flex;
  flex-direction: column;
  height: 100%;
}
.card-pd0 .el-card__body{
 padding-bottom: 0;
}
// 隐藏滚动条
.hide-scrollbar {
  scrollbar-width: none;
  -ms-overflow-style: none;
  &::-webkit-scrollbar {
    display: none;
  }
}
// 通用的一些颜色样式
.primary {
  color: $--color-primary;
  &:acitve, &:hover {color: $--color-primary!important; opacity: 0.9;}
}
.success {
  color: $--color-success;
  &:acitve, &:hover {color: $--color-success!important; opacity: 0.9;}
}
.warning {
  color: $--color-warning;
  &:acitve, &:hover {color: $--color-warning!important; opacity: 0.9;}
}
.danger {
  color: $--color-danger;
  &:acitve, &:hover {color: $--color-danger!important; opacity: 0.9;}
}
.info {
  color: $--color-info;
}
.error {
  color: $--color-error;
}
// el-tree 右侧菜单公共样式
.el-popover.custom-tree-popover {
  border: none;
  min-width: auto;
  padding: 0;
}
.u-tree__menu {
  > div {
    line-height: 30px;
    padding: 0 10px;
    border-radius: 4px;
    cursor: pointer;
    font-size: 12px;
    display: flex;
    align-items: center;
    i {
      margin-right: 10px;
      font-size: 16px;
    }
    &:hover {
      color: $--color-primary;
      background-color: #F8FAFB;
    }
  }
}
// 固定底部的按钮
.m-fixed__btm {
  position: fixed;
  bottom: 0;
  height: 80px;
  z-index: 2;
  background: #FFFFFF;
  box-shadow: 0px -3px 5px 1px rgba(229,229,229,0.43);
  display: flex;
  align-items: center;
  justify-content: center;
  width: 100%;
  left: 0px;
  padding-left: 180px;
}
// 修改echarts tooltip样式
.echarts-tooltip {
  background: rgba(51,51,51,0.70)!important;
  border: none!important;
  div {
    color: #fff!important;
    font-weight: 600!important;
    span {
      color: #fff!important;
      font-weight: normal!important;
    }
  }
}
//滚动条美化
/*定义滚动条高宽及背景 高宽分别对应横竖滚动条的尺寸*/
::-webkit-scrollbar {
  width: 8px;
  height: 8px;
}

/*定义滚动条轨道 内阴影+圆角*/
::-webkit-scrollbar-track {
  border-radius: 4px;
  background-color: #ffffff;
}

/*定义滑块 内阴影+圆角*/
::-webkit-scrollbar-thumb {
  border-radius: 4px;
  background-color: #CCCCCC;
}