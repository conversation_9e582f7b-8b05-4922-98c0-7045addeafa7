.m_plan_add{
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  padding: 20px 12px;
  box-sizing: border-box;
  margin-bottom: 80px;
  .g-plan-title-icon{
      display: flex;
      align-items: center;
      margin-left: 5px;
      .u-txt-icon{
        width: 16px;
        height: 16px;
        background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
      }
      .u-txt-title{
        margin-left: 4px;
        font-size: 16px;
        font-weight: 600;
        color: #333;
      }
  }
  .g-plan-form{
    padding: 0 8px;
    margin-top: 24px;
  }
  .border-dashed{
    margin-top: 16px;
    position: relative;

    &:after{
      content: '';
      position: absolute;
      left: 10px;
      right: 10px;
      overflow: hidden;
      border-bottom: 1px dashed #ADB3BF;
    }
  }
  .mt45{
    margin-top: 45px;
  }
  .g-rule-form{
    margin-top: 32px;
    padding: 0 8px;
  }
}
.m_plan_info{
  ::v-deep .el-card__body{
    padding: 0;
  }
  .g_card_bg{
    width: 100%;
    height: 56px;
    line-height: 56px;
    padding-left: 20px;
    background: #F7F9FF;
    border-radius: 8px 8px 0px 0px;
    .g-plan-title-icon{
      display: flex;
      align-items: center;
      margin-left: 5px;
      .u-txt-icon{
        width: 16px;
        height: 16px;
        background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
      }
      .u-txt-title{
        margin-left: 4px;
        font-size: 16px;
        font-weight: 600;
        color: #31415F;
      }
    }
  }
  .g_plan_info{
    padding: 24px 20px 0;
    .g_plan_info_row{
      display: flex;
      margin-bottom: 24px;
      position: relative;
      .overflow{
        position: absolute;
        right:0;
        top:0;
        font-size: 14px;
        display: flex;
        align-items: center;
        color: $--color-primary;
        cursor: pointer;
      }
      .g_plan_info_rowItem{
        flex: 1;
        .label{
          min-width: 108px;
          font-size: 14px;
          color: #999999;
        }
        .value{
          word-break: break-all;
          color: #333333;
          font-size: 14px;
        }
        .value_all{
          display: flex;
          flex-direction: column;
          .value_tips{
            font-size: 14px;
            color: #6C7B96;
            margin-bottom: 8px;
          }
          .value_val{

            font-size: 14px;
            color: #31415F;
          }
        }


      }
      .label_yh{
        min-width: 108px;
        font-size: 14px;
        color: #6C7B96;
      }
      .value_yh{
        width: 82%;
        font-size: 14px;
        color: #31415F;
        word-break: break-all;
        overflow: hidden;
        text-overflow: ellipsis;
      }
      .value_yh_more{
        width: 100%;
        font-size: 14px;
        color: #31415F;
        position: relative;
        word-break: break-all;

      }
      .sq{
        font-size: 14px;
        color: $--color-primary;
        cursor: pointer;
        right: 0;
        bottom: 0;
      }
      .more{
        position: absolute;
        right: 0;
        font-size: 14px;
        display: flex;
        align-items: center;
        color: $--color-primary;
        cursor: pointer;
      }

    }
    .g_plan_info_row1{
      margin-bottom: 24px;
      .label{
        min-width: 108px;
        font-size: 14px;
        color: #999999;
        margin-bottom: 12px;
      }
      .g_content_box{
        border-radius: 8px;
        border: 1px solid #D8DCE6;
        padding: 20px;
        display: flex;
        overflow-x: auto;
        width: 100%;
        .m-item{
          position: relative;
          min-width: 33.3%;
          padding: 0 20px;
          .m-item-top{
            display: flex;
            justify-content: space-between;
            .img{
              width:13px ;
              height: 13px;
              vertical-align: bottom;
            }
          }
          .m-item-content{
            margin-top: 12px;
            background: #F7F8F9;
            border-radius: 6px;
            padding: 8px 12px 0;
            line-height: 20px;
            font-size: 12px;
            color: #31415F;
            height: 90px;
            .label{
              min-width: 40px;
              color: #9CA8C3;
            }
            .val{
              word-break: break-all;
            }
          }
          &:after{
            content: '';
            position: absolute;
            top: 0;
            right: -20px;
            height: 100%;
            margin: 0 20px;
            border-right: 1px solid #D8DCE6;
          }
          &:last-child{
            padding-right: 0;
            &:after{
              display: none;
            }
          }
          &:first-child{
            padding-left: 0;
          }
        }
      }
    }
  }

}
