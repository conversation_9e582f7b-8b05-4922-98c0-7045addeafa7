/**
* I think element-ui's default theme color is too light for long-term use.
* So I modified the default color and you can modify it to your liking.
**/

/* theme color */
$--color-primary: #005DCF;
$--color-success: #2FC18C;
$--color-warning: #E3B720;
$--color-danger: #F8716B;
$--color-info: #909399;
$--color-error: #F24040;
$--button-font-weight: 400;

// 单选按钮边框色
// $--radio-button-border-color: #CED1D9;
// 默认文字颜色
$--color-text-primary: #333333;
$--color-text-info: #666666;
$--color-text-tip: #999999;
$--color-text-regular: #666666;
/* icon font path, required */
$--font-path: '~element-ui/lib/theme-chalk/fonts';
// 背景色
$--color-background: #F5F7FA;

$menuText:#fff;
$menuActiveText:$--color-text-primary;

$menuBg: $--color-primary;
$menuActiveBg: #F4F6F9 ;
// the :export directive is the magic sauce for webpack
// https://www.bluematador.com/blog/how-to-share-variables-between-js-and-sass
:export {
  menuBg: $menuBg;
  menuText: $menuText;
  menuActiveText: $menuActiveText;
  theme: $--color-primary;
}



