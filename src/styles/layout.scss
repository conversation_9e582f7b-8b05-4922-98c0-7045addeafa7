
.g-pkg{
  flex: 1;
  display: flex;
  overflow: hidden;
  .g-pkg_slide{
    width: 180px;
    z-index:10;
    display: flex;
    flex-direction: column;
    //margin-right: 20px;
    .u-logo {
      height: 80px;
      background-image: url('~@/assets/logo.png');
      background-repeat: no-repeat;
      background-position: center;
      background-size: 160px 30px;
    }
    background: $menuBg;
    // .el-submenu {
      // background-color: $menuBg!important;
      // &.is-active .el-submenu__title {
      //   background-color: $menuActiveBg!important;
      //   color: $menuActiveText !important;
      // }
      // .el-submenu__title {
      //   background-color: $menuBg!important;;
      // }
    // }
    .el-menu-item {
      transition: none;
      &.is-active {
        background-color: $menuActiveBg !important;
        color: $menuActiveText !important;
        z-index: 10;
        &:hover {
          background-color: $menuActiveBg !important;
          color: $menuActiveText !important;
        }
      }
    }
    .el-scrollbar {
      flex: 1;
    }
    .scrollbar-wrapper {
      overflow-x: hidden !important;
    }
    a {
      display: inline-block;
      width: 100%;
    }
    .el-menu[role='menubar'] {
      background: transparent!important;
      border-right: none;
    }

    .svg-icon {
      display: inline-block;
      vertical-align: middle;
      margin-right: 16px;
    }
    .el-submenu__title{
      overflow: hidden;
    }
    .el-submenu__title i{
      color:#fff
    }
  }
  .g-pkg_main{
    overflow: hidden;
    flex: 1;
    display: flex;
    flex-direction: column;
    &--head {  
      .m-top {
        height: 50px;
        background: #FFFFFF;
        box-shadow: 0px 1px 4px 0px rgba(223,223,223,0.5);
        color: $--color-text-primary;
        display: flex;
        align-items: center;
        justify-content: space-between;
        padding: 0 20px;
        .m-title {
          font-size: 20px;
          display: flex;
          align-items: center;
          &__img {
            width: 32px;
            height: 32px;
            margin-right: 20px;
            background-image: url('~@/assets/title.png');
            background-size: contain;
          }
        }
        &__right {
          display: flex;
          align-items: center;
          .u-authority {
            display: flex;
            align-items: center;
            &__divide {
              height: 12px;
              width: 1px;
              background-color: #D8DCE6;
              margin: 0 16px;
            }
            i {
              color: $--color-text-tip;
              cursor: pointer;
              &:hover {
                color: $--color-primary;
              }
            }
          }
          .m-user {
            margin-left: 50px;
            display: flex;
            align-items: center;
            cursor: pointer;
            img {
              width: 32px;
              height: 32px;
              border-radius: 50%;
              user-select: none;
            }
            span {
              margin: 0 20px;
              font-size: 18px;
            }
            i {
              color: $--color-text-tip;
            }
          }
        }
      }
      .m-bread {
        display: inline-block;
        line-height: 40px;
        background-color: #E2E3EB;
        border-top-right-radius: 8px;
        white-space: nowrap;
        &__item {
          display: inline-block;
          font-size: 16px;
          color: #333333;
          padding: 0 12px;
          border-bottom-left-radius: 8px;
          user-select: none;
          cursor: pointer;
          &:hover {
            color: $--color-primary;
          }
          i {
            margin-left: 20px;
            font-weight: 600;
            &:hover {
              color: $--color-danger;
            }
          }
          &.active {
            background-color: #fff;
            border-bottom-left-radius: 0;
            border-top-left-radius: 8px;
            border-top-right-radius: 8px;
            position: relative;
            &:first-child {
              border-top-left-radius: 0px;
            }
            &:last-child::after {
              display: none;
            }
            &:not(first-child)::before {
              content: "";
              display: block;
              height: 8px;
              width: 8px;
              position: absolute;
              bottom: 0;
              background: radial-gradient(8px at 8px 0px, transparent 8px, #ffffff 8px);
              z-index: 1;
              left: -8px;
              transform: rotate(270deg)
            }
            &::after {
              content: "";
              display: block;
              height: 8px;
              width: 8px;
              position: absolute;
              bottom: 0;
              background: radial-gradient(8px at 8px 0px, transparent 8px, #ffffff 8px);
              z-index: 1;
              right: -8px
            }
          }
        }
      }
    }
    &--body {
      flex:1;
      padding: 20px;
      overflow-y: auto;
    }
  }
}

//fix样式兼容问题
.el-dropdown-menu__item{
  white-space: nowrap;
}
