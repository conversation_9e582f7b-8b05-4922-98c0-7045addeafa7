a:focus, a:active {outline: none;}
a, a:focus, a:hover {cursor: pointer;color: inherit;text-decoration: none;}
// div:focus {outline: none;}
th{font-weight: 500;}
.flex{
  display: flex;
}
.fr {float: right;}
.fl {float: left;}
.cl{zoom:1;clear: both;}
.dib{display: inline-block;vertical-align: middle;}
.cup{cursor: pointer;}
.text-center {text-align: center;}
.text-right {text-align: right;}
.text-left {text-align: left;}

.pr-5 {padding-right: 5px;}
.pl-5 {padding-left: 5px;}

.ml20{ margin-left: 20px;}
.mr20{ margin-right: 20px;}

.block {display: block;}
.pointer {cursor: pointer;}

.clearfix {
  &:after {
    visibility: hidden;
    display: block;
    font-size: 0;
    content: " ";
    clear: both;
    height: 0;
  }
}
.row-one{white-space: nowrap;text-overflow: ellipsis;overflow: hidden;word-break: break-all;}

//全局详情页标题样式
.tilte {
  display: flex;
  align-items: center;
  height: 22px;
  line-height: 25px;
  font-weight: 600;
  color: #333333;
  &:before{
    content: "";
    width: 16px;
    height: 16px;
    background: url("~@/assets/images/banner.png");
    background-size: 100%;
  }
}
.pagination-container {
  margin-top: 20px;
}
// 自定义loading样式
.customLoading .el-loading-spinner {
  font-size: 32px;
}
.customLoading .el-loading-spinner .path{
  stroke:#fff
}

.alignCenter {align-items: center;}
.flex1{flex: 1;}
.margin-l-25 {margin-left: 25px}
.margin-l-20 {margin-left: 20px}
.margin-l-140 {margin-left: 140px}
.margin-l-10 {margin-left: 10px}
.margin-b-20 {margin-bottom: 20px;}
.margin-b-30 {margin-bottom: 30px;}
.margin-b-6 {margin-bottom: 6px;}
.margin-b-10 {margin-bottom: 10px;}
.margin-t-20 {margin-top: 20px;}
.margin-t-16 {margin-top: 16px;}
.margin-t-30 {margin-top: 30px;}
.margin-t-10 {margin-top: 10px;}
.margin-t-60 {margin-top: 60px;}
.margin-t-36 {margin-top: 36px;}
.ml32{
  margin-left: 32px;
}
.ml12{
  margin-left: 12px;
}
.line-24 {line-height: 24px;}

.flexSpace{
  display: flex;
  flex-wrap: wrap;
}

.el-date-editor .el-range__close-icon{
  line-height: 25px;
}
.clear {
  overflow: hidden;
}
.f-14{font-size: 14px;}
.f-16{font-size: 16px;}
.f-18{font-size: 18px;}
.f-20{font-size: 20px;}
// 显示一行
.row-one{white-space: nowrap;text-overflow: ellipsis;overflow: hidden;word-break: break-all;}
// 显示两行
.row-two{
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 2;
}
.el-tabs__item{
  height: auto !important;
}

.buleRound{
  width: 14px;
  height: 14px;
  border-radius: 50%;
  background: rgba(43, 111, 239, 1);
  position: relative;
  display:inline-block ;
  margin-right: 4px;
  margin-top: 1px;
  &:after{
    content: '';
    position: absolute;
    width: 8px;
    height: 8px;
    background: #fff;
    border-radius: 50%;
    left: 50%;
    top: 50%;
    margin-top: -4px;
    margin-left: -4px;
  }

}
.margin-r-24{
  margin-right: 24px;
}
.margin-r-20{
  margin-right: 20px;
}
.margin-r-10{
  margin-right: 10px;
}
.margin-r-5{
  margin-right: 5px;
}

.el-upload--picture-card p{
  width: max-content;
  position: absolute;
  bottom: -7px;
}
.cancel-btn{
  width: 100px;
  background: #EDF0FF;
  border-radius: 4px;
  border: 1px solid $--color-primary;
  font-size: 14px;
  color: $--color-primary;
}
.el-picker-panel.no-atTheMoment {
  .el-button--text.el-picker-panel__link-btn {
    display: none;
  }
}
