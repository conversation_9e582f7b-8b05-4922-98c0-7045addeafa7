// cover some element-ui styles

.el-breadcrumb__inner,
.el-breadcrumb__inner a {
  font-weight: 400 !important;
}
.el-upload {
  input[type="file"] {
    display: none !important;
  }
}

.el-upload__input {
  display: none;
}

.cell {
  .el-tag {
    margin-right: 0px;
  }
}

.small-padding {
  .cell {
    padding-left: 5px;
    padding-right: 5px;
  }
}

.fixed-width {
  .el-button--mini {
    padding: 7px 10px;
    width: 60px;
  }
}

.status-col {
  .cell {
    padding: 0 10px;
    text-align: center;

    .el-tag {
      margin-right: 0px;
    }
  }
}

// to fixed https://github.com/ElemeFE/element/issues/2461
.el-dialog {
  border-radius: 12px!important;
  box-shadow: 0px 0px 20px 0px rgba(0,0,0,0.3);
  max-height: 90vh;
  top: 50%!important;
  transform: translate3d(0, -50%, 0) !important;
  margin-top: 0!important;
  display: flex;
  flex-direction: column;
  &__title {
    font-weight: 500;
    font-size: 16px!important;
  }
  &__header {
    text-align: center;
    .el-dialog__close {
      font-weight: 600;
      transition: all ease-in-out .3s;
      &:hover {
        transform: rotate(180deg);
      }
    }
  }
  &__body {
    flex: 1;
    padding: 20px 20px!important;
    overflow-y: auto;
  }
  // 给el-dialog 添加flex-body 类名，就是body按照flex纵轴布局
  &.flex-body &__body {
    display: flex;
    flex-direction: column;
  }
  &__footer {
    text-align: center!important;
  }
}

// refine element ui upload
.upload-container {
  .el-upload {
    width: 100%;

    .el-upload-dragger {
      width: 100%;
      height: 200px;
    }
  }
}

// dropdown
.el-dropdown-menu {
  a {
    display: block
  }
}
.el-range-separator {
  box-sizing: content-box;
}
// 修正卡片的样式
.el-card{
  margin-top: 20px;
  &:first-child{
    margin-top: 0;
  }
}
.el-card__body{
  >.el-form--inline{
    margin-bottom: -20px;
  }
}

.um-search_form--block{
  >.el-form{
    margin-bottom: -20px;
  }
}

.el-breadcrumb + .el-card{
  margin-top: 0;
}

// 自定义搜索样式
.um-search_form{
  position: relative;
  .el-input{
    width: 100%;
  }
  &.close{
    .el-form{
      height: 36px;
      overflow: hidden;
      margin-bottom: 20px;
    }
  }
  .u-more{
    position: absolute;
    right:0;
    top:0;
  }
}

.el-table.el-table--striped {
  border: 1px solid rgba(240,243,245,1);
  border-right: 1px solid rgba(240,243,245,1)!important;
  border-bottom: none;
  border-radius: 8px;
  thead {
    color: $--color-text-primary!important;
    .el-table__cell {
      padding: 20px 0!important;
    }
  }
  tr {
    background: $--color-background!important;
  }
  .el-table__body tr.el-table__row--striped td.el-table__cell {
    background: #FFF!important;
  }

  .el-table__body tr.el-table__row--striped:hover td.el-table__cell {
    background: $--color-background!important;
  }
  // 针对fixed 列
  .el-table__body tr.hover-row > td.el-table__cell,.el-table__body tr.hover-row.current-row > td.el-table__cell, .el-table__body tr.hover-row.selection-row >td.el table__cell, .el_table__body tr.hover-row.el-table__row--striped > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.current-row > td.el-table__cell, .el-table__body tr.hover-row.el-table__row--striped.selection-row > td.el-table__cell {
    background: $--color-background!important;
  }
  th.el-table__cell.is-leaf,  td.el-table__cell {
    border-bottom: none!important
  }
  .el-button--text{
    margin-left:  0;
    margin-right: 14px;
    &+.el-button--text{
      margin-left:  0;
    }
  }
}
// 表单的label * 修改
.el-form-item.is-required:not(.is-no-asterisk) > .el-form-item__label:before, 
.el-form-item.is-required:not(.is-no-asterisk) .el-form-item__label-wrap > .el-form-item__label:before {
  position: absolute;
  left: -8px;
  top: 2px;
}
.el-checkbox__label, .el-radio__label {
  font-weight: normal!important;
}
.el-form-item {
  margin-bottom: 20px!important;
}
.el-form-item__label {
  position: relative;
  padding: 0!important;
  text-align: left!important;
  line-height: 32px!important;
  color: #333!important;
}
.detailForm {
  .el-form-item__label{
    color: #999!important
  }
  .el-form-item__content{
    color: #333!important;
    line-height: 32px!important;
  }
}
.el-message-box__title, .el-message-box__headerbtn {
  line-height: 26px!important;
}
.el-pagination {
  display: flex;
  font-weight: 400!important;
  color: $--color-text-regular!important;
  .el-pagination__sizes {
    flex: 1;
  }
  .number.active {
    font-weight: 500!important;
  }
}
.el-input.el-input--mini .el-input__inner, .el-input.el-pagination__editor.el-input .el-input__inner {
  line-height: 28px!important;
  height: 28px!important;
}
.el-form-item__content {
  line-height: 28px!important;
}
.el-input__inner, .el-cascader, .el-input__icon {
  line-height: 32px!important;
  height: 32px!important;
}
.el-button, .el-radio-button__inner {
  padding-top: 8px!important;
  padding-bottom: 8px!important;
}
.el-date-editor .el-range__icon, .el-date-editor .el-range-separator {
  line-height: 26px!important;
}
.el-input-number {
  line-height: 32px!important;
}


