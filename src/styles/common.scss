//浮动相关
.f-fl{
  float: left;
}
.f-fr{
  float: right;
}
//flex布局相关
.f-flex{
  display: flex;
}
.f-flex1{
  flex: 1;
}
.f-flex-shrink0{
  flex-shrink:0 ;
}
.f-flex-jcsb{
  display: flex;
  justify-content: space-between;
}
.f-flex-ac{
  display: flex;
  align-items: center;
}
.f-flex-ajc{
  display: flex;
  align-items: center;
  justify-content: center;
}
.f-flex-je{
  display: flex;
  justify-content: flex-end;
}
.f-flex-acjcsb{
  display: flex;
  align-items: center;
  justify-content: space-between;
}
.f-flex-dc{
  display: flex;
  flex-direction: column;
}
.f-flex-djc{
  display: flex;
  flex-direction: column;
  justify-content: space-between;
}
.f-flex-warp{
  flex-wrap:wrap
}
// 盒子相关
.f-block{
  display: block;
}
.f-bnone{
  display:none;
}
.f-inline{
  display: inline-block;
}
.f-ver-m{
  vertical-align: middle;
}
//小手
.f-cursor{
  cursor: pointer;
}
//文字对齐方式
.f-tac{
  text-align: center;
}
.f-tal{
  text-align: left;
}
.f-tar{
  text-align: right;
}
//常用间距margin及padding
.f-pd0{
  padding: 0;
}
.f-pd-20{
  padding: 20px;
}
.f-pd-l20{
  padding-left: 20px;
}
.f-pd-b100{
  padding-bottom: 100px;
}
.f-pd-b20{
   padding-bottom: 20px;
 }
.f-mg-20{
  margin: 20px;
}

.f-mg-t20{
  margin-top: 20px;
}

.f-mg-t30{
  margin-top: 30px;
}
.f-mg-t40{
  margin-top: 40px;
}
.f-mg-t15{
  margin-top: 15px;
}
.f-mg-lr8{
  margin: 0 8px;
}
.f-mg-r8{
  margin-right: 8px;
}
.f-mg-r10{
  margin-right: 10px;
}
.f-mg-r12{
  margin-right: 12px;
}
.f-mg-r16{
  margin-right: 16px;
}
.f-mg-r20{
  margin-right: 20px;
}
.f-mg-r30{
  margin-right: 30px;
}
.f-mg-l16{
  margin-left: 16px;
}
.f-mg-l10{
  margin-left: 10px;
}
.f-mg-l20{
  margin-left: 20px;
}
.f-mg-b10{
  margin-bottom: 10px;
}
.f-mg-b12{
  margin-bottom: 12px;
}
.f-mg-b20{
  margin-bottom: 20px !important;
}
.f-mg-t24{
  margin-top: 24px !important;
}
.f-mg-b0{
  margin-bottom: 0px !important;
}
.f-mg-bt20{
  margin-top: 20px;
  margin-bottom: 20px;
}
.f-mg-lr20{
  margin: 0 20px;
}
.f-mg-lr12{
  margin: 0 12px;
}
.f-mg-l8{
  margin-left: 8px;
}
.f-mg-l12{
  margin-left: 12px !important;
}
.f-mg-l16{
  margin-left: 16px;
}
.f-mg-l20{
  margin-left: 20px;
}
.f-mg-l30{
  margin-left: 30px;
}
.f-mg-l40{
  margin-left: 40px;
}
.f-mg-l100{
  margin-left: 100px;
}
.f-mg-r60{
  margin-right: 60px;
}

.el-tooltip__popper{
  max-width: 800px;
}
.flex-1 {
  flex: 1;
}
.flex-col {
  display: flex;
  flex-direction: column;
}
.auto-y {
  overflow-y: auto;
}