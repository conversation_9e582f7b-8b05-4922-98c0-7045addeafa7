// 时间单位 2000020
export const TIME_UNIT = {
  MINUTE: 2000021, // 分钟
  HOUR: 2000022, // 小时
  DAY: 2000023, // 天
  MONTH: 2000024 // 月
}

// 时间方位 2000030
export const TIME_DIRECRION = {
  BEFORE: 2000031, // 前
  AFTER: 2000032 // 后
}

// 问卷模版状态
export const QUES_TEMPLATE_STATUS = {
  DRAFT: 2200091, // 草稿
  PUBLISH: 2200092, // 已发布
  DUTY: 2200093 // 已作废
}

// 触点规则类型
export const POINT_RULE = {
  EVENT: 2200041, // 事件触点
  POINT: 2200042, // 节点触点
  SPECIAL: 2200043, // 专项调研
  DEEP: 2200044 // 深访调研
}

// OA申请状态
export const OA_STATUS = {
  DRAFT: 2200251, // 草稿
  APPROVAL: 2200252, // 审批中
  PASS: 2200253, // 审批通过
  NO_PASS: 2200254, // 审批不通过
  RETURN: 2200255 // 驳回
}

// 问卷考核类型
export const EXAMINE_STATUS = {
  YES: 2200301, // 考核
  NO: 2200302 // 不考核
}

// 装修标准
export const DECORATION_STATUS = {
  SIMPLE: 2200031, // 毛坯
  SIMPLE_OUT: '01', // 毛坯【特殊：对外使用，后台接口需要】
  FINE: 2200032, // 精装
  FINE_OUT: '02' // 精装【特殊：对外使用，后台接口需要】
}

// 回收方式（数据来源）
export const DATA_SOURCES = {
  ONLINE: 2200161, // 线上评价
  PHONE: 2200162 // 电话回访
}

// 指标 、 触点/节点 枚举
export const TARGET_POINT = {
  POINT: 2300052, // 触点/节点
  TARGET: 2300051 // 指标
}

// 满意度年度目标状态
export const YEAR_TARGET_STATUS = {
  DRAFT: 2300021, // 草稿
  PUBLISH: 2300022, // 已发布
  DUTY: 2300023 // 已作废
}

// 特殊事项申请范围
export const SPECIAL_APPLY_RANGE = {
  ALL: 2200271, // 整个项目
  APPOINT: 2200272 // 指定房间
}

// 特殊事项申请类型
export const SPECIAL_APPLY_TYPE = {
  NO_SURVEY: 2200261, // 不调研
  ONLY_WATCH: 2200262, // 仅监控不考核
  DELAY: 2200263 // 延期调研
}

// 数据字典枚举，用户调后端接口查询
export const DICT_CODE = {
  REVERSE_APPLY_REASON: 2100021, // 反向抽查处置原因
  SPECIAL_APPLY_REASON: 2100022, // 特殊事项申请原因
  POINT_TYPE: 2100023, // 触点类型
  QUESTION_TYPE: 2100024
}

// 后台系统中定义的字典值
export const SYS_DICT_CODE = {
  BUSINESS: 2000050, // 业务条线
  OPERATE: 2200310 // 操盘条线
}

// 调研计划状态
export const PLAN_STATUS = {
  UN_PUBLISH: 2200141, // 待发布
  IN_PROGRESS: 2200142, // 调研中
  COMPLANT: 2200143, // 已完成
  IN_AUDIT: 2200252, // 审核中【深访】
  PASS: 2200253, // 审核通过【深访】
  NO_PASS: 2200254, // 审核不通过【深访】
  RETURN: 2200255 // 驳回【深访】
}

// 调研范围
export const PLAN_RANGE = {
  ALL: 2200131, // 全部
  PROJECT: 2200132, // 指定项目
  CUSTOM: 2200133 // 指定客户
}

// 考核类型
export const EXAMINE_TYPE = {
  MONTH: 2200151, // 月度
  QUARTER: 2200152, // 季度
  HALF_YEAR: 2200153, // 半年度
  YEAR: 2200154, // 年度
  NO: EXAMINE_STATUS.NO
}

// 推送渠道
export const CHANNEL_TYPE = {
  MSG: 2200051, // 短信
  PUBLIC: 2200052, // 公众号
  MINI: 2200053 // 小程序
}

// 专项调研发布规则
export const SPECIAL_PUSH_RULE = {
  PUBLISH: 2200181, // 发布时
  POINT: 2200182 // 指定时间
}

// 对象类型
export const CUSTOMER_TYPE = {
  OWNER: 2200021, // 业主
  OWNER_OUT: 10, // 业主【特殊：对外使用，后台接口需要】
  TOGETHER: 2200022, // 同住人
  TOGETHER_OUT: 20, // 同住人【特殊：对外使用，后台接口需要】
  TENANT: 2200023, // 租户
  TENANT_OUT: 30 // 租户【特殊：对外使用，后台接口需要】
}

// 月份类型
export const MONTH_TYPE = {
  BEFORE_MONTH: 2000042, // 上月
  CURRENT_MONTH: 2000041, // 本月
  AFTER_MONTH: 2000043 // 次月
}

// 所属阶段
export const CUSTOMER_LEVEL_TYPE = {
  READY: 2200011, // 准业主
  RUNIN: 2200012, // 磨合期
  STABLE: 2200013, // 稳定期
  OLD: 2200014 // 老业主
}
// 所属阶段下拉
export const CUSTOMER_LEVEL_LIST = [ // 所属阶段
  {
    label: '准业主',
    value: CUSTOMER_LEVEL_TYPE.READY
  },
  {
    label: '磨合期',
    value: CUSTOMER_LEVEL_TYPE.RUNIN
  },
  {
    label: '稳定期',
    value: CUSTOMER_LEVEL_TYPE.STABLE
  },
  {
    label: '老业主',
    value: CUSTOMER_LEVEL_TYPE.OLD
  }
]

// 节点触点次数限制 2200210
export const POINT_LIMIT = {
  NONE: 2200211, // 不限次数
  ONECE: 2200212, // 仅调研一次
  YEAR: 2200213 // 每年一次
}

// 调研时间日期类型
export const WATCH_DATE_TYPE = {
  NET: 2200331, // 网签日期
  CONTRACT: 2200332, // 合同约定交付日期
  CENTER: 2200333, // 集中交付结束日期
  REAL: 2200334 // 实际收楼日期
}

export const MATH_MODEL = {
  LEVEL: 2300071, // 分级计算
  DIRECT: 2300072 // 直接计算
}

export const HOUSE_TYPE = {
  BUILDING: 2200281, // 楼栋
  UNIT: 2200282, // 单元
  ROOM: 2200283 // 房间
}

// 问卷逻辑设置前置条件
export const BEFORE_CONDIONS = [
  {
    title: '是否有会所',
    key: 'hasClub',
    options: [
      {
        label: '有',
        value: 1
      },
      {
        label: '无',
        value: 0
      }
    ]
  },
  {
    title: '是否华发操盘物业',
    key: 'isHfOperate',
    options: [
      {
        label: '是',
        value: 1
      },
      {
        label: '否',
        value: 0
      }
    ]
  },
  {
    title: '房屋装修类型',
    key: 'decorationType',
    options: [
      {
        label: '毛坯',
        value: 2200031
      },
      {
        label: '精装',
        value: 2200032
      }
    ]
  }
]

// 是否有会所 club

// 房屋装修类型 decoration

// 是否华发操盘物业 decoration
