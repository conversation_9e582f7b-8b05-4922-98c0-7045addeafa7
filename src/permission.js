import router from './router'
import store from './store'
import { Message } from 'element-ui'
import NProgress from 'nprogress' // progress bar
import 'nprogress/nprogress.css' // NProgress Configuration
import { getToken, setToken } from './utils/auth' // progress bar style
NProgress.configure({ showSpinner: false })
const whitePages = ['/404', '/oa/special/reverse', '/oa/special/apply']
router.beforeEach(async(to, from, next) => {
  if (whitePages.includes(to.path)) {
    next()
    return
  }
  const token = to.query.accessToken || getToken()
  const msg = to.query.msg

  NProgress.start()
  if (msg) {
    next({ path: '/error', query: { message: msg }, replace: true })
    return
  }
  if (!token) {
    // hash模式
    // window.location.href = process.env.VUE_APP_AUTH_API + '/api/identity/pc/sso/login?redirectUrl=' + encodeURIComponent(window.location.origin + '/pc/#/')
    // history模式
    window.location.href = process.env.VUE_APP_AUTH_API + '/api/identity/pc/sso/login?redirectUrl=' + window.location.origin + '/pc'
    return
  }
  setToken(token)
  store.commit('user/SET_TOKEN', token)
  const { name } = to
  if (name) {
    store.dispatch('tagsView/addView', to)
  }

  const hasRoles = store.getters.roles && store.getters.roles.length > 0
  if (hasRoles) {
    next()
  } else {
    try {
      await store.dispatch('user/getInfo')
      if (!store.getters.roles.length) {
        next({ path: '/404', replace: true })
        return
      }
      const accessRoutes = await store.dispatch('permission/generateRoutes')
      router.addRoutes(accessRoutes)

      next({ ...to, replace: true })
    } catch (error) {
      // await store.dispatch('user/resetToken')
      Message.error(error || 'Has Error')
      // next(`/login?redirect=${to.fullPath}`)
      NProgress.done()
    }
  }
})

router.afterEach((...arg) => {
  NProgress.done()
})
