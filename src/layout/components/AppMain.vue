<template>
  <section class="g-pkg_main--body">
    <keep-alive :include="cachedViews" :max="2">
      <router-view :key="key" />
    </keep-alive>
  </section>
</template>

<script>
import { mapGetters } from 'vuex'
export default {
  name: 'AppMain',
  data() {
    return {
    }
  },
  computed: {
    ...mapGetters(['cachedViews']),
    key() {
      return this.$route.path
    }
  },
  created() {
  },
  methods: {
  }
}
</script>
