<template>
  <div class="g-pkg_slide">
    <div class="u-logo" />
    <el-scrollbar wrap-class="scrollbar-wrapper">
      <el-menu
        ref="elMenu"
        :default-active="activeMenu"
        :collapse="false"
        :background-color="variables.menuBg"
        :text-color="variables.menuText"
        :unique-opened="true"
        :active-text-color="variables.menuActiveText"
        :collapse-transition="false"
        mode="vertical"
      >
        <sidebar-item
          v-for="route in permission_routes"
          :key="route.path"
          :item="route"
          :base-path="route.path"
        />
      </el-menu>
    </el-scrollbar>
  </div>
</template>

<script>
import SidebarItem from './SidebarItem'
import { mapGetters, mapMutations } from 'vuex'
import variables from '@/styles/element-variables.scss'
export default {
  name: 'Sidebar',
  components: { SidebarItem },
  data() {
    return {
      variables
    }
  },
  computed: {
    ...mapGetters(['permission_routes', 'computedActiveRoute']),
    activeMenu() {
      const route = this.$route
      const { meta, path } = route
      if (meta.activeMenu) {
        return meta.activeMenu
      }
      return path
    }
  },
  watch: {
    // 场景： 比如问卷编辑页面，用户阻止了返回上一页，则需要将当前选中的路由根据浏览器地址路由重新设置一遍
    computedActiveRoute(v) {
      if (v) {
        const route = this.$route
        const { meta, path } = route
        if (meta.activeMenu) {
          this.$refs.elMenu.activeIndex = meta.activeMenu
        } else {
          this.$refs.elMenu.activeIndex = path
        }
        this['globalData/SET_COMPUTED_ACTIVE_ROUTE'](false)
      }
    }
  },
  methods: {
    ...mapMutations(['globalData/SET_COMPUTED_ACTIVE_ROUTE'])
  }
}
</script>
