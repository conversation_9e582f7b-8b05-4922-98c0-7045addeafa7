<script>
export default {
  name: 'MenuItem',
  functional: true,
  props: {
    icon: {
      type: String,
      default: ''
    },
    title: {
      type: String,
      default: ''
    }
  },
  render(h, context) {
    const { icon, title } = context.props
    const vnodes = []

    if (icon) {
      vnodes.push(<svg-icon icon-class={icon}/>)
    } else {
      vnodes.push(<svg-icon icon-class={icon} style='width:11px;margin:0'/>)
    }

    if (title) {
      vnodes.push(<span slot='title' style='position:relative;z-index:20;'>{(title)}</span>)
    }
    return vnodes
  }
}
</script>
