<template>
  <div class="g-pkg_main--head">
    <div class="m-top">
      <div class="m-title">
        <div class="m-title__img" />
        <span>华发股份满意度评价管理系统</span>
      </div>
      <div class="m-top__right">
        <div class="u-authority">
          <el-select :value="userInfo.positionId" placeholder="请选择" @change="changePosition">
            <el-option
              v-for="item in positions"
              :key="item.positionId"
              :label="item.positionName"
              :value="item.positionId"
              :disabled="item.disabled"
            />
          </el-select>
          <div class="u-authority__divide" />
          <el-popover
            v-model="showExportPop"
            placement="bottom-start"
            width="500"
            trigger="hover"
          >
            <el-badge slot="reference" :value="exportList.filter(item => !item.success).length" type="primary" :max="99" :hidden="!exportList.filter(item => !item.success).length">
              <i id="targetPosi" class="el-icon-lightning" />
            </el-badge>
            <el-table v-loading="exportLoading" stripe :data="exportList" height="300">
              <el-table-column label="内容" prop="fileName" show-overflow-tooltip />
              <el-table-column width="165" label="创建时间" prop="startTime" />
              <el-table-column width="65" label="操作">
                <template slot-scope="{ row }">
                  <el-button v-if="row.success" type="text" @click="downloadFile(row)">下载</el-button>
                  <span v-else>打包中...</span>
                </template>
              </el-table-column>
            </el-table>
          </el-popover>
        </div>
        <el-popover
          placement="bottom-end"
          trigger="hover"
          popper-class="logout-popper"
        >
          <div class="u-top__logout" @click="logout">退出登录</div>
          <div slot="reference" class="m-user">
            <img src="@/assets/avatar.svg" alt="">
            <span>{{ userInfo.userName }}</span>
            <i class="el-icon-caret-bottom" />
          </div>
        </el-popover>
      </div>
    </div>
    <el-scrollbar ref="scrollBar">
      <div class="m-bread">
        <div
          v-for="item in visitedViews"
          :key="item.fullPath"
          class="m-bread__item"
          :class="{ active: item.path === path }"
          @click="$router.push({
            path: item.path,
            query: item.query
          })"
        >
          <span>{{ item.title }}</span>
          <i v-if="item.fullPath !== '/'" class="el-icon-close" @click.stop="del(item, item.path === path)" />
        </div>
      </div>
    </el-scrollbar>
  </div>
</template>

<script>
import { mapGetters, mapActions, mapMutations } from 'vuex'
import { throttle } from 'lodash'
import { logout } from '@/api/common'
let timer = null
export default {
  name: 'NavBar',
  data() {
    return {
      path: null,
      positionId: null
    }
  },
  computed: {
    ...mapGetters(['userInfo', 'positions', 'visitedViews', 'exportList', 'exportShow', 'exportLoading']),
    showExportPop: {
      get() {
        return this.exportShow
      },
      set(v) {
        this['globalData/SET_EXPORT_SHOW'](v)
      }
    }
  },
  watch: {
    '$route': {
      immediate: true,
      handler(v) { // 监听路由变化，自动滚动当前面包屑到中间
        this.path = v.path
        this.$nextTick(() => {
          if (this.$refs.scrollBar) {
            const activeBread = document.querySelector('.m-bread__item.active')
            const scrollBar = this.$refs.scrollBar
            const diff = activeBread.offsetLeft + activeBread.offsetWidth / 2 - scrollBar.$el.offsetWidth / 2
            this.transition(diff, this.$refs.scrollBar.wrap.scrollLeft, 300, value => {
              this.$refs.scrollBar.wrap.scrollLeft = value
            })
          }
        })
      }
    },
    visitedViews() {
      this.$nextTick(() => {
        this.$refs.scrollBar.update()
      })
    },
    exportShow(v) {
      if (v) {
        this.getExportData()
        timer = setInterval(() => {
          this['globalData/GET_EXPORT_LIST']()
        }, 5000)
      } else {
        clearInterval(timer)
      }
    }
  },
  created() {
    this['globalData/GET_EXPORT_LIST']()
  },
  beforeDestroy() {
    clearInterval(timer)
  },
  methods: {
    ...mapActions(['tagsView/delVisitedView', 'tagsView/delCachedView', 'user/resetToken', 'globalData/GET_EXPORT_LIST']),
    ...mapMutations(['user/SET_USERINFO', 'tagsView/DEL_ALL_VISITED_VIEWS', 'tagsView/DEL_ALL_CACHED_VIEWS', 'globalData/SET_EXPORT_SHOW', 'globalData/SET_IS_ASYNC_CLOSE_VIEW']),
    getExportData: throttle(function() {
      this['globalData/GET_EXPORT_LIST']()
    }, 5000),
    async del(view, isActive = false) {
      // 问卷编辑页面，返回会有拦截提示，所以会在拦截的回调中判断是否需要删除
      if (view.path.indexOf('/wenjuan/edit') !== -1) {
        this['globalData/SET_IS_ASYNC_CLOSE_VIEW'](true)
        const arr = this.visitedViews.filter(item => item.path.indexOf('/wenjuan/edit') === -1)
        if (!arr.length) {
          this.$router.replace('/')
          return
        }
        const route = arr[arr.length - 1]
        this.$router.replace({
          path: route.path,
          query: route.query
        })
        return false
      }
      try {
        const arr = await this['tagsView/delVisitedView'](view)
        await this['tagsView/delCachedView'](view)
        if (!isActive) return // 如果删除的不是当前页面路由，则表述还有历史记录存在，不用处理
        if (!arr.length) {
          this.$router.replace('/')
          return
        }
        const route = arr[arr.length - 1]
        this.$router.replace({
          path: route.path,
          query: route.query
        })
      } catch (error) {

      }
    },
    changePosition(value) {
      this['user/SET_USERINFO']({
        positionId: value
      })
      window.location.href = process.env.VUE_APP_AUTH_API + '/api/identity/pc/sso/login?redirectUrl=' + window.location.origin + '/pc'
    },
    transition(start, end, time, change) {
      const diff = start - end
      // 频率
      const p = 10
      // 执行次数(毫秒)， 常量
      const n = time / p
      // 执行次数变量
      const n_i = 0
      const loop = (n_i) => {
        // 进度
        const r = 1 / n * n_i
        const next_value = start - (diff * (1 - r))
        change(next_value)
        if (n_i < n) {
          setTimeout(() => {
            requestAnimationFrame(loop.bind(this, n_i + 1))
          }, p)
        }
      }
      loop(n_i)
    },
    downloadFile(data) {
      window.open(data.httpPath)
    },
    logout() {
      this.$confirm('您确定要退出登录吗？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async() => {
        const load = this.$load()
        try {
          await logout()
          await this['user/resetToken']()
          window.location.reload()
        } catch (error) {

        } finally {
          load.close()
        }
      }).catch(() => {
      })
    }
  }
}
</script>

<style lang="scss">
.logout-popper {
  padding: 2px 0;
  min-width: 100px;
}
.u-top__logout {
  text-align: center;
  cursor: pointer;
  line-height: 36px;
  &:hover {
    background-color: #ecf5ff;
    color: $--color-primary;
  }
}
</style>

<style lang="scss" scoped>
::v-deep {
  .el-badge__content.is-fixed {
    right: 5px;
    display: flex;
    align-items: center;
  }
}
</style>
