import request from '@/utils/request'

// 【查询】查询字典列表
export function getDictList(data) {
  return request({
    url: '/api/base/pc/config/dict/getDictList',
    method: 'post',
    data
  })
}
// 【新增】新增字典数据
export function addDict(data) {
  return request({
    url: '/api/base/pc/config/dict/add',
    method: 'post',
    data
  })
}
// 【编辑】编辑字典数据
export function editDict(data) {
  return request({
    url: '/api/base/pc/config/dict/edit',
    method: 'post',
    data
  })
}
