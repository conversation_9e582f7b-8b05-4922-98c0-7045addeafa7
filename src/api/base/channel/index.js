import request from '@/utils/request'

// 【查询】渠道列表
export function getChannelList(params) {
  return request({
    url: '/api/ques/pc/channel/list',
    method: 'get',
    params
  })
}

// 【修改】禁用
export function disableChannel(data) {
  return request({
    url: '/api/ques/pc/channel/disable?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】启用
export function enableChannel(data) {
  return request({
    url: '/api/ques/pc/channel/enable?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【查询】渠道列表
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/channel/detail',
    method: 'get',
    params
  })
}

// 【修改】编辑
export function updateChannel(data) {
  return request({
    url: '/api/ques/pc/channel/update',
    method: 'post',
    data
  })
}
