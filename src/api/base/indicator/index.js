import request from '@/utils/request'

// 【查询】获取指标树
export function getTargetTree(params) {
  return request({
    url: '/api/ques/pc/target/getTargetTree',
    method: 'get',
    params
  })
}

// 【通用查询】二级指标列表
export function getTargetList(data) {
  return request({
    url: '/api/ques/pc/target/getTargetList',
    method: 'post',
    noCache: true,
    data
  })
}

// 【新增】满意度指标
export function addTarget(data) {
  return request({
    url: '/api/ques/pc/target/addTarget',
    method: 'post',
    data
  })
}

// 【编辑】满意度指标
export function editTarget(data) {
  return request({
    url: '/api/ques/pc/target/editTarget',
    method: 'post',
    data
  })
}

// 【删除】满意度指标
export function deleteTarget(params) {
  return request({
    url: '/api/ques/pc/target/deleteTarget',
    method: 'get',
    params
  })
}

// 【查询】满意度指标原因分页
export function getTargetReasonPage(data) {
  return request({
    url: '/api/ques/pc/target/getTargetReasonPage',
    method: 'post',
    data
  })
}

// 【编辑】开启或关闭其他项
export function changeOtherFlag(data) {
  return request({
    url: '/api/ques/pc/target/changeOtherFlag',
    method: 'post',
    data
  })
}

// 【删除】满意度指标原因
export function deleteTargetReason(params) {
  return request({
    url: '/api/ques/pc/target/deleteTargetReason',
    method: 'get',
    params
  })
}

// 【新增】满意度指标原因
export function addTargetReason(data) {
  return request({
    url: '/api/ques/pc/target/addTargetReason',
    method: 'post',
    data
  })
}

// 【编辑】满意度指标原因
export function editTargetReason(data) {
  return request({
    url: '/api/ques/pc/target/editTargetReason',
    method: 'post',
    data
  })
}

// 【编辑】移动满意度指标
export function updateTargetSort(data) {
  return request({
    url: '/api/ques/pc/target/updateTargetSort',
    method: 'post',
    data
  })
}

// 【新增】指标原因分类
export function addCategory(data) {
  return request({
    url: '/api/ques/pc/target/addCategory',
    method: 'post',
    data
  })
}

// 【编辑】指标原因分类
export function editCategory(data) {
  return request({
    url: '/api/ques/pc/target/editCategory',
    method: 'post',
    data
  })
}

// 【查询】满意度指标原因
export function getCategoryPage(data) {
  return request({
    url: '/api/ques/pc/target/getCategoryPage',
    method: 'post',
    data
  })
}

// 【删除】满意度指标原因
export function deleteCategory(params) {
  return request({
    url: '/api/ques/pc/target/deleteCategory',
    method: 'get',
    params
  })
}

// 【通用查询】指标分类列表
export function getCategoryList(params) {
  return request({
    url: '/api/ques/pc/target/getCategoryList',
    method: 'get',
    params
  })
}

