import request from '@/utils/request'
const baseURL = 'http://api.pm.youmatech.com/mock/1890'

// 【查询】触点权重列表
export function getPointWeightPage(data) {
  return request({
    url: '/api/report/pc/config/getPointWeightPage',
    method: 'post',
    data,
    baseURL
  })
}

// 【新增查询】获取触点集合
export function getPointList(params) {
  return request({
    url: '/api/report/pc/config/getPointList',
    method: 'get',
    params,
    baseURL
  })
}

// 【新增】触点权重
export function addPointWeight(data) {
  return request({
    url: '/api/report/pc/config/addPointWeight',
    method: 'post',
    data,
    baseURL
  })
}

// 【编辑查询】编辑回显触点权重配置-详情
export function getPointWeightDetails(params) {
  return request({
    url: '/api/report/pc/config/getPointWeightDetails',
    method: 'get',
    params,
    baseURL
  })
}

// 【编辑】编辑触点权重配置
export function editPointWeight(data) {
  return request({
    url: '/api/report/pc/config/editPointWeight',
    method: 'post',
    data,
    baseURL
  })
}

// 【编辑】变更单据状态
export function changeStatusCode(params) {
  return request({
    url: '/api/report/pc/config/changeStatusCode',
    method: 'get',
    params,
    baseURL
  })
}
