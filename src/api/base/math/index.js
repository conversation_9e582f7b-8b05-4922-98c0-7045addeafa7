import request from '@/utils/request'

// 【查询】算法配置列表
export function getAlgorithmPointPage(data) {
  return request({
    url: '/api/report/pc/config/getAlgorithmPointPage',
    method: 'post',
    data
  })
}

// 【查询】算法配置列表
export function changeStatusCode(params) {
  return request({
    url: '/api/report/pc/config/changeStatusCode',
    method: 'get',
    params
  })
}

// 【新增】算法配置
export function addAlgorithmPoint(data) {
  return request({
    url: '/api/report/pc/config/addAlgorithmPoint',
    method: 'post',
    data
  })
}

// 【编辑】算法配置
export function editAlgorithmPoint(data) {
  return request({
    url: '/api/report/pc/config/editAlgorithmPoint',
    method: 'post',
    data
  })
}

// 【查询】触点权重项列表
export function getAlgorithmPointInfo(params) {
  return request({
    url: '/api/report/pc/config/getAlgorithmPointInfo',
    method: 'get',
    params
  })
}

// 【编辑】触点权重项
export function editAlgorithmPointItem(data) {
  return request({
    url: '/api/report/pc/config/editAlgorithmPointItem',
    method: 'post',
    data
  })
}
