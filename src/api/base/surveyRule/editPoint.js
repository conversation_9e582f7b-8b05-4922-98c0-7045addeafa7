import request from '@/utils/request'
const baseURL = 'https://api.pm.youmatech.com/mock/1886'
// 【修改】触点调研编辑
export function addNode(data) {
  return request({
    url: '/api/ques/pc/point/addNode',
    method: 'post',
    data
    // baseURL
  })
}

// 【修改】节点调研编辑
export function updateNode(data) {
  return request({
    url: '/api/ques/pc/point/updateNode',
    method: 'post',
    data
  })
}

// 【修改】节点调研编辑
export function getNodeDetail(params) {
  return request({
    url: '/api/ques/pc/point/nodeDetail',
    method: 'get',
    params
  })
}

