import request from '@/utils/request'
// 【查询】获取指标树
export function getPointList(params) {
  return request({
    url: '/api/ques/pc/point/list',
    method: 'get',
    params
  })
}

// 【修改】上移/下移
export function sortPoint(data) {
  return request({
    url: '/api/ques/pc/point/sort?uuids=' + data.uuids,
    method: 'post',
    data
  })
}

// 【修改】启用
export function setPointEnable(data) {
  return request({
    url: '/api/ques/pc/point/enable?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】禁用
export function setPointDisable(data) {
  return request({
    url: '/api/ques/pc/point/disable?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

