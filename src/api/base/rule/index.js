import request from '@/utils/request'

// 【查询】推送时间限制配置
export function getSendTimeRule(params) {
  return request({
    url: '/api/base/pc/config/getSendTimeRule',
    method: 'get',
    params
  })
}
// 【编辑】推送时间限制配置
export function editSendTimeRule(data) {
  return request({
    url: '/api/base/pc/config/editSendTimeRule',
    method: 'post',
    data
  })
}

// 【查询】绩效转换系数配置
export function getPerformanceConversionRule(params) {
  return request({
    url: '/api/base/pc/config/getPerformanceConversionRule',
    method: 'get',
    params
  })
}

// 【编辑】绩效转换系数配置
export function editPerformanceConversionRule(data) {
  return request({
    url: '/api/base/pc/config/editPerformanceConversionRule',
    method: 'post',
    data
  })
}

// 【查询】节假日配置
export function getHolidaysRule(params) {
  return request({
    url: '/api/base/pc/config/getHolidaysRule',
    method: 'get',
    params
  })
}

// 【删除】节假日配置
export function deleteHolidaysRule(params) {
  return request({
    url: '/api/base/pc/config/deleteHolidaysRule',
    method: 'get',
    params
  })
}

// 【新增】节假日配置
export function addHolidaysRule(params) {
  return request({
    url: '/api/base/pc/config/addHolidaysRule',
    method: 'get',
    params
  })
}

// 【查询】回访周期配置
export function getReturnRule(data) {
  return request({
    url: '/api/base/pc/config/getReturnRule',
    method: 'post',
    data
  })
}

// 【编辑】回访周期配置
export function editReturnRule(data) {
  return request({
    url: '/api/base/pc/config/editReturnRule',
    method: 'post',
    data
  })
}

// 【新增】特殊周期设置
export function addSpecialRule(data) {
  return request({
    url: '/api/base/pc/config/addSpecialRule',
    method: 'post',
    data
  })
}

// 【编辑】特殊周期设置
export function editSpecialRule(data) {
  return request({
    url: '/api/base/pc/config/editSpecialRule',
    method: 'post',
    data
  })
}

// 【删除】特殊周期设置
export function deleteSpecialRule(params) {
  return request({
    url: '/api/base/pc/config/deleteSpecialRule',
    method: 'get',
    params
  })
}

// 【新增或更新】深访配置
export function eidtDepthPoint(data) {
  return request({
    url: '/api/ques/pc/point/depth',
    method: 'post',
    data
  })
}

// 深访详情
export function getDepthPoint(params) {
  return request({
    url: '/api/ques/pc/point/depth',
    method: 'get',
    params
  })
}
