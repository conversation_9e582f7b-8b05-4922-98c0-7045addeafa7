import request from '@/utils/request'
// 【查询】分页问卷管理
export function getTemplatePage(data) {
  return request({
    url: '/api/ques/pc/quesTemplate/getTemplatePage',
    method: 'post',
    data
  })
}

// 【删除】问卷管理删除
export function deleteQuesTemplate(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/deleteQuesTemplate',
    method: 'get',
    params
  })
}

// 【作废】问卷管理作废
export function voidedQuesTemplate(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/voidedQuesTemplate',
    method: 'get',
    params
  })
}

// 【复制】问卷管理复制
export function copyQuesTemplate(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/copyQuesTemplate',
    method: 'get',
    params
  })
}

// 【保存】适用场景
export function saveTemplatePoint(data) {
  return request({
    url: '/api/ques/pc/quesTemplate/saveTemplatePoint',
    method: 'post',
    data
  })
}

// 【查询】获取题库分类树
export function getCategoryTree(params) {
  return request({
    url: '/api/ques/pc/questionBank/getCategoryTree',
    method: 'get',
    params
  })
}

// 【新增】题库分类
export function addCategory(data) {
  return request({
    url: '/api/ques/pc/questionBank/addCategory',
    method: 'post',
    data
  })
}

// 【编辑】题库分类
export function editCategory(data) {
  return request({
    url: '/api/ques/pc/questionBank/editCategory',
    method: 'post',
    data
  })
}

// 【删除】题库分类
export function deleteCategory(params) {
  return request({
    url: '/api/ques/pc/questionBank/deleteCategory',
    method: 'get',
    params
  })
}

// 【新增】题库
export function addQuestionBank(data) {
  return request({
    url: '/api/ques/pc/questionBank/addQuestionBank',
    method: 'post',
    data
  })
}

// 【删除】题库
export function deleteQuestionBank(params) {
  return request({
    url: '/api/ques/pc/questionBank/deleteQuestionBank',
    method: 'get',
    params
  })
}

// 【保存】问卷管理详情页-保存
export function saveQuesTemplate(data) {
  return request({
    url: '/api/ques/pc/quesTemplate/addQuesTemplate',
    method: 'post',
    data
  })
}
// 【编辑】问卷管理
export function editQuesTemplate(data) {
  return request({
    url: '/api/ques/pc/quesTemplate/editQuesTemplate',
    method: 'post',
    data
  })
}

// 【详情】问卷管理详情
export function getQuesTemplateDetail(params) {
  return request({
    url: '/api/ques/h5/quesTemplate/getQuesTemplateDetail',
    method: 'get',
    params
  })
}

// 预览问卷保存
export function savePreviewToRedis(data) {
  return request({
    url: '/api/ques/pc/quesTemplate/savePreviewToRedis',
    method: 'post',
    data
  })
}

// 【查询问题】根据问卷id查询所有问题
export function getQuestionListByQuesTemplateId(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/getQuestionListByQuesTemplateId',
    method: 'get',
    params
  })
}

