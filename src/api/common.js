import request from '@/utils/request'
// import { userInfo, userPermission, userPosition } from '@/api/mock'

// 获取用户信息
export function getUserInfo(params) {
  // return Promise.resolve(userInfo)
  return request({
    url: '/api/identity/pc/user/info',
    method: 'get',
    params
  })
}

// 获取用户信息
export function getUserBizline(params) {
  return request({
    url: '/api/identity/pc/user/bizLine',
    method: 'get',
    params
  })
}

// 退出登录
export function logout(data) {
  return request({
    url: '/api/identity/pc/sso/logout',
    method: 'post',
    data
  })
}

// 【查询】用户岗位
export function getUserPosition(data) {
  // return Promise.resolve(userPosition)
  return request({
    url: '/api/identity/pc/user/position',
    method: 'post',
    data
  })
}

// 【查询】用户菜单和按钮权限
export function getUserPermission(data) {
  // return Promise.resolve(userPermission)
  return request({
    url: '/api/identity/pc/user/permission',
    method: 'post',
    data
  })
}

// 【查询】区域公司列表
export function getAreaList(params) {
  return request({
    url: '/api/base/pc/mdata/list/areaCompany',
    method: 'get',
    noCache: true,
    params
  })
}

// 【查询】城市公司列表
export function getCityList(params) {
  return request({
    url: '/api/base/pc/mdata/list/cityCompany',
    method: 'get',
    noCache: true,
    params
  })
}

// 【查询】项目列表
export function getProjectList(data) {
  return request({
    url: '/api/base/pc/mdata/list/project',
    method: 'post',
    noCache: true,
    data
  })
}

// 【公共接口】启用的触点下拉框
export function getPointOption(params) {
  return request({
    url: '/api/ques/pc/point/option',
    method: 'get',
    params
  })
}

// 【查询】区域公司和城市公司2层层级树
export function getTwoLevelOrgan(params) {
  return request({
    url: '/api/base/pc/mdata/tree/cityCompany',
    method: 'get',
    params
  })
}

// 【查询】区域公司至项目3层层级树
export function getThreeLevelOrgan(params) {
  return request({
    url: '/api/base/pc/mdata/tree/project',
    method: 'get',
    params
  })
}

// 【用户-数据权限】区域公司至分期4层层级树
export function getTreeStage(params) {
  return request({
    url: '/api/base/pc/udata/tree/stage',
    method: 'get',
    noCache: true,
    params
  })
}

// 【用户-数据权限】项目和分期2层层级树
export function getProjectStage(data) {
  return request({
    url: '/api/base/pc/udata/tree/project-stage',
    method: 'post',
    noCache: true,
    data
  })
}

// 【用户数据权限】区域公司和城市公司2层层级树
export function getCityCompany(params) {
  return request({
    url: '/api/base/pc/udata/tree/cityCompany',
    method: 'get',
    params
  })
}

// 【查询】项目和分期2层层级树
export function getTwoLevelProject(data) {
  return request({
    url: '/api/base/pc/mdata/tree/project-stage',
    method: 'post',
    data
  })
}

// 【查询】楼栋至房间3层层级树
export function getRoomTree(data) {
  return request({
    url: '/api/base/pc/mdata/tree/house',
    method: 'post',
    data
  })
}

// 获取字典表集合，基础配置中的满意度数据字典
export function getDictList(params) {
  return request({
    url: '/api/base/pc/cfgDict/getDictList',
    method: 'get',
    noCache: true,
    params
  })
}

// 获取系统字典集合，主要是后台定义的一些枚举，有个在线文档中列了
export function getSysDictList(params) {
  return request({
    url: '/api/base/pc/sysDict/getDictList',
    method: 'get',
    noCache: true,
    params
  })
}

// 【用户-数据权限】是否有华发股份数据权限
export function gufenAuth(params) {
  return request({
    url: '/api/base/pc/udata/gufenAuth',
    method: 'get',
    params
  })
}

// 【查询】调研业态
export function getProductType(params) {
  return request({
    url: '/api/base/pc/mdata/tree/productType',
    method: 'get',
    params
  })
}

// 【查询】项目配套业态
export function getProjectSupport(params) {
  return request({
    url: '/api/base/pc/mdata/tree/projectSupport',
    method: 'get',
    params
  })
}

// 文件模版地址
export function getFileTemplate(params) {
  return request({
    url: '/api/base/pc/file/template',
    method: 'get',
    params
  })
}

// 文件导出任务列表
export function pageExportList(data) {
  return request({
    url: '/api/base/pc/task/pageExportList',
    method: 'post',
    data
  })
}

// 【查询】区域公司至项目楼栋列表
export function getBuilding(data) {
  return request({
    url: '/api/base/pc/mdata/list/building',
    method: 'post',
    data
  })
}

// 【查询】区域公司至项目分期楼栋单元
export function getUnit(data) {
  return request({
    url: '/api/base/pc/mdata/list/unit',
    method: 'post',
    data
  })
}

// 【查询】根据项目分期楼栋单元查询房间信息
export function getHouse(data) {
  return request({
    url: '/api/base/pc/mdata/list/house',
    method: 'post',
    data
  })
}
