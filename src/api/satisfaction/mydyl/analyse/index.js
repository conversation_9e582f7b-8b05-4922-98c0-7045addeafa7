import request from '@/utils/request'

// 【查询】触点分析-获取触点集合
export function getPointList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/point/getPointList',
    method: 'post',
    data
  })
}

// 【查询】触点分析-获取触点集合
export function getPointInfo(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/point/targetInfo',
    method: 'post',
    noCache: true,
    data
  })
}

// 【查询】指标分析-获取指标集合
export function getTargetList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/target/getTargetList',
    method: 'post',
    data
  })
}

// 【查询】指标分析-获取指标下的指标详情
export function getTargetInfo(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/target/targetInfo',
    method: 'post',
    noCache: true,
    data
  })
}

// 【分页】问卷明细
export function getQuestionnaireDetailList(data) {
  return request({
    url: '/api/report/pc/analyse/questionnaire/getQuestionnaireDetailList',
    method: 'post',
    data
  })
}
// 【查询】问卷统计信息
export function getQuestionnaireStatisticsData(data) {
  return request({
    url: '/api/report/pc/analyse/questionnaire/getQuestionnaireStatisticsData',
    method: 'post',
    data
  })
}
// 【查询】触点分析-获取指标其他原因集合
export function getPointOtherReasonList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/point/getOtherReasonList',
    method: 'post',
    data
  })
}
// 【查询】指标分析-获取指标其他原因集合
export function getTargetOtherReasonList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/satisfactionAnalysis/target/getOtherReasonList',
    method: 'post',
    data
  })
}
