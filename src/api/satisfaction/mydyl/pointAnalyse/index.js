import request from '@/utils/request'

// 【查询】指标分析-获取指标集合
export function getTargetList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/pointAnalysis/target/getTargetList',
    method: 'post',
    data
  })
}

// 【查询】指标分析-获取指标下的指标详情
export function getTargetInfo(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/pointAnalysis/target/targetInfo',
    method: 'post',
    noCache: true,
    data
  })
}
// 【查询】触点分析-获取指标其他原因集合
export function getOtherReasonList(data) {
  return request({
    url: '/api/report/pc/questionAnalysis/pointAnalysis/target/getOtherReasonList',
    method: 'post',
    data
  })
}
