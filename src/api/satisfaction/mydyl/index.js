import request from '@/utils/request'
const baseURL = 'http://api.pm.youmatech.com/mock/1890'
// 【统计】总体概览
export function getGeneralOverView(data) {
  return request({
    url: '/api/report/pc/analyse/general/overview',
    method: 'post',
    data
  })
}
// 【统计】总体满意度图表
export function getSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/general/satOverview',
    method: 'post',
    data
  })
}
// 【统计】触点满意度概览图表
export function getPointSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/general/pointSatOverview',
    method: 'post',
    data
  })
}
// 【统计】指标满意度概览图表
export function getTargetSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/general/targetSatOverview',
    method: 'post',
    data
  })
}

// 【统计】区域晾晒考核
export function getAreaCompanyScore(data) {
  return request({
    url: '/api/report/pc/analyse/examine/air/getAreaCompanyScore',
    method: 'post',
    data
  })
}

// 【统计】城市晾晒考核
export function getCityCompanyScore(data) {
  return request({
    url: '/api/report/pc/analyse/examine/air/getCityCompanyScore',
    method: 'post',
    data
  })
}

// 【统计】项目晾晒考核
export function getProjectScore(data) {
  return request({
    url: '/api/report/pc/analyse/examine/air/getProjectScore',
    method: 'post',
    data
  })
}

// 【统计】区域总体满意度概览图表
export function getAreaSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/areaCompany/satOverview',
    method: 'post',
    data
  })
}
// 【统计】区域触点满意度概览图表
export function getAreaPointSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/areaCompany/pointSatOverview',
    method: 'post',
    data
  })
}
// 【统计】区域指标满意度概览图表
export function getAreaTargetSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/areaCompany/targetSatOverview',
    method: 'post',
    data
  })
}

// 【统计】城市总体满意度概览
export function getCitySatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/cityCompany/satOverview',
    method: 'post',
    data
  })
}
// 【统计】城市触点满意度概览
export function getCityPointSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/cityCompany/pointSatOverview',
    method: 'post',
    data
  })
}
// 【统计】城市指标满意度概览
export function getCityTargetSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/cityCompany/targetSatOverview',
    method: 'post',
    data
  })
}

// 【统计】项目总体满意度概览
export function getProjectSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/project/satOverview',
    method: 'post',
    data
  })
}
// 【统计】项目触点满意度概览
export function getProjectPointSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/project/pointSatOverview',
    method: 'post',
    data
  })
}
// 【统计】项目指标满意度概览
export function getProjectTargetSatOverview(data) {
  return request({
    url: '/api/report/pc/analyse/project/targetSatOverview',
    method: 'post',
    data
  })
}
