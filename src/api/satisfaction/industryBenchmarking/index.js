import request from '@/utils/request'

// 【分页】行业对标年度列表
export function getScoreMapPageList(data) {
  return request({
    url: '/api/report/pc/score/map/getScoreMapPageList',
    method: 'post',
    data
  })
}

// 【新增】行业对标年度
export function addYearRule(data) {
  return request({
    url: '/api/report/pc/score/map/addYearRule',
    method: 'post',
    data
  })
}

// 【编辑】行业对标年度
export function editYearRule(data) {
  return request({
    url: '/api/report/pc/score/map/editYearRule',
    method: 'post',
    data
  })
}

// 【发布/撤回】行业对标年度
export function publishYearRule(data) {
  return request({
    url: '/api/report/pc/score/map/publishYearRule',
    method: 'post',
    data
  })
}

// 【删除】行业对标年度
export function deleteYearRule(data) {
  return request({
    url: '/api/report/pc/score/map/deleteYearRule',
    method: 'post',
    data
  })
}

// 【查询】行业对标子项列表
export function getItemList(data) {
  return request({
    url: '/api/report/pc/score/map/item/getItemList',
    method: 'post',
    data
  })
}

// 【新增】行业对标子项
export function addItem(data) {
  return request({
    url: '/api/report/pc/score/map/item/addItem',
    method: 'post',
    data
  })
}

// 【修改】行业对标子项
export function editItem(data) {
  return request({
    url: '/api/report/pc/score/map/item/editItem',
    method: 'post',
    data
  })
}

// 【删除】行业对标子项
export function deleteItem(data) {
  return request({
    url: '/api/report/pc/score/map/item/deleteItem',
    method: 'post',
    data
  })
}
