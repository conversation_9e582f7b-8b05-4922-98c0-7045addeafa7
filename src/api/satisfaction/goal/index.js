import request from '@/utils/request'

// 【分页】满意度年度目标列表
export function getYearGoalPageList(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/getYearGoalPageList',
    method: 'post',
    data
  })
}

// 【新增】新增年度目标
export function addYearGoal(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/addYearGoal',
    method: 'post',
    data
  })
}

// 【操作】取消发布年度目标
export function cancelYearPush(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/cancelYearPush',
    method: 'post',
    data
  })
}

// 【操作】作废年度目标
export function invalidYearGoal(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/invalidYearGoal',
    method: 'post',
    data
  })
}

// 【查询】指标目标详情
export function getGoalItemDetail(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/getGoalItemDetail',
    method: 'post',
    data
  })
}

// 【保存】指标目标详情设置
export function saveItem(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/saveItem',
    method: 'post',
    data
  })
}

// 【操作】取消锁定
export function cancelLockItem(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/cancelLockItem',
    method: 'post',
    data
  })
}

// 【分页】满意度目标配置操作日志列表
export function getGoalConfigLogPageList(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/log/getGoalConfigLogPageList',
    method: 'post',
    data
  })
}

// 【查询】目标拆解 v-1.12
export function getStandardDetail(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/getStandardDetail',
    method: 'post',
    data
  })
}

// 【操作】返回上一步 v-1.12
export function undoConfig(params) {
  return request({
    url: '/api/report/pc/analyse/goal/config/undo',
    method: 'get',
    params
  })
}
// 【保存】目标拆解 v-1.12
export function saveStandard(data) {
  return request({
    url: '/api/report/pc/analyse/goal/config/saveStandard',
    method: 'post',
    data
  })
}

// 满意度目标导出
export function exportGoalItem(data) {
  return request({
    url: '/api/task/pc/goal/exportGoalItem',
    method: 'post',
    data
  })
}
