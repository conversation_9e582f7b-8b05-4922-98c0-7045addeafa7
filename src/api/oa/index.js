
import request from '@/utils/request'

// 【查询】特殊事项申请详情（根据申请编号）
export function getSpecialDetailByApplyCode(params) {
  return request({
    url: '/api/ques/pc/matter/detailByApplyCode',
    method: 'get',
    params
  })
}

// 【查询】反向处罚申请详情（根据申请编号）
export function getReverseDetailByApplyCode(params) {
  return request({
    url: '/api/ques/pc/inspect/detailByApplyCode',
    method: 'get',
    params
  })
}

