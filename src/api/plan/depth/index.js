import request from '@/utils/request'

// 深访计划-新增选择调研题目下拉框
export function getQuesPainItem(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/getQuesPainItem',
    method: 'get',
    params
  })
}

// 【查询】计划调研重合范围
export function overlap(data) {
  return request({
    url: '/api/ques/pc/depth-plan/overlap',
    method: 'post',
    data
  })
}
// 【查询】计划调研重合范围【列表使用】
export function getOverlap(params) {
  return request({
    url: '/api/ques/pc/depth-plan/overlap',
    method: 'get',
    params
  })
}
// 新增
export function addPlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/addPlan',
    method: 'post',
    data
  })
}
// 更新
export function updatePlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/updatePlan',
    method: 'post',
    data
  })
}
// 计划列表
export function pageList(data) {
  return request({
    url: '/api/ques/pc/depth-plan/pageList',
    method: 'post',
    data
  })
}
// 计划列表
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/depth-plan/detail',
    method: 'get',
    params
  })
}
// 【修改】发布计划
export function publishPlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/publish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}
// 【删除】删除计划
export function deletePlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/delete?uuid=' + data.uuid,
    method: 'post',
    data
  })
}
// 【修改】结束调研
export function finishPlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/finish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}
// 【修改】取消发布
export function unPublishPlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/unPublish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}
// 【操作】操作问卷
export function operateQues(data) {
  return request({
    url: '/api/ques/pc/questionnaire/operate',
    method: 'post',
    data
  })
}
// 【操作】操作问卷
export function getLog(data) {
  return request({
    url: '/api/ques/pc/questionnaire/logPageList',
    method: 'post',
    data
  })
}
// 【查询】操作日志详情
export function getLogDetail(data) {
  return request({
    url: '/api/ques/pc/questionnaire/logDetail',
    method: 'post',
    data
  })
}
// 【查询】有操作权限的计划
export function hasAuthPlan(data) {
  return request({
    url: '/api/ques/pc/depth-plan/hasAuthPlan',
    method: 'post',
    data
  })
}
