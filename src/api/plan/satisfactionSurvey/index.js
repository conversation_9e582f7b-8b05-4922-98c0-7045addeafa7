import request from '@/utils/request'

// 【查询】计划列表
export function pageList(data) {
  return request({
    url: '/api/ques/pc/poi-plan/pageList',
    method: 'post',
    data
  })
}

// 【修改】发布计划
export function publishPlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/publish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【删除】删除计划
export function deletePlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/delete?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】结束调研
export function finishPlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/finish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】取消发布
export function unPublishPlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/unPublish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【新增】新增计划
export function addPlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/addPlan',
    method: 'post',
    data
  })
}

// 【修改】编辑计划
export function updatePlan(data) {
  return request({
    url: '/api/ques/pc/poi-plan/updatePlan',
    method: 'post',
    data
  })
}

// 【查询】是否已产生问卷
export function hasQues(params) {
  return request({
    url: '/api/ques/pc/poi-plan/hasQues',
    method: 'get',
    params
  })
}

// 【查询】是否已产生问卷
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/poi-plan/detail',
    method: 'get',
    params
  })
}

// 【下拉框】根据触点id获取问卷集合
export function getQuesTemplateList(params) {
  return request({
    url: '/api/ques/pc/quesTemplate/getQuesTemplateList',
    method: 'get',
    params
  })
}
