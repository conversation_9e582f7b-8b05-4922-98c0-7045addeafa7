import request from '@/utils/request'

// 【查询】问卷详情顶部数据
export function getTopData(data) {
  return request({
    url: '/api/report/pc/poi-plan/getTopData',
    method: 'post',
    data
  })
}

// 【查询】调研详情-问卷列表
export function quesPageList(data) {
  return request({
    url: '/api/ques/pc/plan/quesPageList',
    method: 'post',
    data
  })
}

// 【查询】作废问卷
export function cancelQues(data) {
  return request({
    url: '/api/ques/pc/questionnaire/cancel?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【查询】重新推送
export function retrySend(data) {
  return request({
    url: '/api/ques/pc/questionnaire/retrySend?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 指标分析-获取问卷指标集合
export function getTargetList(data) {
  return request({
    url: '/api/report/pc/questionnaire/targetAnalysis/getTargetList',
    method: 'post',
    data
  })
}

// 指标分析-获取问卷指标集合
export function getTargetInfo(data) {
  return request({
    url: '/api/report/pc/questionnaire/targetAnalysis/targetInfo',
    method: 'post',
    noCache: true,
    data
  })
}

// 问卷分析-数据概况
export function getDataGeneral(data) {
  return request({
    url: '/api/report/pc/questionnaire/questionnaireAnalysis/dataGeneral',
    method: 'post',
    noCache: true,
    data
  })
}

// 问卷分析-获得调研计划题目集合
export function getQuestionList(data) {
  return request({
    url: '/api/report/pc/questionnaire/questionnaireAnalysis/getQuestionList',
    method: 'post',
    noCache: true,
    data
  })
}

// 问卷分析-统计单选/多选/打分题信息
export function getStatisticalResult(data) {
  return request({
    url: '/api/report/pc/questionnaire/questionnaireAnalysis/getStatisticalResult',
    method: 'post',
    noCache: true,
    data
  })
}

// 问卷分析-获得填空题和文本题答案列表
export function pageAnswer(data) {
  return request({
    url: '/api/report/pc/questionnaire/questionnaireAnalysis/pageAnswer',
    method: 'post',
    noCache: true,
    data
  })
}

// 问卷分析-获附件题答案列表
export function pageFile(data) {
  return request({
    url: '/api/report/pc/questionnaire/questionnaireAnalysis/pageFile',
    method: 'post',
    noCache: true,
    data
  })
}
// 指标分析-获取指标其他原因集合
export function getOtherReasonList(data) {
  return request({
    url: '/api/report/pc/questionnaire/targetAnalysis/getOtherReasonList',
    method: 'post',
    noCache: true,
    data
  })
}

// 【查询报表】计划列表
export function pageReportList(data) {
  return request({
    url: '/api/report/pc/poi-plan/pageReportList',
    method: 'post',
    data
  })
}
