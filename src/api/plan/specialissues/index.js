import request from '@/utils/request'

// 【查询】计划列表
export function pageList(data) {
  return request({
    url: '/api/ques/pc/spi-plan/pageList',
    method: 'post',
    data
  })
}

// 【修改】发布计划
export function publishPlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/publish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【删除】删除计划
export function deletePlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/delete?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】结束调研
export function finishPlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/finish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【修改】取消发布
export function unPublishPlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/unPublish?uuid=' + data.uuid,
    method: 'post',
    data
  })
}

// 【新增】新增计划
export function addPlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/addPlan',
    method: 'post',
    data
  })
}

// 【修改】编辑计划
export function updatePlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/updatePlan',
    method: 'post',
    data
  })
}

// 【查询】计划详情-客户名单
export function listCustomer(params) {
  return request({
    url: '/api/ques/pc/spi-plan/listCustomer',
    method: 'get',
    params
  })
}

// 【查询】选择客户
export function pageCustomer(data) {
  return request({
    url: '/api/ques/pc/spi-plan/pageCustomer',
    method: 'post',
    data
  })
}

// 【查询】计划详情-客户名单
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/spi-plan/detail',
    method: 'get',
    params
  })
}

// 【查询】计划详情-客户名单
export function getListCustomer(params) {
  return request({
    url: '/api/ques/pc/spi-plan/listCustomer',
    method: 'get',
    params
  })
}

// 【查询】专项有操作权限的计划
export function hasAuthPlan(data) {
  return request({
    url: '/api/ques/pc/spi-plan/hasAuthPlan',
    method: 'post',
    data
  })
}
