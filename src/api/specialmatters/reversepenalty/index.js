import request from '@/utils/request'

// 【查询】申请列表
export function pageList(data) {
  return request({
    url: '/api/ques/pc/inspect/pageList',
    method: 'post',
    data
  })
}

// 【删除】删除申请
export function deleteInspect(params) {
  return request({
    url: '/api/ques/pc/inspect/delete',
    method: 'get',
    params
  })
}

// 【查询】申请详情
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/inspect/detail',
    method: 'get',
    params
  })
}

// 【新增】新增申请
export function addApply(data) {
  return request({
    url: '/api/ques/pc/inspect/addApply',
    method: 'post',
    data
  })
}

// 【修改】编辑申请
export function updateApply(data) {
  return request({
    url: '/api/ques/pc/inspect/updateApply',
    method: 'post',
    data
  })
}

// 【操作】扣除、取消扣除
export function updateDeductStatus(data) {
  return request({
    url: '/api/ques/pc/inspect/updateDeductStatus',
    method: 'post',
    data
  })
}

