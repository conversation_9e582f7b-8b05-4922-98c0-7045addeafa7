import request from '@/utils/request'

// 【查询】申请列表
export function pageList(data) {
  return request({
    url: '/api/ques/pc/matter/pageList',
    method: 'post',
    data
  })
}

// 【删除】删除申请
export function deleteMatter(params) {
  return request({
    url: '/api/ques/pc/matter/delete',
    method: 'get',
    params
  })
}

// 【查询】申请详情
export function getDetail(params) {
  return request({
    url: '/api/ques/pc/matter/detail',
    method: 'get',
    params
  })
}

// 【新增】新增申请
export function addApply(data) {
  return request({
    url: '/api/ques/pc/matter/addApply',
    method: 'post',
    data
  })
}

// 【修改】编辑申请
export function updateApply(data) {
  return request({
    url: '/api/ques/pc/matter/updateApply',
    method: 'post',
    data
  })
}

