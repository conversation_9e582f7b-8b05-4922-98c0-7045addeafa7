import request from '@/utils/request'

// 【导出】总体满意度分析-指标分析
export function exportTargetAnalysis(data) {
  return request({
    url: '/api/task/pc/satisfactionAnalysis/exportTargetAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】总体满意度分析-触点分析
export function exportPointAnalysis(data) {
  return request({
    url: '/api/task/pc/satisfactionAnalysis/exportPointAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】触点总体满意度分析-指标分析
export function exportPointTargetAnalysis(data) {
  return request({
    url: '/api/task/pc/pointSatisfactionAnalysis/exportTargetAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】总体满意度图表【总体概览】
export function exportSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/general/exportSatOverviewData',
    method: 'post',
    data
  })
}
// 【导出】触点满意度概览图表【总体概览】
export function exportPointSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/general/exportPointSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】指标满意度概览图表【总体概览】
export function exportTargetSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/general/exportTargetSatOverviewData',
    method: 'post',
    data
  })
}
// 【导出】总体满意度图表【区域排名】
export function exportAreaSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/areaCompany/exportSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】触点满意度概览图表【区域排名】
export function exportAreaPointSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/areaCompany/exportPointSatOverviewData',
    method: 'post',
    data
  })
}
// 【导出】指标满意度概览图表【区域排名】
export function exportAreaTargetSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/areaCompany/exportTargetSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】总体满意度图表【城市】
export function exportCitySatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/cityCompany/exportSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】触点满意度概览图表【城市】
export function exportCityPointSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/cityCompany/exportPointSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】指标满意度概览图表【城市】
export function exportCityTargetSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/cityCompany/exportTargetSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】总体满意度图表【项目】
export function exportProjectSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/project/exportSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】触点满意度概览图表【项目】
export function exportProjectPointSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/project/exportPointSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】指标满意度概览图表【项目】
export function exportProjectTargetSatOverviewData(data) {
  return request({
    url: '/api/task/pc/analyse/project/exportTargetSatOverviewData',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】回访名单
export function exportSurveyTask(data) {
  return request({
    url: '/api/task/pc/survey-task/exportSurveyTask',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】客户原声
export function exportQuestionnaire(data) {
  return request({
    url: '/api/task/pc/questionnaire/exportQuestionnaire',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】调研详情
export function exportSurveyDetail(data) {
  return request({
    url: '/api/task/pc/surveyPlan/exportSurveyDetail',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】问卷明细
export function exportQuestionnaireDetail(data) {
  return request({
    url: '/api/task/pc/analyse/questionnaire/exportQuestionnaireDetail',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】汇总概览
export function exportGatherData(data) {
  return request({
    url: '/api/task/pc/survey/overview/exportGatherData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】明细概览
export function exportDetailData(data) {
  return request({
    url: '/api/task/pc/survey/overview/exportDetailData',
    method: 'post',
    noPage: true,
    data
  })
}
// 【导出】指标分析-触点分析
export function exportTargetPointAnalysis(data) {
  return request({
    url: '/api/task/pc/targetAnalysis/exportTargetPointAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】总体满意度分析-触点分析-其它原因
export function exportPointOtherReason(data) {
  return request({
    url: '/api/task/pc/satisfactionAnalysis/exportPointOtherReason',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】总体满意度分析-指标分析-其他原因
export function exportTargetOtherReason(data) {
  return request({
    url: '/api/task/pc/satisfactionAnalysis/exportTargetOtherReason',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】触点总体满意度分析-指标分析-其他原因
export function exportPointTargetOtherReason(data) {
  return request({
    url: '/api/task/pc/pointSatisfactionAnalysis/exportTargetOtherReason',
    method: 'post',
    noPage: true,
    data
  })
}

// 【导出】指标分析-触点分析-其他原因
export function exportTargetPointOtherReason(data) {
  return request({
    url: '/api/task/pc/pointSatisfactionAnalysis/exportTargetPointOtherReason',
    method: 'post',
    noPage: true,
    data
  })
}

// 【调研计划】统计分析-指标分析
export function exportPlanAnalysisTargetAnalysis(data) {
  return request({
    url: '/api/task/pc/planAnalysis/exportTargetAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}
// 【调研计划】统计分析-其它原因导出
export function exportOtherReasonPlanAnalysis(data) {
  return request({
    url: '/api/task/pc/planAnalysis/exportOtherReason',
    method: 'post',
    noPage: true,
    data
  })
}
// 满意度/深访调研-统计分析-问卷分析导出
export function exportQuestionnaireAnalysis(data) {
  return request({
    url: '/api/task/pc/survey/exportQuestionnaireAnalysis',
    method: 'post',
    noPage: true,
    data
  })
}
// 绩效考核导出
export function exportExamineData(data) {
  return request({
    url: '/api/task/pc/analyse/examine/exportExamineData',
    method: 'post',
    noPage: true,
    data
  })
}

