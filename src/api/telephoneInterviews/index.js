
import request from '@/utils/request'

// 【查询】回访名单
export function pageList(data) {
  return request({
    url: '/api/ques/pc/survey-task/pageList',
    method: 'post',
    data
  })
}

// 【查询】客户原声
export function customPageList(data) {
  return request({
    url: '/api/ques/pc/questionnaire/pageList',
    method: 'post',
    data
  })
}

// 【操作】编辑问卷
export function updateQuestion(data) {
  return request({
    url: '/api/ques/pc/questionnaire/update',
    method: 'post',
    data
  })
}

// 查询【满意度】计划年份
export function getMydYear(params) {
  return request({
    url: '/api/ques/pc/poi-plan/optionYear',
    method: 'get',
    params
  })
}

// 查询【满意度】计划选项
export function getMydPlan(params) {
  return request({
    url: '/api/ques/pc/poi-plan/option',
    method: 'get',
    params
  })
}

// 查询【专项】计划年份
export function getSpecYear(params) {
  return request({
    url: '/api/ques/pc/spi-plan/optionYear',
    method: 'get',
    params
  })
}

// 查询【专项】计划选项
export function getSpecPlan(params) {
  return request({
    url: '/api/ques/pc/spi-plan/option',
    method: 'get',
    params
  })
}

// 查询【深访】计划年份
export function getDepthYear(params) {
  return request({
    url: '/api/ques/pc/depth-plan/optionYear',
    method: 'get',
    params
  })
}

// 查询【深访】计划选项
export function getDepthPlan(params) {
  return request({
    url: '/api/ques/pc/depth-plan/option',
    method: 'get',
    params
  })
}
