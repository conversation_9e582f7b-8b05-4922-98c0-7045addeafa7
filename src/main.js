import Vue from 'vue'
import 'normalize.css/normalize.css' // a modern alternative to CSS resets
import Element from 'element-ui'

import '@/styles/index.scss' // global css

import App from './App'
import store from './store'
import router from './router'

import './icons' // icon
import './permission' // permission control

import * as filters from './filters' // global filters
import moment from 'moment'
import components from './components'

// 错误处理
import { errorHandle, handleFile, base64Img } from '@/utils'

import checkPermission from '@/utils/permission'

import { getPageSize, setPageSize } from '@/utils/cache'

Vue.prototype.$moment = moment // 批量注册公共组件
Vue.use(components)

// 页面公共缓存
Vue.prototype.$setPageSize = setPageSize
Vue.prototype.$getPageSize = getPageSize

// 系统提供的公共方法
Vue.prototype.$errorHandle = errorHandle
Vue.prototype.$handleFile = handleFile
Vue.prototype.$base64Img = base64Img

Vue.prototype.$load = () => {
  return Vue.prototype.$loading({
    lock: true,
    customClass: 'customLoading',
    background: 'rgba(0, 0, 0, 0.2)'
  })
}

// 全局清除表单方法
Vue.prototype.$resetForm = function(name) {
  this.$refs[name].resetFields()
  this.$nextTick(() => {
    this.$refs[name].clearValidate()
  })
}
Vue.prototype.$formatterTable = filters.formatterTable

Vue.prototype.$checkPermission = checkPermission

Vue.use(Element, {
  // size: 'small'
})

Object.keys(filters).forEach(key => {
  Vue.filter(key, filters[key])
})

Vue.config.productionTip = false

const app = new Vue({
  el: '#app',
  router,
  store,
  render: h => h(App)
})
console.log(app)
