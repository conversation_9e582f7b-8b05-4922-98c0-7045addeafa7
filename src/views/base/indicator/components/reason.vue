<template>
  <div v-loading="tableLoading">
    <div style="font-size: 14px;font-weight: bold;margin-bottom: 10px;">{{ searchForm.reasonTypeCode === 2200072 ? '不满意' : '满意' }}原因配置</div>
    <el-row type="flex">
      <div style="flex: 1;margin-right: 20px;">
        <el-form-item :label="searchForm.reasonTypeCode === 2200072 ? '不满意原因：' : '满意原因：'" label-width="100px">
          <el-input v-model="searchForm.reasonName" :placeholder="searchForm.reasonTypeCode === 2200072 ? '请输入不满意原因' : '请输入满意原因'" clearable />
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
        <el-button v-if="$checkPermission(['JCPZ_ZBPZ_XZZBYY'])" type="primary" icon="el-icon-circle-plus-outline" @click="editReasonShow = true">新增</el-button>
      </div>
    </el-row>
    <el-row>
      <el-col :span="12">
        <el-form-item label="添加“其他”项：">
          <el-switch v-model="otherFlag" checked :disabled="!$checkPermission(['JCPZ_ZBPZ_TJZBQT'])" @change="changeOtherFlag" />
        </el-form-item>
      </el-col>
    </el-row>
    <um-table-full :data="tableData">
      <el-table-column prop="xh" label="序号" width="80" />
      <el-table-column key="reasonName" width="220" prop="reasonName" :label="searchForm.reasonTypeCode === 2200072 ? '不满意原因' : '满意原因'" />
      <el-table-column key="categoryName" prop="categoryName" width="220" label="所属分类" />
      <el-table-column key="pointNameList" min-width="300" label="适用触点">
        <template slot-scope="{ row }">
          <span class="row_2" :title="row.pointNameList ? row.pointNameList.join('、') : ''">{{ row.pointNameList && row.pointNameList.length ? row.pointNameList.join('、') : '不限' }}</span>
        </template>
      </el-table-column>
      <el-table-column label="装修类型" width="120">
        <template slot-scope="{ row }">{{ row.decorateTypeCode === DECORATION_STATUS.SIMPLE ? '毛坯' : row.decorateTypeCode === DECORATION_STATUS.FINE ? '精装' : '不限' }}</template>
      </el-table-column>
      <el-table-column label="操作" fixed="right" width="140">
        <template v-if="!row.otherFlag" slot-scope="{row}">
          <el-button v-if="$checkPermission(['JCPZ_ZBPZ_XGZBYY'])" type="text" @click="editReasonData = row;editReasonShow = true">修改</el-button>
          <el-button v-if="$checkPermission(['JCPZ_ZBPZ_SCZBYY'])" type="text" @click="deleteTarget(row, `是否确定删除该${searchForm.reasonTypeCode === 2200072 ? '不满意原因' : '满意原因'}？`)"><span class="danger">删除</span></el-button>
        </template>
      </el-table-column>
    </um-table-full>
    <pagination
      :total="tableTotal"
      :page.sync="searchForm.page.pageNum"
      :limit.sync="searchForm.page.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList('page')"
    />
    <EditReason
      :visible.sync="editReasonShow"
      :edit-data="editReasonData"
      :current-node-data="currentNodeData"
      :reason-type-code="searchForm.reasonTypeCode"
      @success="getList"
    />
  </div>
</template>

<script>
import { getTargetReasonPage, deleteTargetReason, changeOtherFlag } from '@/api/base/indicator/index'
import { DECORATION_STATUS } from '@/enum'
import getList from '@/mixins/getList'
import EditReason from './editReason.vue'
export default {
  components: { EditReason },
  mixins: [getList],
  props: {
    currentNodeData: {
      type: Object,
      default: () => ({})
    },
    reasonTypeCode: {
      type: Number,
      default: 2200071
    },
    tgtTargetId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      listApi: getTargetReasonPage,
      DECORATION_STATUS,
      searchForm: {
        tgtTargetId: null,
        reasonTypeCode: 2200071,
        reasonName: null
      },
      otherFlag: false, // 是否添加其它项
      tableData: [],
      editReasonShow: false,
      editReasonData: {}
    }
  },
  watch: {
    reasonTypeCode(val) {
      this.searchForm.reasonName = null
      this.searchForm.reasonTypeCode = val
      this.getList()
    },
    tgtTargetId: {
      immediate: true,
      handler(val) {
        this.searchForm.tgtTargetId = val
        this.getList()
      }
    }
  },
  methods: {
    afterApiCallBack(data) {
      this.otherFlag = data.otheFlag
    },
    // 删除指标、满意、不满意原因
    deleteTarget(data, tip) {
      this.$confirm(tip, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteTargetReason({ tgtTargetReasonId: data.tgtTargetReasonId }).then(res => {
          this.$message.success(res.msg)
          this.getList()
        }).finally(() => load.close())
      })
    },
    changeOtherFlag(flag) {
      const load = this.$load()
      changeOtherFlag({ tgtTargetId: this.searchForm.tgtTargetId, reasonTypeCode: this.searchForm.reasonTypeCode, otherFlag: +flag }).then(res => {
        this.$message.success(res.msg)
        this.getList()
      }).finally(() => load.close())
    }
  }
}
</script>
