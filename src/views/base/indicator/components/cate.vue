<template>
  <div v-loading="tableLoading">
    <div style="font-size: 14px;font-weight: bold;margin-bottom: 10px;">{{ searchForm.reasonTypeCode === 2200072 ? '不满意' : '满意' }}原因分类</div>
    <el-row type="flex">
      <div style="flex: 1;margin-right: 20px;">
        <el-form-item label="分类名称：" label-width="100px">
          <el-input v-model="searchForm.categoryName" placeholder="请输入分类名称" clearable />
        </el-form-item>
      </div>
      <div>
        <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
        <el-button v-if="$checkPermission(['JCPZ_ZBPZ_XZZBFL'])" type="primary" icon="el-icon-circle-plus-outline" @click="editCateShow = true">新增</el-button>
      </div>
    </el-row>
    <um-table-full :data="tableData">
      <el-table-column prop="xh" label="序号" width="80" />
      <el-table-column key="categoryName" min-width="200" prop="categoryName" label="分类名称" />
      <el-table-column key="remark" min-width="200" prop="remark" label="备注" />
      <el-table-column key="otherFlag" width="180" prop="otherFlag" label="是否支持添加“其他”项">
        <template slot-scope="{ row }">
          {{ row.otherFlag === 1 ? '是' : '否' }}
        </template>
      </el-table-column>
      <el-table-column key="updateUser" width="120" prop="updateUser" label="更新人" />
      <el-table-column key="updateTime" width="220" prop="updateTime" label="更新时间" />
      <el-table-column label="操作" fixed="right" width="140">
        <template slot-scope="{row}">
          <el-button v-if="$checkPermission(['JCPZ_ZBPZ_BJZBFL'])" type="text" @click="editCateData = row;editCateShow = true">修改</el-button>
          <el-button v-if="$checkPermission(['JCPZ_ZBPZ_SCZBFL'])" type="text" @click="deleteTarget(row, `是否确定删除该${searchForm.reasonTypeCode === 2200072 ? '不满意原因' : '满意原因'}？`)"><span class="danger">删除</span></el-button>
        </template>
      </el-table-column>
    </um-table-full>
    <pagination
      :total="tableTotal"
      :page.sync="searchForm.page.pageNum"
      :limit.sync="searchForm.page.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList('page')"
    />
    <EditCate
      :visible.sync="editCateShow"
      :edit-data="editCateData"
      :current-node-data="currentNodeData"
      :reason-type-code="searchForm.reasonTypeCode"
      @success="editSuccess"
    />
  </div>
</template>

<script>
import { getCategoryPage, deleteCategory } from '@/api/base/indicator/index'
import { DECORATION_STATUS } from '@/enum'
import getList from '@/mixins/getList'
import EditCate from './editCate.vue'
export default {
  components: { EditCate },
  mixins: [getList],
  props: {
    currentNodeData: {
      type: Object,
      default: () => ({})
    },
    reasonTypeCode: {
      type: Number,
      default: 2200071
    },
    tgtTargetId: {
      type: String,
      default: null
    }
  },
  data() {
    return {
      listApi: getCategoryPage,
      DECORATION_STATUS,
      searchForm: {
        tgtTargetId: null,
        reasonTypeCode: 2200071,
        categoryName: null
      },
      tableData: [],
      editCateShow: false,
      editCateData: {}
    }
  },
  watch: {
    reasonTypeCode(val) {
      this.searchForm.categoryName = null
      this.searchForm.reasonTypeCode = val
      this.getList()
    },
    tgtTargetId: {
      immediate: true,
      handler(val) {
        this.searchForm.tgtTargetId = val
        this.getList()
      }
    }
  },
  methods: {
    editSuccess() {
      this.getList()
      this.$emit('refresh')
    },
    // 删除指标、满意、不满意原因
    deleteTarget(data, tip) {
      this.$confirm(tip, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteCategory({ categoryId: data.categoryId }).then(res => {
          this.$message.success(res.msg)
          this.getList()
          this.$emit('refresh')
        }).finally(() => load.close())
      })
    }
  }
}
</script>
