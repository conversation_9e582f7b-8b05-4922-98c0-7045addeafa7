<template>
  <el-dialog
    :title="addForm.tgtTargetReasonId ? `修改${reasonTypeCode === 2200072 ? '不':''}满意原因` : `新增${reasonTypeCode === 2200072 ? '不':''}满意原因`"
    :visible.sync="asyncVisible"
    width="500px"
    @close="$refs.addForm.resetFields();"
  >
    <el-form ref="addForm" :model="addForm" label-width="90px" action="javascript:void(0);">
      <el-form-item :label="reasonTypeCode === 2200072 ? '不满意原因：' : '满意原因：'" prop="reasonName" :rules="[{ required: true, message: reasonTypeCode === 2200072 ? '请输入不满意原因' : '请输入满意原因', trigger: ['blur']}]">
        <el-input
          v-model.trim="addForm.reasonName"
          :placeholder="reasonTypeCode === 2200072 ? '请输入不满意原因' : '请输入满意原因'"
          maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item label="所属分类：" prop="categoryId">
        <el-select v-model="addForm.categoryId" filterable clearable style="width: 100%" placeholder="请选择">
          <el-option v-for="item in cateList" :key="item.categoryId" :label="item.categoryName" :value="item.categoryId" />
        </el-select>
      </el-form-item>
      <el-form-item label="适用触点：" prop="pointIdList">
        <el-cascader
          v-model="addForm.pointIdList"
          :options="pointOptions"
          collapse-tags
          style="width: 100%;"
          filterable
          :props="{
            multiple: true,
            emitPath: false
          }"
          clearable
        />
      </el-form-item>
      <el-form-item label="装修类型：" prop="decorateTypeCode">
        <el-select v-model="addForm.decorateTypeCode" filterable clearable style="width: 100%" placeholder="请选择">
          <el-option label="毛坯" :value="DECORATION_STATUS.SIMPLE" />
          <el-option label="精装" :value="DECORATION_STATUS.FINE" />
        </el-select>
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="asyncVisible=false">取消</el-button>
      <el-button type="primary" @click="submitHandle">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addTargetReason, editTargetReason, getCategoryList } from '@/api/base/indicator/index'
import getPointList from '@/mixins/getPointList'
import { DECORATION_STATUS } from '@/enum'
export default {
  name: 'EditReason',
  mixins: [getPointList],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    reasonTypeCode: {
      type: [String, Number],
      default: null
    },
    currentNodeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      DECORATION_STATUS,
      addForm: {
        tgtTargetReasonId: null,
        reasonName: null,
        pointIdList: [],
        decorateTypeCode: null,
        categoryId: null,
        wyFlag: null,
        clubFlag: null
      },
      needSpecial: true,
      cateList: [],
      options: [] // 上一级指标下拉框
    }
  },
  computed: {
    asyncVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    asyncVisible(val) {
      if (val && this.editData.tgtTargetReasonId) {
        const { pointIdList, reasonName, reasonTypeCode, tgtTargetReasonId, decorateTypeCode, wyFlag, clubFlag, categoryId } = this.editData
        this.$nextTick(() => {
          this.addForm = {
            pointIdList: pointIdList || [],
            categoryId,
            reasonName,
            reasonTypeCode,
            tgtTargetReasonId,
            decorateTypeCode: decorateTypeCode === -1 ? null : decorateTypeCode,
            wyFlag: wyFlag === -1 ? null : wyFlag,
            clubFlag: clubFlag === -1 ? null : clubFlag
          }
        })
      } else {
        this.$parent.editReasonData = {}
        this.addForm.tgtTargetReasonId = null
      }
      if (val) {
        this.getPointOption(false)
        this.getCategoryList()
      }
    }
  },
  created() {

  },
  methods: {
    getCategoryList() {
      getCategoryList({ reasonTypeCode: this.reasonTypeCode, targetId: this.$parent.currentNodeData.tgtTargetId }).then(res => {
        this.cateList = res.data || []
      })
    },
    // 提交
    submitHandle() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const load = this.$load()
          const API = this.addForm.tgtTargetReasonId ? editTargetReason : addTargetReason
          API({
            ...this.addForm,
            decorateTypeCode: !this.addForm.decorateTypeCode ? -1 : this.addForm.decorateTypeCode,
            tgtTargetId: this.$parent.currentNodeData.tgtTargetId,
            reasonTypeCode: this.$parent.searchForm.reasonTypeCode
          }).then(res => {
            this.$message.success(res.msg)
            this.$emit('success')
            this.asyncVisible = false
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>
