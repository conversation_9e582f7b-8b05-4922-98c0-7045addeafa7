<template>
  <el-dialog
    :title="addForm.categoryId ? `修改${reasonTypeCode === 2200072 ? '不':''}满意原因分类` : `新增${reasonTypeCode === 2200072 ? '不':''}满意原因分类`"
    :visible.sync="asyncVisible"
    width="600px"
    @close="$refs.addForm.resetFields();"
  >
    <el-form ref="addForm" :model="addForm" label-position="left" label-width="130px" action="javascript:void(0);">
      <el-form-item label="分类名称" prop="categoryName" :rules="[{ required: true, message: '请输入分类名称', trigger: ['blur']}]">
        <el-input
          v-model.trim="addForm.categoryName"
          placeholder="请输入分类名称"
          maxlength="30"
          clearable
        />
      </el-form-item>
      <el-form-item label="备注" prop="remark">
        <el-input
          v-model.trim="addForm.remark"
          type="textarea"
          placeholder="请输入分类名称"
          maxlength="50"
          :rows="4"
          resize="none"
          show-word-limit
          clearable
        />
      </el-form-item>
      <el-form-item label="是否添加“其他”项：" prop="otherFlag">
        <el-switch v-model="addForm.otherFlag" />
      </el-form-item>
    </el-form>
    <div slot="footer" class="dialog-footer">
      <el-button type="primary" plain @click="asyncVisible=false">取消</el-button>
      <el-button type="primary" @click="submitHandle">确定</el-button>
    </div>
  </el-dialog>
</template>
<script>
import { addCategory, editCategory } from '@/api/base/indicator/index'
import { DECORATION_STATUS } from '@/enum'
export default {
  name: 'EditCate',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    editData: {
      type: Object,
      default: () => ({})
    },
    reasonTypeCode: {
      type: [String, Number],
      default: null
    },
    currentNodeData: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      DECORATION_STATUS,
      addForm: {
        categoryId: null,
        categoryName: null,
        remark: null,
        otherFlag: true
      },
      needSpecial: true,
      options: [] // 上一级指标下拉框
    }
  },
  computed: {
    asyncVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    asyncVisible(val) {
      if (val && this.editData.categoryId) {
        const { categoryId, categoryName, remark, otherFlag } = this.editData
        this.$nextTick(() => {
          this.addForm = {
            categoryId,
            categoryName,
            remark,
            otherFlag: !!otherFlag
          }
        })
      } else {
        this.$parent.editCateData = {}
        this.addForm.categoryId = null
      }
      if (val) {
        this.getPointOption(false)
      }
    }
  },
  created() {

  },
  methods: {
    // 提交
    submitHandle() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          const load = this.$load()
          const API = this.addForm.categoryId ? editCategory : addCategory
          API({
            ...this.addForm,
            tgtTargetId: this.$parent.currentNodeData.tgtTargetId,
            reasonTypeCode: this.$parent.searchForm.reasonTypeCode,
            otherFlag: +this.addForm.otherFlag
          }).then(res => {
            this.$message.success(res.msg)
            this.$emit('success')
            this.asyncVisible = false
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>
