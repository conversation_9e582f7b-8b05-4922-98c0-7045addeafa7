<template>
  <div class="g-container">
    <CommonTitle title="满意度指标管理" show-bg>
      <el-button v-if="$checkPermission(['JCPZ_ZBPZ_CKXGRZ'])" type="primary" plain @click="$router.push('/base/indicator/log')">查看日志</el-button>
    </CommonTitle>
    <div class="g-body">
      <div v-loading="sortLoading" class="g-body__l">
        <div class="g-body__l--head">
          <span class="m-title">指标</span>
          <i v-if="$checkPermission(['JCPZ_ZBPZ_XZZB'])" class="el-icon-circle-plus" @click="parentTargetName = null;editTargetForm.parentId = editTargetForm.tgtTargetId = null;editTargetFormVisible = true" />
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" class="g-body__l--content">
          <UmConfigTree
            show-menu
            :tree-data="treeData"
            :default-props="{
              label: 'targetName',
              value: 'tgtTargetId',
              children: 'targetChildren'
            }"
            draggable
            :current-id="currentNodeData.tgtTargetId"
            @node-click="nodeClick"
            @dropSort="dropSort"
          >
            <template slot="menu" slot-scope="{data}">
              <i class="el-icon-rank" style="margin-right: 10px;color: #999;" />
              <el-popover
                placement="right-start"
                popper-class="custom-tree-popover"
                width="104"
                :visible-arrow="false"
                trigger="hover"
              >
                <div class="u-tree__menu">
                  <div v-if="$checkPermission(['JCPZ_ZBPZ_XZZB']) && ((data.rootDepth === 0 && data.levelCode !== 2200062) || data.rootDepth === 1)" @click="editTargetForm.tgtTargetId = null;editTargetForm.parentId = data.tgtTargetId; parentTargetName = data.targetFullName;editTargetFormVisible = true"><i class="el-icon-circle-plus-outline" />添加</div>
                  <div v-if="$checkPermission(['JCPZ_ZBPZ_BJZB'])" @click="editTree(data)"><i class="el-icon-edit-outline" />编辑</div>
                  <div v-if="$checkPermission(['JCPZ_ZBPZ_SCZB']) && !data.systemFlag" @click="deleteTarget(data)"><i class="el-icon-delete" />删除</div>
                </div>
                <svg-icon v-if="$checkPermission(['JCPZ_ZBPZ_XZZB', 'JCPZ_ZBPZ_BJZB', 'JCPZ_ZBPZ_SCZB'])" slot="reference" class="tree-more" icon-class="more" />
              </el-popover>
            </template>
          </UmConfigTree>
        </el-scrollbar>
      </div>
      <div v-loading="tableLoading" class="g-body__r">
        <um-empty v-if="!currentNodeData.targetName" title="请选择指标" />
        <template v-else>
          <div class="g-body__r--head">
            <span class="m-title">{{ currentNodeData.targetName || '--' }}</span>
          <!-- <el-button type="primary">保存</el-button> -->
          </div>
          <div class="g-body__r--content">
            <el-form>
              <el-form-item label="上级指标：">
                <el-input disabled style="width: 300px;" :value="parentNodeName || '--'" />
              </el-form-item>
              <el-form-item label="是否必答：">
                <el-switch v-model="currentNodeData.reasonRequiredFlag" checked :disabled="!$checkPermission(['JCPZ_ZBPZ_TJZBQT'])" @change="changeRequired" />
              </el-form-item>
              <!-- <div class="u-dashed" /> -->
              <CommonTabs :tabs="tabs" style="margin-bottom: 20px;" @changeTab="changeTab" />
              <Cate :reason-type-code="reasonTypeCode" :tgt-target-id="currentNodeData.tgtTargetId" :current-node-data="currentNodeData" @refresh="refreshReason" />
              <div class="u-dashed" />
              <Reason ref="reason" :reason-type-code="reasonTypeCode" :tgt-target-id="currentNodeData.tgtTargetId" :current-node-data="currentNodeData" />
            </el-form>
          </div>
        </template>
      </div>
    </div>
    <el-dialog
      :title="!editTargetForm.tgtTargetId ? '新增指标' : '编辑指标'"
      :visible.sync="editTargetFormVisible"
      width="500px"
      :close-on-click-modal="false"
      @close="$refs.editTargetForm.resetFields();editTargetForm.targetName=null"
    >
      <el-form ref="editTargetForm" :model="editTargetForm" label-width="80px">
        <el-form-item v-show="!editTargetForm.tgtTargetId && parentTargetName" label="上级指标：">
          <el-input
            disabled
            placeholder="请输入指标名称"
            maxlength="20"
            clearable
            :value="parentTargetName"
          />
        </el-form-item>
        <el-form-item label="指标名称：" prop="targetName" :rules="[{ required: true, message: '请输入指标名称', trigger: ['blur']}]">
          <el-input
            v-model.trim="editTargetForm.targetName"
            placeholder="请输入指标名称"
            maxlength="50"
            clearable
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="editTargetFormVisible=false">取消</el-button>
        <el-button type="primary" @click="confirmEditTarget">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getTargetTree, addTarget, editTarget, deleteTarget, updateTargetSort } from '@/api/base/indicator/index'
import getList from '@/mixins/getList'
import CommonTitle from '@/components/CommonTitle'
import CommonTabs from '../../../components/CommonTabs'
import Reason from './components/reason.vue'
import Cate from './components/cate.vue'
export default {
  name: 'BaseIndicator',
  components: { CommonTitle, CommonTabs, Reason, Cate },
  mixins: [getList],
  data() {
    return {
      reasonTypeCode: 2200071,
      tabs: [{
        label: '满意原因',
        value: 2200071
      }, {
        label: '不满意原因',
        value: 2200072
      }],
      treeData: [], // 指标树
      editTargetFormVisible: false,
      parentTargetName: null, // 新增表单中展示的上级指标
      parentNodeName: null, // 右侧标题展示的上级指标
      currentNodeData: {},
      required: false, // 是否必答
      editTargetForm: {
        parentId: null,
        tgtTargetId: null,
        targetName: null
      },
      sortLoading: false
    }
  },
  created() {
    this.getTargetTree()
  },
  methods: {
    refreshReason() {
      this.$refs.reason.getList()
    },
    // 获取指标树
    getTargetTree() {
      const load = this.$load()
      return getTargetTree({ disableFlag: null }).then(res => {
        this.treeData = res.data || []
        if (this.treeData.length && !this.currentNodeData.tgtTargetId) {
          this.currentNodeData = {
            ...this.treeData[0],
            reasonRequiredFlag: !!this.treeData[0].reasonRequiredFlag
          }
        }
      }).finally(() => load.close())
    },
    editTree(data) {
      this.editTargetFormVisible = true
      this.parentTargetName = data.targetFullName
      this.$nextTick(() => {
        this.editTargetForm.parentId = null
        this.editTargetForm.tgtTargetId = data.tgtTargetId
        this.editTargetForm.targetName = data.targetName
      })
    },
    // 新增/编辑指标
    confirmEditTarget() {
      this.$refs.editTargetForm.validate(valid => {
        if (valid) {
          const load = this.$load()
          const API = this.editTargetForm.tgtTargetId ? editTarget : addTarget
          API(this.editTargetForm).then(res => {
            this.editTargetFormVisible = false
            this.$message.success(res.msg)
            this.getTargetTree()
          }).finally(() => load.close())
        }
      })
    },
    // 删除指标、满意、不满意原因
    deleteTarget(data) {
      this.$confirm('是否确定删除该指标？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteTarget({ tgtTargetId: data.tgtTargetId, tgtTargetReasonId: data.tgtTargetReasonId }).then(res => {
          this.$message.success(res.msg)
          this.getTargetTree()
        }).finally(() => load.close())
      })
    },
    nodeClick(data, path) {
      this.currentNodeData = {
        ...data,
        reasonRequiredFlag: !!data.reasonRequiredFlag
      }
      this.parentNodeName = path.slice(0, -1).join('-')
      this.getList()
    },
    changeTab(data) {
      this.reasonTypeCode = data
    },
    changeRequired() {
      const load = this.$load()
      const { tgtTargetId, targetName, reasonRequiredFlag } = this.currentNodeData
      editTarget({ tgtTargetId, targetName, reasonRequiredFlag: +reasonRequiredFlag }).then(res => {
        this.$message.success(res.msg)
        this.getTargetTree()
      }).finally(() => load.close())
    },
    dropSort({ before, after, inner }) {
      const tgtTargetId = before.data.tgtTargetId
      const toTgtTargetId = after.data.tgtTargetId
      this.sortLoading = true
      updateTargetSort({ tgtTargetId, toTgtTargetId, moveType: inner === 'after' ? 1 : 2 }).then(res => {
        this.$message.success('操作成功')
      }).finally(() => {
        this.getTargetTree()
        this.sortLoading = false
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  background-color: #fff;
  height: 100%;
  display: flex;
  flex-direction: column;
  .m-title {
    color: #333;
    font-size: 16px;
    font-weight: 500;
  }
  .u-dashed {
    width: 100%;
    height: 0;
    border-top: 1px dashed #F5F7FA;
    margin: 20px 0;
  }
  .g-body {
    flex: 1;
    display: flex;
    margin-top: -20px;
    overflow: hidden;
    &__l {
      width: 300px;
      flex-shrink: 0;
      display: flex;
      flex-direction: column;
      box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
      &--head {
        display: flex;
        justify-content: space-between;
        padding: 0 10px 0 20px;
        height: 56px;
        align-items: center;
        i {
          color: $--color-primary;
          cursor: pointer;
          font-size: 18px;
        }
      }
      &--content {
        flex: 1;
        overflow-x: hidden;
      }
    }

    &__r {
      padding-left: 20px;
      padding-right: 20px;
      flex: 1;
      display: flex;
      flex-direction: column;
      overflow-x: hidden;
      &--head {
        height: 56px;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }
      &--content {
        padding-bottom: 20px;
        flex: 1;
        overflow-y: auto;
      }
    }
  }
}
</style>
