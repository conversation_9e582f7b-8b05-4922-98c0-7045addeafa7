<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form label-suffix="：">
        <el-row type="flex" justify="space-between">
          <el-row type="flex">
            <el-form-item label="操作人" label-width="80px" class="mb-0 mr-40">
              <el-input v-model="searchForm.targetName" placeholder="请输入" clearable maxlength="20" />
            </el-form-item>
            <el-form-item label="操作时间" label-width="80px" class="mb-0">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                value-format="yyyy-MM-dd hh-mm-ss"
                range-separator="至"
                clearable
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
          </el-row>
          <el-form-item label="" class="mb-0">
            <el-button :loading="tableLoading" type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <div v-loading="tableLoading" class="g-table_full margin-t-20">
      <um-table-full :data="tableData" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column prop="opeateTime" label="操作时间" width="200px" />
        <el-table-column prop="opeateUser" label="操作人" width="120px" />
        <el-table-column prop="targetName" label="指标名称" width="250px" />
        <el-table-column prop="content" label="操作内容" />
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList('page')"
      />
    </div>
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { getTargetLogList } from '@/api/base/indicator/log'

export default {
  name: 'BaseIndicatorLog',
  mixins: [getList],
  props: {},
  data() {
    return {
      listApi: getTargetLogList,
      dateRange: null,
      searchForm: {
        targetName: null,
        startTime: null,
        endTime: null
      }
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    search() {
      if (this.dateRange) {
        [this.searchForm.startTime, this.searchForm.endTime] = this.dateRange
      } else {
        [this.searchForm.startTime, this.searchForm.endTime] = [null, null]
      }
      this.getList()
    }
  }
}
</script>

  <style lang="scss" scoped>
  .mb-20 {
    margin-bottom: 20px;
  }
  .mr-40 {
    margin-right: 40px;
  }
  .mb-0 {
    margin-bottom: 0;
  }

  .g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .u-tips {
      line-height: 24px;
      background: rgba(#FF9645, 0.1);
      border-radius: 4px;
      color: #FF9645;
      padding-left: 12px;
      font-size: 12px;
      position: relative;
      margin-bottom: 12px;
      font-weight: 500;
      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background: #FF9645 ;
        position: absolute;
        left: 0;
        top: 6px;
      }
    }
  }
  </style>

