<template>
  <div class="g-container">
    <el-form ref="editForm" :model="editForm" label-suffix="：" label-position="top">
      <el-row :gutter="40">
        <el-col :span="12">
          <el-form-item label="渠道类型" required>
            <el-input disabled :value="editForm.channelTypeName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="渠道名称" required>
            <el-input disabled :value="editForm.channelName" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="触点/节点" prop="pointIds" :rules="{ required: true, type: 'array', message: '请选择' }">
            <el-cascader
              v-model="editForm.pointIds"
              :options="pointOptions"
              collapse-tags
              style="width: 100%;"
              filterable
              :props="{
                multiple: true,
                emitPath: false
              }"
              clearable
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="项目范围" prop="scopeCode" :rules="{ required: true, message: '请选择' }">
            <el-row type="flex" align="middle" style="height: 32px;">
              <el-radio-group v-model="editForm.scopeCode">
                <el-radio :label="2200381">全部</el-radio>
                <el-radio :label="2200382">指定项目</el-radio>
              </el-radio-group>
              <el-form-item v-if="editForm.scopeCode === 2200382" ref="projDtos" label="" prop="projDtos" style="margin-bottom: 0!important;margin-left: 10px;flex: 1" :rules="[{ required: true, type: 'array', message: '请选择指定项目' }]">
                <el-select-tree
                  ref="elSelectTree"
                  v-model="editForm.projDtos"
                  is-leaf
                  style="width: 100%"
                  placeholder="请选择项目"
                  :options="organList"
                  clearable
                  filterable
                  :show-all-levels="true"
                  :check-strictly="false"
                  collapse-tags
                  :default-props="{
                    multiple: true,
                    label: 'name',
                    value: 'code',
                  }"
                />
              </el-form-item>
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="m-fixed__btm">
      <el-button plain @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getDetail, updateChannel } from '@/api/base/channel'
import { getThreeLevelOrgan } from '@/api/common'
import { PLAN_RANGE } from '@/enum'
import getPointList from '@/mixins/getPointList'
import { cloneDeep } from 'lodash'
import { getMaxRangeCheckedData, getCheckedNodes, formatProjDtos, computedCheckedNodes } from '../../../plan/satisfactionSurvey/add/utils'
export default {
  name: 'BaseChannelEdit',
  mixins: [getPointList],
  data() {
    return {
      needDeep: true, // 需要深访调研数据，mixins中使用
      PLAN_RANGE,
      editForm: {
        uuid: null,
        scopeCode: null,
        pointIds: [],
        projDtos: []
      },
      organList: [],
      needSpecial: true // 需要专项调研
    }
  },
  async created() {
    const { id } = this.$route.query
    this.getPointOption(false)
    const load = this.$load()
    try {
      await this.getThreeLevelOrgan()
      if (id) {
        this.editForm.uuid = id
        await this.getDetail()
      }
    } catch (error) {
    } finally {
      load.close()
    }
  },
  methods: {
    getDetail() {
      return getDetail({ uuid: this.editForm.uuid }).then(res => {
        const { scopeIds } = res.data
        this.editForm = {
          ...res.data,
          scopeCode: res.data.scopeCode || 2200382,
          projDtos: computedCheckedNodes(scopeIds, this.organList)
        }
      })
    },
    getThreeLevelOrgan() {
      return getThreeLevelOrgan().then(res => {
        this.organList = res.data || []
      })
    },
    save() {
      this.$refs.editForm.validate(valid => {
        if (!valid) { return }
        const arr = this.editForm.scopeCode === 2200382 ? cloneDeep(this.$refs.elSelectTree?.$children[0].getNodes()) : []
        getMaxRangeCheckedData(arr) // 递归处理数据
        const load = this.$load()
        updateChannel({
          ...this.editForm,
          projDtos: formatProjDtos(getCheckedNodes(arr))
        }).then(res => {
          this.$alert(res.msg || '操作成功', '提示', {
            type: 'success',
            showClose: false
          }).then(() => {
            this.$router.back()
          })
        }).finally(() => load.close())
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.g-container {
  background-color: #fff;
  height: calc(100% - 80px);
  padding: 20px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  margin-bottom: 80px;
  overflow-y: auto;
  .m-box {
    background-color: #F5F7FA;
    padding: 4px 12px 0px;
    &__item {
      display: flex;
      &--label {
        margin-right: 30px;
        color: #333;
      }
    }
  }
}
</style>
