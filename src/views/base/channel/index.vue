<template>
  <div v-loading="tableLoading" class="g-container">
    <um-table-full
      :data="tableData"
      :span-method="objectSpanMethod"
      scroll
    >
      <el-table-column
        type="index"
        label="序号"
        width="60"
      />
      <el-table-column label="渠道" class="snzk" align="center">
        <el-table-column
          align="center"
          prop="channelTypeName"
          width="100"
        />
        <el-table-column
          align="center"
          prop="channelName"
          width="140"
        />
      </el-table-column>
      <el-table-column
        prop="face"
        label="触点"
        min-width="200"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.pointNames ? row.pointNames.join('、') : '--' }}
        </template>
      </el-table-column>
      <el-table-column
        label="项目范围"
        min-width="200"
        show-overflow-tooltip
      >
        <template slot-scope="{ row }">
          {{ row.scopeNames && row.scopeNames.length ? row.scopeNames.join('、') : '--' }}
        </template>
      </el-table-column>
      <el-table-column
        prop="updater"
        label="操作人"
        width="120"
        :formatter="$formatterTable"
      />
      <el-table-column
        prop="updateTime"
        label="更新时间"
        width="180"
        :formatter="$formatterTable"
      />
      <el-table-column
        label="操作"
        width="140"
        fixed="right"
      >
        <template slot-scope="{ row }">
          <el-button v-if="$checkPermission(['JCPZ_TSQDSZ_BJ'])" type="text" @click="$router.push('/base/channel/edit?id=' + row.uuid)">编辑</el-button>
          <el-button v-if="$checkPermission(['JCPZ_TSQDSZ_QYZF']) && row.statusCode === STATUS.DISABLE" type="text" @click="enableChannel(row)">启用</el-button>
          <el-button v-if="$checkPermission(['JCPZ_TSQDSZ_QYZF']) && row.statusCode === STATUS.ENABLE" type="text" @click="disableChannel(row)"><span class="danger">作废</span></el-button>
        </template>
      </el-table-column>
    </um-table-full>
  </div>
</template>

<script>
import { getChannelList, disableChannel, enableChannel } from '@/api/base/channel'
const STATUS = {
  ENABLE: 2200372, // 启用
  DISABLE: 2200373 // 启用
}
export default {
  name: 'BaseChannel',
  data() {
    return {
      STATUS,
      tableLoading: false,
      tableData: []
    }
  },
  activated() {
    this.getChannelList()
  },
  methods: {
    getChannelList() {
      this.tableLoading = true
      getChannelList().then(res => {
        this.tableData = res.data || []
        this.computedRowSPan(this.tableData, 'channelTypeCode')
      }).finally(() => {
        this.tableLoading = false
      })
    },
    objectSpanMethod({ row, columnIndex }) {
      if (columnIndex === 1) {
        return {
          rowspan: row.rowspan,
          colspan: 1
        }
      }
    },
    computedRowSPan(arr, key) {
      const length = arr.length
      for (let i = 0; i < length; i++) {
        const item = arr[i]
        item.rowspan = 1
        for (let j = i + 1; j < length; j++) {
          if (item[key] === arr[j][key]) {
            item.rowspan++
          } else {
            i = j - 1
            break
          }
        }
      }
    },
    disableChannel(data) {
      this.$confirm(`是否确定作废该渠道：${data.channelName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        disableChannel({ uuid: data.uuid }).then(res => {
          this.$message.success(res.msg)
          this.getChannelList()
        }).finally(() => load.close())
      })
    },
    enableChannel(data) {
      this.$confirm(`是否确定启用该渠道：${data.channelName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        enableChannel({ uuid: data.uuid }).then(res => {
          this.$message.success(res.msg)
          this.getChannelList()
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  background-color: #fff;
  height: 100%;
  padding: 20px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  display: flex;
  flex-direction: column;
}
::v-deep {
  thead.is-group tr:last-child {
    display: none;
  }
  .el-table thead.is-group th.el-table__cell {
    background-color: #fff;
  }
  .el-table--border th.el-table__cell {
    border-bottom: none;
  }
}
</style>
