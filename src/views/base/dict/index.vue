<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form ref="searchForm" label-width="70px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="8">
            <el-form-item prop="keywords" label="类型">
              <el-input v-model="searchForm.keywords" clearable placeholder="请输入" />
            </el-form-item>
          </el-col>
          <el-col :span="16" class="fr text-right">
            <el-button :loading="loading" type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div v-loading="loading" class="g-table_full margin-t-20">
      <um-table-full
        :data="tableData"
        scroll
      >
        <el-table-column
          label="序号"
          width="80"
          type="index"
        />
        <el-table-column
          label="类型"
          width="180"
        >
          <template slot-scope="scope">
            <el-button type="text" @click="openDialog(scope.row, scope.$index)">{{ scope.row.dictName }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="createUserName"
          label="值"
          min-width="200"
        >
          <template slot-scope="scope">
            {{ scope.row.dictValueList.map(item => item.dictName).join(' / ') }}
          </template>
        </el-table-column>
        <el-table-column
          prop="optUser"
          label="操作人"
          width="160"
        />
        <el-table-column
          prop="optTime"
          label="更新时间"
          width="180"
        />
      </um-table-full>
    </div>
    <el-dialog :visible.sync="dialogVisible" custom-class="flex-body" width="700px" :close-on-click-modal="false" :title="dialogForm.dialogTitle || '--'" @close="openDialogIndex=null">
      <el-row class="f-mg-b20" type="flex" justify="space-between">
        <div class="u-dialog__tip">编辑已有描述会影响现有数据统计，请勿改变原有含义。</div>
        <el-button v-if="$checkPermission(['JCPZ_MYDSJZD_XZ'])" type="primary" icon="el-icon-circle-plus-outline" @click="addItem">新增</el-button>
      </el-row>

      <el-form ref="addForm" :model="dialogForm" class="flex-1 flex-col auto-y">
        <um-table-full
          :data="dialogForm.dialogTableData"
          scroll
        >
          <el-table-column type="index" label="序号" width="80" />
          <el-table-column prop="dictName" label="描述" min-width="140">
            <template slot-scope="scope">
              <span v-show="!scope.row.isEdit">{{ scope.row.dictName }}</span>
              <el-form-item
                v-if="scope.row.isEdit"
                :prop="`dialogTableData[${scope.$index}].dictName`"
                label=""
                :rules="[{ required: true, trigger: 'blur', message: '请输入' }]"
                style="margin-bottom: 0!important;"
              >
                <el-input v-model="scope.row.dictName" placeholder="请输入" maxlength="20" clearable />
              </el-form-item>
            </template>
          </el-table-column>
          <el-table-column
            prop="answer"
            label="是否启用"
            width="120"
          >
            <template slot-scope="scope">
              <el-switch
                :value="scope.row.dictCancelCode === DICT_CODE.ENABLE"
                :disabled="!$checkPermission(['JCPZ_MYDSJZD_QYJY'])"
                @change="changeStatus(scope.row)"
              />
            </template>
          </el-table-column>
          <el-table-column
            prop="answer"
            label="操作"
            width="120"
          >
            <template slot-scope="scope">
              <el-button v-if="$checkPermission(['JCPZ_MYDSJZD_BJ'])" v-show="!scope.row.isEdit" type="text" @click="scope.row.isEdit = true">编辑</el-button>
              <el-button v-show="scope.row.isEdit" type="text" @click="save(scope.row, scope.$index)">保存</el-button>
            </template>
          </el-table-column>
        </um-table-full>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getDictList, addDict, editDict } from '@/api/base/dict'
export default {
  name: 'BaseDict',
  data() {
    return {
      searchForm: {
        keywords: null
      },
      tableData: [],
      searchData: [],
      dialogForm: {
        dialogTableData: [],
        dictCode: null,
        parentDictCode: null,
        dialogTitle: null
      },
      dialogVisible: false,
      DICT_CODE: {
        ENABLE: 1100601,
        DISABLE: 1100602
      },
      loading: false,
      openDialogIndex: null // 当前点开的下标
    }
  },
  created() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getDictList(this.searchForm).then(res => {
        this.tableData = res.data || []
        if (this.openDialogIndex !== null) {
          this.dialogForm.dialogTableData = this.tableData[this.openDialogIndex].dictValueList.map(item => {
            return {
              ...item,
              isEdit: false
            }
          })
        }
      }).finally(() => {
        this.loading = false
      })
    },
    // 前端过滤搜索数据
    filterData() {
      if (!this.tableData.length) this.getList()
      this.searchData = this.tableData.filter(item => {
        return item.dictName.indexOf(this.searchForm.keywords) !== -1 || !this.searchForm.keywords
      })
    },
    openDialog(data, index) {
      this.openDialogIndex = index
      this.dialogForm.dialogTitle = data.dictName
      this.dialogForm.parentDictCode = data.dictCode
      this.dialogForm.dialogTableData = data.dictValueList.map(item => {
        return {
          ...item,
          isEdit: false
        }
      })
      this.dialogVisible = true
    },
    // 向表格中追加一条数据
    addItem() {
      this.dialogForm.dialogTableData.push({
        dictName: null,
        dictCancelCode: this.DICT_CODE.DISABLE,
        isEdit: true,
        dictCode: this.dialogForm.dictCode
      })
    },
    // 1200601:正常 1200602:作废
    changeStatus(data) {
      data.dictCancelCode = data.dictCancelCode === this.DICT_CODE.ENABLE ? this.DICT_CODE.DISABLE : this.DICT_CODE.ENABLE
      // 如果不是编辑状态，直接调用接口
      if (!data.isEdit) {
        const load = this.$load()
        editDict({
          ...data,
          parentDictCode: this.dialogForm.parentDictCode
        }).then(res => {
          this.$message.success('操作成功')
          this.getList()
        }).finally(() => load.close())
      }
    },
    // 保存/编辑
    save(data, index) {
      this.$refs.addForm.validateField(`dialogTableData[${index}].dictName`, async errMsg => {
        if (errMsg) {
          this.$message.warning(errMsg)
          return
        }
        const load = this.$load()
        try {
          if (data.dictId) {
            await editDict({
              ...data,
              parentDictCode: this.dialogForm.parentDictCode
            })
          } else {
            await addDict({
              ...data,
              parentDictCode: this.dialogForm.parentDictCode
            })
          }
          this.$message.success('操作成功')
          this.getList()
          data.isEdit = false
        } catch (error) {

        } finally {
          load.close()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .u-dialog__tip {
    flex: 1;
    line-height: 32px;
    background: #FEF0F0;
    border-radius: 4px;
    color: $--color-danger;
    margin-right: 20px;
    padding-left: 16px;
    position: relative;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #F8716B;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
}
</style>
