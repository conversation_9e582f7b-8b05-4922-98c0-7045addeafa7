<template>
  <div class="g-container">
    <el-form ref="editForm" :model="editForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="触点名称："
            prop="pointName"
            :rules="{ required: true, message: '请输入触点名称' }"
          >
            <el-input v-model="editForm.pointName" placeholder="请输入" clearable maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属阶段：" prop="ownerStageCode">
            <el-select v-model="editForm.ownerStageCode" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in CUSTOMER_LEVEL_LIST" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="触点类型：" prop="pointTypeId">
            <el-select v-model="editForm.pointTypeId" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in pointTypes" :key="item.cfgDictId" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="相关业务条线："
            prop="bizLines"
            :rules="{ required: true, type: 'array', message: '请选择相关业务条线' }"
          >
            <el-checkbox-group v-model="editForm.bizLines" style="min-height: 32px;">
              <el-checkbox v-for="item in businessList" :key="item.dictValue" :label="+item.dictValue">{{ item.dictName }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调研业态：" prop="rule.productTypeIds">
            <el-select-tree
              v-model="editForm.rule.productTypeIds"
              :options="productList"
              filterable
              multiple
              show-checkbox
              clearable
              collapse-tags
              :show-all-levels="false"
              :props="{
                disabled: 'disable', // 这里不需要禁用，所以随便给个字读单
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问卷类型：对客展示" prop="questionnaireTypeId" :rules="{ required: true, message: '请选择' }">
            <el-select v-model="editForm.questionnaireTypeId" placeholder="请选择" style="width: 100%" clearable>
              <el-option v-for="item in questionTypes" :key="item.cfgDictId" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除房屋属性" prop="rule.excludeHouseAttributes">
            <el-select
              v-model="editForm.rule.excludeHouseAttributes"
              placeholder="请选择"
              style="width: 100%"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="item in excludeHouseList"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除经营属性" prop="rule.excludeBusinessAttributes">
            <el-select
              v-model="editForm.rule.excludeBusinessAttributes"
              placeholder="请选择"
              style="width: 100%"
              clearable
              multiple
              collapse-tags
            >
              <el-option v-for="item in excludeBusinessList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目配套：" prop="rule.supportProductTypeIds">
            <el-select-tree
              v-model="editForm.rule.supportProductTypeIds"
              :options="supportList"
              filterable
              clearable
              collapse-tags
              multiple
              show-checkbox
              :show-all-levels="true"
              :props="{
                disabled: 'disable', // 这里不需要禁用，所以随便给个字读单
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="装修类型：" prop="rule.decorationStandard">
            <el-select v-model="editForm.rule.decorationStandard" filterable clearable style="width: 100%" placeholder="请选择">
              <el-option label="毛坯" :value="DECORATION_STATUS.SIMPLE_OUT" />
              <el-option label="精装" :value="DECORATION_STATUS.FINE_OUT" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="对象类型：" prop="rule.customerTypes" :rules="{ required: true, message: '请选择对象类型' }">
            <el-checkbox-group v-model="editForm.rule.customerTypes">
              <el-checkbox v-for="item in customerList" :key="item.value" :label="item.value">{{ item.label }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="次数限制：" prop="rule.frequencyCode" :rules="{ required: true, message: '请选择次数限制' }">
            <el-select v-model="editForm.rule.frequencyCode" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in pointLimitList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="满意度工单责任归属：" prop="orderLine" :rules="{ required: true,trigger: 'blur', message: '请选择' }">
            <el-select v-model="editForm.orderLine" clearable placeholder="请选择" style="width: 50%;">
              <el-option label="客关" :value="1" />
              <el-option label="物业" :value="16" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操盘条线：满足以下任一条件时，推送满意度问卷：" prop="rule.cpRule" :rules="{ required: true, type: 'array', message: '操盘条线不能为空' }">
            <CpLines ref="CpLines" :operate-list="operateList" :init-rule="editForm.rule.cpRule" :init-rule-names="cpRuleNames" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调研时间：满足以下所有条件时，推送满意度问卷：" prop="rule.dateRule" :rules="{ required: true, type: 'array', message: '调研时间不能为空' }">
            <WatchTime ref="WatchTime" :init-rule="editForm.rule.dateRule" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="推送方式：" prop="rule.sendRule.rangeFlag" :rules="[{ required: true, message: '请选择推送方式' }]">
            <el-radio-group v-model="editForm.rule.sendRule.rangeFlag">
              <el-radio :label="1" style="display: flex;align-items: center;margin-bottom: 20px;">
                <el-row type="flex" align="middle">
                  每月问卷于调研周期第
                  <el-form-item prop="rule.sendRule.relativeStartDay1" :rules="[{ required: editForm.rule.sendRule.rangeFlag === 1, message: '请输入' }]" style="margin-bottom: 0!important;">
                    <el-input-number v-model="editForm.rule.sendRule.relativeStartDay1" :min="1" :max="30" :controls="false" style="width: 80px;margin: 0 10px" />
                  </el-form-item>
                  日至结束前
                  <el-form-item prop="rule.sendRule.relativeEndDay" :rules="[{ required: editForm.rule.sendRule.rangeFlag === 1, message: '请输入' }]" style="margin-bottom: 0!important;">
                    <el-input-number v-model="editForm.rule.sendRule.relativeEndDay" :min="1" :max="30" :controls="false" style="width: 80px;margin: 0 10px" />
                  </el-form-item>
                  日随机推送
                </el-row>
              </el-radio>
              <el-radio :label="0" style="display: flex;align-items: center;">
                <el-row type="flex" align="middle">
                  每月问卷于调研周期第
                  <el-form-item prop="rule.sendRule.relativeStartDay2" :rules="[{ required: editForm.rule.sendRule.rangeFlag === 0, message: '请输入' }]" style="margin-bottom: 0!important;">
                    <el-input-number v-model="editForm.rule.sendRule.relativeStartDay2" :min="1" :max="30" :controls="false" style="width: 80px;margin: 0 10px" />
                  </el-form-item>
                  日
                  <el-form-item prop="rule.sendRule.relativeStartHour" :rules="[{ required: editForm.rule.sendRule.rangeFlag === 0, message: '请输入' }]" style="margin-bottom: 0!important;">
                    <el-input-number v-model="editForm.rule.sendRule.relativeStartHour" :min="0" :max="23" :controls="false" style="width: 80px;margin: 0 10px" />
                  </el-form-item>
                  时开始推送
                </el-row>
              </el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="m-fixed__btm">
      <el-button plain @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script>
import { TIME_UNIT, DICT_CODE, SYS_DICT_CODE, DECORATION_STATUS, CUSTOMER_TYPE, POINT_LIMIT, CUSTOMER_LEVEL_LIST } from '@/enum'
import { getSysDictList, getDictList, getProductType, getProjectSupport } from '@/api/common'
import { addNode, updateNode, getNodeDetail } from '@/api/base/surveyRule/editPoint'
import CpLines from './components/CpLines.vue'
import WatchTime from './components/WatchTime.vue'
export default {
  name: 'BasePointEdit',
  components: { CpLines, WatchTime },
  data() {
    return {
      TIME_UNIT,
      DECORATION_STATUS,
      editForm: {
        uuid: null,
        pointName: null,
        ownerStageCode: null,
        pointTypeId: null,
        bizLines: [],
        questionnaireTypeId: null,
        orderLine: null, // 工单所属 1-客关16-物业
        rule: {
          productTypeIds: [],
          supportProductTypeIds: [],
          excludeHouseAttributes: [],
          excludeBusinessAttributes: [],
          decorationStandard: null,
          customerTypes: [],
          frequencyCode: null,
          cpRule: [],
          dateRule: [],
          sendRule: {
            rangeFlag: null, // 是否是范围 1-是 0-否
            relativeStartDay1: undefined, // 开始相对天数【范围】
            relativeStartDay2: undefined, // 开始相对天数【非范围】
            relativeStartHour: undefined, // 开始相对小时
            relativeEndDay: undefined // 截止相对天数
          }
        }
      },
      cpRuleNames: [], // 操盘条线的回显，用于编辑
      CUSTOMER_LEVEL_LIST,
      pointTypes: [], // 触点类型
      businessList: [], // 相关业务条线
      productList: [], // 调研业态
      supportList: [], // 项目配套
      operateList: [], // 操盘条线数据
      excludeHouseList: [], // 剔除房屋属性
      excludeBusinessList: [], // 剔除经营属性
      questionTypes: [], // 问卷类型
      customerList: [// 对象类型数据
        {
          label: '业主',
          value: CUSTOMER_TYPE.OWNER_OUT
        },
        {
          label: '同住人',
          value: CUSTOMER_TYPE.TOGETHER_OUT
        },
        {
          label: '租户',
          value: CUSTOMER_TYPE.TENANT_OUT
        }
      ],
      pointLimitList: [// 次数限制
        {
          label: '不限次数',
          value: POINT_LIMIT.NONE
        },
        {
          label: '仅调研一次（全局）',
          value: POINT_LIMIT.ONECE
        },
        {
          label: '每年一次（全局）',
          value: POINT_LIMIT.YEAR
        }
      ]
    }
  },
  async created() {
    const { id } = this.$route.query
    const load = this.$load()
    try {
      // 获取触点类型数据字典值
      await this.getDictList(DICT_CODE.POINT_TYPE, 'pointTypes')
      // 获取问卷类型数据字典值
      await this.getDictList(DICT_CODE.QUESTION_TYPE, 'questionTypes')
      // 获取相关业务条线数据字典值
      await this.getSysDictList(SYS_DICT_CODE.BUSINESS, 'businessList')
      // 获取操盘条线数据
      await this.getSysDictList(SYS_DICT_CODE.OPERATE, 'operateList')
      await this.getSysDictList(2000080, 'excludeHouseList')
      await this.getSysDictList(2000090, 'excludeBusinessList')
      // 获取调研业态
      await this.getProductType()
      await this.getProjectSupport()
      if (id) {
        this.editForm.uuid = id
        await this.getDetail()
      }
    } catch (error) {

    } finally {
      load.close()
    }
  },
  methods: {
    // 获取系统数据字典值
    getSysDictList(dictCode, key) {
      return getSysDictList({ dictCode }).then(res => {
        this[key] = res.data || []
      })
    },
    // 获取满意度数据字典
    getDictList(dictCode, key) {
      return getDictList({ dictCode }).then(res => {
        this[key] = res.data || []
      })
    },
    // 获取调研业态
    getProductType() {
      return getProductType().then(res => {
        this.productList = res.data || []
      })
    },
    // 获取项目配套
    getProjectSupport() {
      return getProjectSupport().then(res => {
        this.supportList = res.data || []
      })
    },
    getDetail() {
      return getNodeDetail({ uuid: this.editForm.uuid }).then(res => {
        const data = res.data || {}
        const { rule = {}} = data
        this.editForm.pointName = data.pointName
        this.editForm.ownerStageCode = data.ownerStageCode
        this.editForm.pointTypeId = data.pointTypeId
        this.editForm.bizLines = data.bizLines || []
        this.editForm.orderLine = data?.orderLine || ''
        this.editForm.rule.productTypeIds = rule.productTypeIds || []
        this.editForm.rule.supportProductTypeIds = rule.supportProductTypeIds || []
        this.editForm.rule.decorationStandard = rule.decorationStandard
        this.editForm.rule.customerTypes = rule.customerTypes || []
        this.editForm.rule.excludeHouseAttributes = rule.excludeHouseAttributes?.map(item => item.toString()) || []
        this.editForm.rule.excludeBusinessAttributes = rule.excludeBusinessAttributes?.map(item => item.toString()) || []
        this.editForm.rule.frequencyCode = rule.frequencyCode
        this.editForm.rule.cpRule = rule.cpRule || []
        this.cpRuleNames = rule.cpRuleNames || []
        this.editForm.rule.dateRule = rule.dateRule || []
        this.editForm.questionnaireTypeId = data.questionnaireTypeId
        this.editForm.rule.sendRule.rangeFlag = rule.sendRule ? rule.sendRule.rangeFlag : null
        if (this.editForm.rule.sendRule.rangeFlag === 1) {
          this.editForm.rule.sendRule.relativeStartDay1 = rule.sendRule?.relativeStartDay
          this.editForm.rule.sendRule.relativeEndDay = rule.sendRule?.relativeEndDay
        }
        if (this.editForm.rule.sendRule.rangeFlag === 0) {
          this.editForm.rule.sendRule.relativeStartDay2 = rule.sendRule?.relativeStartDay
          this.editForm.rule.sendRule.relativeStartHour = rule.sendRule?.relativeStartHour
        }
      })
    },
    async save() {
      const data = await this.$refs.CpLines.validate()
      if (!data) return
      this.editForm.rule.cpRule = data.map(item => {
        return [...item.ids]
      })
      this.$refs.WatchTime.validate(data => {
        if (data) {
          this.editForm.rule.dateRule = data.map(item => item)
        }
      })
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const load = this.$load()
          const API = this.editForm.uuid ? updateNode : addNode
          if (this.editForm.rule.sendRule.rangeFlag === 1) {
            this.editForm.rule.sendRule.relativeStartDay = this.editForm.rule.sendRule.relativeStartDay1
          }
          if (this.editForm.rule.sendRule.rangeFlag === 0) {
            this.editForm.rule.sendRule.relativeStartDay = this.editForm.rule.sendRule.relativeStartDay2
          }
          API({ ...this.editForm }).then(res => {
            this.$alert(res.msg || '操作成功', '提示', {
              type: 'success',
              showClose: false
            }).then(() => {
              this.$router.back()
            })
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .g-container {
    background-color: #fff;
    height: calc(100% - 80px);
    box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
    border-radius: 12px;
    padding: 20px;
    overflow-y: auto;
    margin-bottom: 80px;
  }
  </style>

