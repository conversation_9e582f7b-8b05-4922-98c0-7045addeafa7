<template>
  <div class="g-container">
    <el-form ref="detail" :model="detail" class="detailForm">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item label="触点名称：">
            {{ detail.pointName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属阶段：" label-width="98px">
            {{ detail.ownerStageName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="触点类型：">
            {{ detail.pointTypeName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="相关业务条线：" label-width="98px">
            {{ detail.bizLineNames.join('、') | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调研业态：">
            {{ detail.rule.productTypeNames.join('、') | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问卷类型：对客展示">
            {{ detail.questionnaireTypeName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除房屋属性：">
            {{ detail.rule.excludeHouseAttributeNames ? (detail.rule.excludeHouseAttributeNames.join('、') || '--') : '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除经营属性：">
            {{ detail.rule.excludeBusinessAttributeNames ? (detail.rule.excludeBusinessAttributeNames.join('、') || '--') : '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="项目配套：" label-width="98px">
            {{ detail.rule.supportProductTypeNames.join('、') | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="装修类型：">
            {{ detail.rule.decorationStandardName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="对象类型：" label-width="98px">
            {{ detail.rule.customerTypeNames.filter(item => item).join('、') | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="次数限制：">
            {{ detail.rule.frequencyName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="满意度工单责任归属：">
            {{ detail.orderLineName | formatterTable }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操盘条线：满足以下任一条件时，推送满意度问卷：">
            <CpLines ref="CpLines" is-detail :init-rule="detail.rule.cpRule" :init-rule-names="cpRuleNames" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="调研时间：满足以下所有条件时，推送满意度问卷：">
            <WatchTime ref="WatchTime" is-detail :init-rule="detail.rule.dateRule" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="推送方式：" prop="rule.sendRule.rangeFlag">
            <template v-if="detail.rule.sendRule.rangeFlag === 1">
              每月问卷于调研周期第{{ detail.rule.sendRule.relativeStartDay1 }}日至结束前{{ detail.rule.sendRule.relativeEndDay }}日随机推送
            </template>
            <template v-else-if="detail.rule.sendRule.rangeFlag === 0">
              每月问卷于调研周期第{{ detail.rule.sendRule.relativeStartDay2 }}日{{ detail.rule.sendRule.relativeStartHour }}时开始推送
            </template>
            <template v-else>--</template>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
  </div>
</template>

<script>
import { getNodeDetail } from '@/api/base/surveyRule/editPoint'
import CpLines from './components/CpLines.vue'
import WatchTime from './components/WatchTime.vue'
export default {
  name: 'BasePointDetail',
  components: { CpLines, WatchTime },
  data() {
    return {
      detail: {
        uuid: null,
        pointName: null,
        ownerStageName: null,
        pointTypeName: null,
        bizLineNames: [],
        rule: {
          productTypeNames: [],
          supportProductTypeNames: [],
          decorationStandardName: null,
          customerTypeNames: [],
          frequencyName: null,
          cpRule: [],
          dateRule: [],
          sendRule: {
            rangeFlag: null, // 是否是范围 1-是 0-否
            relativeStartDay1: undefined, // 开始相对天数【范围】
            relativeStartDay2: undefined, // 开始相对天数【非范围】
            relativeStartHour: undefined, // 开始相对小时
            relativeEndDay: undefined // 截止相对天数
          }
        }
      },
      cpRuleNames: [] // 操盘条线的回显，用于编辑
    }
  },
  async created() {
    const { id } = this.$route.query
    const load = this.$load()
    try {
      if (id) {
        this.detail.uuid = id
        await this.getDetail()
      }
    } catch (error) {

    } finally {
      load.close()
    }
  },
  methods: {
    getDetail() {
      return getNodeDetail({ uuid: this.detail.uuid }).then(res => {
        const data = res.data || {}
        const { rule = {}} = data
        this.detail.pointName = data.pointName
        this.detail.ownerStageName = data.ownerStageName
        this.detail.pointTypeName = data.pointTypeName
        this.detail.questionnaireTypeName = data.questionnaireTypeName
        this.detail.orderLineName = data.orderLineName
        this.detail.bizLineNames = data.bizLineNames || []
        this.detail.rule.productTypeNames = rule.productTypeNames || []
        this.detail.rule.supportProductTypeNames = rule.supportProductTypeNames || []
        this.detail.rule.excludeHouseAttributeNames = rule.excludeHouseAttributeNames || []
        this.detail.rule.excludeBusinessAttributeNames = rule.excludeBusinessAttributeNames || []
        this.detail.rule.decorationStandardName = rule.decorationStandardName
        this.detail.rule.customerTypeNames = rule.customerTypeNames || []
        this.detail.rule.frequencyName = rule.frequencyName
        this.detail.rule.cpRule = rule.cpRule || []
        this.cpRuleNames = rule.cpRuleNames || []
        this.detail.rule.dateRule = rule.dateRule || []
        this.detail.rule.sendRule.rangeFlag = rule.sendRule ? rule.sendRule.rangeFlag : null
        if (this.detail.rule.sendRule.rangeFlag === 1) {
          this.detail.rule.sendRule.relativeStartDay1 = rule.sendRule?.relativeStartDay
          this.detail.rule.sendRule.relativeEndDay = rule.sendRule?.relativeEndDay
        }
        if (this.detail.rule.sendRule.rangeFlag === 0) {
          this.detail.rule.sendRule.relativeStartDay2 = rule.sendRule?.relativeStartDay
          this.detail.rule.sendRule.relativeStartHour = rule.sendRule?.relativeStartHour
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
    background-color: #fff;
    height: 100%;
    box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
    border-radius: 12px;
    padding: 20px;
    overflow-y: auto;
}
</style>
