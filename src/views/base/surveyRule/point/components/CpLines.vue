<template>
  <el-form ref="addForm" :model="addForm">
    <el-table stripe :data="addForm.tableData">
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column label="操盘条线">
        <template slot-scope="scope">
          <el-row type="flex">
            <span style="line-height: 28px;">华发同时操盘：</span>
            <span v-if="!scope.row.isEdit" style="line-height: 32px;">{{ scope.row.name }}</span>
            <el-form-item v-else :prop="`tableData[${scope.$index}].ids`" :rules="[{ required: true, type: 'array',trigger: 'blur', validator: validateRow.bind(this, scope.$index) }]" style="margin-bottom: 4px!important;flex: 1;">
              <el-checkbox-group v-model="scope.row.ids">
                <el-checkbox v-for="item in operateList" :key="item.dictValue" :label="+item.dictValue">{{ item.dictName }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column v-if="!isDetail" label="操作" width="120">
        <template slot-scope="scope">
          <el-button v-if="!scope.row.isEdit" type="text" @click="scope.row.isEdit = true">编辑</el-button>
          <el-button v-else type="text" @click="save(scope.$index, scope.row)">保存</el-button>
          <el-button type="text"><span class="danger" @click="del(scope.$index)">删除</span></el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="addForm.tableData.length < 10 && !isDetail" class="table-btn">
      <el-button type="text" icon="el-icon-circle-plus-outline" @click="appendData">添加</el-button>
    </div>
  </el-form>
</template>

<script>

export default {
  name: 'CpLines',
  props: {
    operateList: {
      type: Array,
      default: () => []
    },
    initRule: {
      type: Array,
      default: () => []
    },
    initRuleNames: {
      type: Array,
      default: () => []
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      addForm: {
        tableData: []
      },
      isSaveAction: false, // 是否是保存动作
      validateRow: (index, rule, value, callback) => {
        if (this.addForm.tableData[index].isEdit && !this.isSaveAction) {
          callback(new Error('请保存'))
        } else if (!value.length && this.isSaveAction) {
          callback(new Error('请选择'))
        } else {
          callback()
        }
      }
    }
  },
  watch: {
    initRule(v) {
      if (v.length && !this.addForm.tableData.length) {
        this.addForm.tableData = v.map((item, index) => {
          return {
            ids: item,
            name: this.initRuleNames[index].join('、'),
            isEdit: false
          }
        })
      }
    }
  },
  methods: {
    appendData() {
      this.addForm.tableData.push({
        ids: [],
        name: null,
        isEdit: true
      })
    },
    save(index, data) {
      this.isSaveAction = true
      this.$refs.addForm.validateField(`tableData[${index}].ids`, errMsg => {
        if (errMsg) return
        const arr = []
        this.operateList.forEach(item => {
          if (data.ids.includes(+item.dictValue)) {
            arr.push(item.dictName)
          }
        })
        data.name = arr.join('、')
        data.isEdit = false
      })
    },
    del(index) {
      this.addForm.tableData.splice(index, 1)
    },
    /**
     * @param {Boolean} required 是否必定要添加至少一条数据
     */
    validate(required = true) {
      this.isSaveAction = false
      return new Promise((resolve) => {
        this.$refs.addForm.validate(valid => {
          if (!valid) return resolve(false)
          const resultArr = this.addForm.tableData.filter(item => !item.isEdit)
          if (!resultArr.length && required) {
            return resolve(false)
          }
          resolve(resultArr)
        })
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.table-btn {
  width: 100%;
  border: 1px solid #f0f3f5;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  border-top: none;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}
::v-deep {
  .el-table.el-table--striped {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
  .el-form-item__content {
    .el-form-item__error {
      margin-top: -12px;
    }
  }
}
</style>
