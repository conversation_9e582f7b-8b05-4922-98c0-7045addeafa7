<template>
  <el-form ref="addForm" :model="addForm" inline>
    <el-table stripe :data="addForm.tableData">
      <el-table-column label="序号" type="index" width="80" />
      <el-table-column label="规则">
        <template slot-scope="scope">
          <el-row type="flex">
            <template v-if="!isDetail">
              <el-form-item
                :prop="`tableData[${scope.$index}].dateTypeCode`"
                :rules="[{ required: true, message: '请选择' }]"
                style="margin-bottom: 0px!important;"
              >
                <el-select v-model="scope.row.dateTypeCode" size="mini" style="margin-right: 10px;">
                  <el-option v-for="item in dateList" :key="item.value" :value="item.value" :label="item.label" />
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="`tableData[${scope.$index}].timeValue`"
                :rules="[{ required: true, message: '请选择' }]"
                style="margin-bottom: 0px!important;"
              >
                <el-input-number v-model="scope.row.timeValue" :min="1" :max="999" :controls="false" size="mini" />
              </el-form-item>
              <el-form-item
                :prop="`tableData[${scope.$index}].timeUnitCode`"
                :rules="[{ required: true, message: '请选择' }]"
                style="margin-bottom: 0px!important;"
              >
                <el-select v-model="scope.row.timeUnitCode" style="width: 90px;margin: 0 10px;" size="mini">
                  <el-option v-for="item in timeList" :key="item.value" :value="item.value" :label="item.label" />
                </el-select>
              </el-form-item>
              <el-form-item
                :prop="`tableData[${scope.$index}].timeDirectionCode`"
                :rules="[{ required: true, message: '请选择' }]"
                style="margin-bottom: 0px!important;"
              >
                <el-select v-model="scope.row.timeDirectionCode" style="width: 90px;" size="mini">
                  <el-option v-for="item in directionList" :key="item.value" :value="item.value" :label="item.label" />
                </el-select>
              </el-form-item>
            </template>
            <template v-else>
              {{ scope.row.dateTypeName }} {{ scope.row.timeValue }} {{ scope.row.timeUnitName }} {{ scope.row.timeDirectionName }}
            </template>
          </el-row>
        </template>
      </el-table-column>
      <el-table-column v-if="!isDetail" label="操作" width="120">
        <template slot-scope="scope">
          <el-button type="text"><span class="danger" @click="del(scope.$index)">删除</span></el-button>
        </template>
      </el-table-column>
    </el-table>
    <div v-if="addForm.tableData.length < 10 && !isDetail" class="table-btn">
      <el-button type="text" icon="el-icon-circle-plus-outline" @click="appendData">添加</el-button>
    </div>
  </el-form>
</template>

<script>
import { WATCH_DATE_TYPE, TIME_UNIT, TIME_DIRECRION } from '@/enum'
export default {
  name: 'CpLines',
  props: {
    initRule: {
      type: Array,
      default: () => []
    },
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      dateList: [
        {
          label: '网签日期',
          value: WATCH_DATE_TYPE.NET
        },
        {
          label: '合同约定交付日期',
          value: WATCH_DATE_TYPE.CONTRACT
        },
        {
          label: '集中交付结束日期',
          value: WATCH_DATE_TYPE.CENTER
        },
        {
          label: '实际收楼日期',
          value: WATCH_DATE_TYPE.REAL
        }
      ],
      timeList: [
        // {
        //   label: '日',
        //   value: TIME_UNIT.DAY
        // },
        {
          label: '月',
          value: TIME_UNIT.MONTH
        }
      ],
      directionList: [
        {
          label: '前',
          value: TIME_DIRECRION.BEFORE
        },
        {
          label: '后',
          value: TIME_DIRECRION.AFTER
        }
      ],
      addForm: {
        tableData: []
      }
    }
  },
  watch: {
    initRule(v) {
      if (v.length && !this.addForm.tableData.length) {
        this.addForm.tableData = v.map((item) => {
          return {
            ...item
          }
        })
      }
    }
  },
  methods: {
    appendData() {
      this.addForm.tableData.push({
        dateTypeCode: null,
        timeValue: undefined,
        timeUnitCode: null,
        timeDirectionCode: null
      })
    },
    del(index) {
      this.addForm.tableData.splice(index, 1)
    },
    validate(cb) {
      this.$refs.addForm.validate(valid => {
        if (!valid) return cb(valid)
        cb(this.addForm.tableData)
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.table-btn {
  width: 100%;
  border: 1px solid #f0f3f5;
  border-bottom-left-radius: 12px;
  border-bottom-right-radius: 12px;
  border-top: none;
  height: 60px;
  display: flex;
  align-items: center;
  justify-content: center;
}
::v-deep {
  .el-table.el-table--striped {
    border-bottom-left-radius: 0;
    border-bottom-right-radius: 0;
  }
}
</style>
