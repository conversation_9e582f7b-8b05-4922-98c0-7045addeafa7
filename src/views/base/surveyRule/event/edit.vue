<template>
  <div class="g-container">
    <el-form ref="editForm" :model="editForm" label-position="top">
      <el-row :gutter="20">
        <el-col :span="12">
          <el-form-item
            label="触点名称："
            prop="pointName"
            :rules="{ required: true, message: '请输入触点名称' }"
          >
            <el-input v-model="editForm.pointName" placeholder="请输入" clearable maxlength="20" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="所属阶段：" prop="ownerStageCode">
            <el-select v-model="editForm.ownerStageCode" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in CUSTOMER_LEVEL_LIST" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="触点类型：" prop="pointTypeId">
            <el-select v-model="editForm.pointTypeId" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in pointTypes" :key="item.cfgDictId" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item
            label="相关业务条线："
            prop="bizLines"
            :rules="{ required: true, type: 'array', message: '请选择相关业务条线' }"
          >
            <el-checkbox-group v-model="editForm.bizLines">
              <el-checkbox v-for="item in businessList" :key="item.dictValue" :label="+item.dictValue">{{ item.dictName }}</el-checkbox>
            </el-checkbox-group>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="调研业态：" prop="rule.productTypeIds">
            <el-select-tree
              v-model="editForm.rule.productTypeIds"
              :options="productList"
              filterable
              multiple
              show-checkbox
              clearable
              collapse-tags
              :show-all-levels="false"
              :props="{
                disabled: 'disable', // 这里不需要禁用，所以随便给个字读单
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除房屋属性" prop="rule.excludeHouseAttributes">
            <el-select
              v-model="editForm.rule.excludeHouseAttributes"
              placeholder="请选择"
              style="width: 100%"
              clearable
              multiple
              collapse-tags
            >
              <el-option
                v-for="item in excludeHouseList"
                :key="item.dictValue"
                :label="item.dictName"
                :value="item.dictValue"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="剔除经营属性" prop="rule.excludeBusinessAttributes">
            <el-select
              v-model="editForm.rule.excludeBusinessAttributes"
              placeholder="请选择"
              style="width: 100%"
              clearable
              multiple
              collapse-tags
            >
              <el-option v-for="item in excludeBusinessList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="问卷类型：对客展示" prop="questionnaireTypeId" :rules="{ required: true, message: '请选择' }">
            <el-select v-model="editForm.questionnaireTypeId" placeholder="请选择" style="width: 100%" clearable>
              <el-option v-for="item in questionTypes" :key="item.cfgDictId" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="满意度工单责任归属：" prop="orderLine" :rules="{ required: true,trigger: 'blur', message: '请选择' }">
            <el-select v-model="editForm.orderLine" clearable placeholder="请选择" style="width: 100%;">
              <el-option label="客关" :value="1" />
              <el-option label="物业" :value="16" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="操盘条线：满足以下任一条件时，推送满意度问卷：">
            <CpLines ref="CpLines" :operate-list="operateList" :init-rule="editForm.rule.cpRule" :init-rule-names="cpRuleNames" />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item
            label="调研规则："
            prop="rule"
            :rules="{ required: true, message: '请配置调研规则' }"
          >
            <el-row type="flex">
              收到触点动作后
              <el-form-item prop="rule.sendRule.immediateFlag" :rules="{ required: true, message: '请选择' }">
                <el-select v-model="editForm.rule.sendRule.immediateFlag" placeholder="请选择" style="width: 80px;margin: 0 8px">
                  <el-option label="延迟" :value="0" />
                  <el-option label="立即" :value="1" />
                </el-select>
              </el-form-item>
              <template v-if="editForm.rule.sendRule.immediateFlag === 0">
                <el-form-item prop="rule.sendRule.startTimeValue" :rules="{ required: true, message: '请输入', trigger: 'blur' }">
                  <el-input-number v-model="editForm.rule.sendRule.startTimeValue" :controls="false" placeholder="请输入" :min="1" :max="60" maxlength="50" style="width: 80px;" />
                </el-form-item>
                <el-form-item prop="rule.sendRule.startTimeUnitCode" :rules="{ required: true, message: '请选择' }">
                  <el-select v-model="editForm.rule.sendRule.startTimeUnitCode" placeholder="请选择" style="width: 80px;margin: 0 8px" @change="startChange">
                    <el-option label="分钟" :value="TIME_UNIT.MINUTE" />
                    <el-option label="小时" :value="TIME_UNIT.HOUR" />
                    <el-option label="天" :value="TIME_UNIT.DAY" />
                  </el-select>
                </el-form-item>
                至
                <el-form-item prop="rule.sendRule.endTimeValue" :rules="{ required: true, message: '请输入', trigger: 'blur' }">
                  <el-input-number v-model="editForm.rule.sendRule.endTimeValue" :controls="false" placeholder="请输入" :min="1" :max="60" maxlength="50" style="width: 80px;margin: 0 8px" />
                </el-form-item>
                <el-form-item prop="rule.sendRule.endTimeUnitCode" :rules="{ required: true, message: '请选择' }">
                  <el-select v-model="editForm.rule.sendRule.endTimeUnitCode" placeholder="请选择" style="width: 80px;margin-right: 8px" @change="endChange">
                    <el-option label="分钟" :value="TIME_UNIT.MINUTE" />
                    <el-option label="小时" :value="TIME_UNIT.HOUR" />
                    <el-option label="天" :value="TIME_UNIT.DAY" />
                  </el-select>
                </el-form-item>
                随机时间，
              </template>发起调研
            </el-row>
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <div class="m-fixed__btm">
      <el-button plain @click="$router.back()">取消</el-button>
      <el-button type="primary" @click="save">保存</el-button>
    </div>
  </div>
</template>

<script>
import { getEventDetail, updateEvent } from '@/api/base/surveyRule/updateEvent'
import { getSysDictList, getDictList, getProductType } from '@/api/common'
import { TIME_UNIT, CUSTOMER_LEVEL_LIST, DICT_CODE, SYS_DICT_CODE } from '@/enum'
import CpLines from '../point/components/CpLines.vue'
export default {
  name: 'BaseEventEdit',
  components: { CpLines },
  data() {
    return {
      TIME_UNIT,
      CUSTOMER_LEVEL_LIST,
      pointTypes: [], // 触点类型
      businessList: [], // 相关业务条线
      productList: [], // 调研业态
      operateList: [], // 操盘条线数据
      cpRuleNames: [], // 操盘条线的回显，用于编辑
      questionTypes: [], // 问卷类型
      editForm: {
        uuid: null,
        ownerStageCode: null,
        pointName: null,
        pointTypeId: null,
        questionnaireTypeId: null,
        bizLines: [],
        orderLine: null, // 工单所属 1-客关16-物业
        rule: {
          cpRule: [],
          productTypeIds: [],
          excludeHouseAttributes: [],
          excludeBusinessAttributes: [],
          sendRule: {
            immediateFlag: null,
            startTimeValue: undefined,
            startTimeUnitCode: null,
            endTimeValue: undefined,
            endTimeUnitCode: null
          }

        }
      },
      excludeHouseList: [],
      excludeBusinessList: []
    }
  },
  created() {
    this.editForm.uuid = this.$route.query.id
    this.getDetail()
    // 获取触点类型数据字典值
    this.getDictList(DICT_CODE.POINT_TYPE, 'pointTypes')
    // 获取问卷类型数据字典值
    this.getDictList(DICT_CODE.QUESTION_TYPE, 'questionTypes')
    // 获取相关业务条线数据字典值
    this.getSysDictList(SYS_DICT_CODE.BUSINESS, 'businessList')
    // 获取操盘条线数据
    this.getSysDictList(SYS_DICT_CODE.OPERATE, 'operateList')
    this.getSysDictList(2000080, 'excludeHouseList')
    this.getSysDictList(2000090, 'excludeBusinessList')
    this.getProductType()
  },
  methods: {
    getSysDictList(dictCode, key) {
      getSysDictList({ dictCode }).then(res => {
        this[key] = res.data || []
      })
    },
    // 获取满意度数据字典
    getDictList(dictCode, key) {
      return getDictList({ dictCode }).then(res => {
        this[key] = res.data || []
      })
    },
    // 获取调研业态
    getProductType() {
      return getProductType().then(res => {
        this.productList = res.data || []
      })
    },
    startChange(code) {
      this.editForm.rule.sendRule.endTimeUnitCode = code
    },
    endChange(code) {
      this.editForm.rule.sendRule.startTimeUnitCode = code
    },
    getDetail() {
      const load = this.$load()
      getEventDetail({ uuid: this.editForm.uuid }).then(res => {
        const data = res.data || {}
        this.editForm.pointName = data.pointName
        this.editForm.ownerStageCode = data.ownerStageCode
        this.editForm.pointTypeId = data.pointTypeId
        this.editForm.orderLine = data?.orderLine || ''
        this.editForm.questionnaireTypeId = data.questionnaireTypeId
        this.cpRuleNames = data.rule?.cpRuleNames || []
        data.bizLines && data.bizLines.length && (this.editForm.bizLines = data.bizLines)
        this.editForm.rule = {
          cpRule: data.rule?.cpRule || [],
          productTypeIds: data.rule?.productTypeIds || [],
          excludeHouseAttributes: data.rule?.excludeHouseAttributes?.map(item => item.toString()) || [],
          excludeBusinessAttributes: data.rule?.excludeBusinessAttributes?.map(item => item.toString()) || [],
          sendRule: {
            immediateFlag: data.rule?.sendRule?.immediateFlag,
            startTimeValue: data.rule?.sendRule?.startTimeValue || undefined,
            endTimeValue: data.rule?.sendRule?.endTimeValue || undefined,
            startTimeUnitCode: data.rule?.sendRule?.startTimeUnitCode,
            endTimeUnitCode: data.rule?.sendRule?.endTimeUnitCode
          }

        }
      }).finally(() => load.close())
    },
    async save() {
      const data = await this.$refs.CpLines.validate(false)
      if (!data) return
      this.editForm.rule.cpRule = data.map(item => {
        return [...item.ids]
      })
      this.$refs.editForm.validate(valid => {
        if (valid) {
          if (this.editForm.rule.sendRule.startTimeValue >= this.editForm.rule.sendRule.endTimeValue) {
            this.$message.warning('开始时间需要低于结束时间！')
            return
          }
          const load = this.$load()
          updateEvent({ ...this.editForm }).then(res => {
            this.$message.success(res.msg)
            this.$router.back()
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  background-color: #fff;
  height: calc(100% - 80px);
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  border-radius: 12px;
  padding: 20px;
  overflow-y: auto;
  margin-bottom: 80px;
}
</style>
