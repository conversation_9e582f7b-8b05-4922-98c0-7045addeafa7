<template>
  <div class="g-container">
    <el-form class="detailForm">
      <el-card shadow="never">
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="12">
            <el-form-item label="触点名称：">
              <span>{{ detail.pointName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="所属阶段：">
              <span>{{ detail.ownerStageName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触点类型：">
              <span>{{ detail.pointTypeName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="相关业务条线：">
              <span>{{ detail.bizLineNames.join('、') || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研业态：">
              {{ detail.rule.productTypeNames.join('、') | formatterTable }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="剔除房屋属性：">
              {{ detail.rule.excludeHouseAttributeNames ? (detail.rule.excludeHouseAttributeNames.join('、') || '--') : '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="剔除经营属性：">
              {{ detail.rule.excludeBusinessAttributeNames ? (detail.rule.excludeBusinessAttributeNames.join('、') || '--') : '--' }}
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="问卷类型：对客展示">
              {{ detail.questionnaireTypeName | formatterTable }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="满意度工单责任归属：">
              {{ detail.orderLineName | formatterTable }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="操盘条线：满足以下任一条件时，推送满意度问卷：">
              <CpLines ref="CpLines" is-detail :init-rule="detail.rule.cpRule" :init-rule-names="cpRuleNames" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研规则：">
              <template v-if="detail.rule.sendRule.immediateFlag === 0">
                收到触点动作后延迟 {{ detail.rule.sendRule.startTimeValue | formatterTable }} {{ detail.rule.sendRule.startTimeUnitName | formatterTable }} 至 {{ detail.rule.sendRule.endTimeValue | formatterTable }} {{ detail.rule.sendRule.endTimeUnitName | formatterTable }} 随机时间，发起调研
              </template>
              <template v-else-if="detail.rule.sendRule.immediateFlag === 1">
                收到触点动作后立即发起调研
              </template>
              <template v-else>--</template>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { getEventDetail } from '@/api/base/surveyRule/updateEvent'
import CpLines from '../point/components/CpLines.vue'
export default {
  name: 'BaseEventDetail',
  components: { CpLines },
  data() {
    return {
      detail: {
        pointName: null,
        pointTypeName: null,
        ownerStageName: null,
        bizLineNames: [],
        rule: {
          sendRule: {},
          productTypeNames: [],
          cpRule: []
        }
      },
      cpRuleNames: [] // 操盘条线的回显，用于编辑
    }
  },
  created() {
    const { id } = this.$route.query
    this.getDetail(id)
  },
  methods: {
    getDetail(uuid) {
      const load = this.$load()
      getEventDetail({ uuid }).then(res => {
        const data = res.data || {}
        const obj = {
          ...data
        }
        obj.rule.productTypeNames = data.rule?.productTypeNames || []
        obj.rule.cpRule = data.rule?.cpRule || []
        this.cpRuleNames = data.rule?.cpRuleNames || []
        this.detail = obj
      }).finally(() => load.close())
    }

  }
}
</script>
