<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form label-suffix="：">
        <el-row type="flex" justify="space-between">
          <el-row type="flex">
            <el-form-item label="触点/节点" label-width="80px" class="mb-0 mr-40">
              <el-input v-model="searchForm.pointName" placeholder="请输入" clearable />
            </el-form-item>
          </el-row>
          <el-form-item label="" class="mb-0">
            <el-button :loading="loading" type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            <el-button v-if="$checkPermission(['JCPZ_DYGZSZ_XZJD'])" type="primary" icon="el-icon-circle-plus-outline" @click="$router.push('/base/surveyRule/point/add')">新增节点</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <div v-loading="loading" class="g-table_full margin-t-20">
      <div class="u-tips">提示：该排序用于统计报表中”触点/节点“的显示先后顺序。</div>
      <um-table-full :data="tableData" scroll>
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="pointName" label="触点/节点" width="200" />
        <el-table-column prop="pointTypeName" label="类型" width="140px" :formatter="$formatterTable" />
        <el-table-column prop="ownerStageName" label="所属阶段" width="140px" :formatter="$formatterTable" />
        <el-table-column prop="bizLineNames" label="相关业务条线" min-width="200px">
          <div slot-scope="{row}">
            {{ row.bizLineNames ? row.bizLineNames.join('、') : '--' }}
          </div>
        </el-table-column>
        <el-table-column prop="questionnaireTypeName" label="问卷类型" width="140px" :formatter="$formatterTable" />
        <el-table-column prop="disableFlag" label="状态" width="80px">
          <div slot-scope="{row}">
            {{ row.disableFlag ? '禁用' : '启用' }}
          </div>
        </el-table-column>
        <el-table-column prop="updater" label="操作人" width="120px" :formatter="$formatterTable" />
        <el-table-column prop="updateTime" label="更新时间" width="200px" :formatter="$formatterTable" />
        <el-table-column width="160px" fixed="right" label="操作">
          <template slot-scope="scope">
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_SYXY']) && scope.$index !== 0"
              type="text"
              @click="sortPoint(scope.$index, 'up')"
            >
              上移
            </el-button>
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_SYXY']) && scope.$index !== tableData.length - 1"
              type="text"
              @click="sortPoint(scope.$index, 'down')"
            >
              下移
            </el-button>
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_XQ'])"
              type="text"
              @click="goDetail(scope.row)"
            >
              详情
            </el-button>
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_BJ'])"
              type="text"
              @click="edit(scope.row)"
            >
              编辑
            </el-button>
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_QYJY']) && scope.row.disableFlag"
              type="text"
              @click="operate(scope.row)"
            >
              启用
            </el-button>
            <el-button
              v-if="$checkPermission(['JCPZ_DYGZSZ_QYJY']) && !scope.row.disableFlag"
              type="text"
              @click="operate(scope.row)"
            >
              <span class="danger">禁用</span>
            </el-button>
          </template>
        </el-table-column>
      </um-table-full>
    </div>
  </div>
</template>

<script>
import { getPointList, sortPoint, setPointEnable, setPointDisable } from '@/api/base/surveyRule'
import { POINT_RULE } from '@/enum'

export default {
  name: 'BaseSurveyRule',
  props: {},
  data() {
    return {
      dateRange: null,
      searchForm: {
        pointName: null
      },
      tableData: [],
      loading: false
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    getList() {
      this.loading = true
      getPointList(this.searchForm).then(res => {
        this.tableData = res.data || []
      }).finally(() => {
        this.loading = false
      })
    },
    // 上下移动
    sortPoint(index, sort) {
      const currentData = this.tableData[index]
      let sortUUids = []
      switch (sort) {
        case 'up': sortUUids = [currentData.uuid, this.tableData[index - 1].uuid]; break
        case 'down': sortUUids = [this.tableData[index + 1].uuid, currentData.uuid]; break
      }
      const load = this.$load()
      sortPoint({ uuids: sortUUids }).then(res => {
        this.$message.success(res.msg)
        this.getList()
      }).finally(() => load.close())
    },
    // 启用。禁用
    operate(data) {
      this.$confirm(`是否确定${data.disableFlag ? '启用' : '禁用'}该触点/节点：${data.pointName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        const API = data.disableFlag ? setPointEnable : setPointDisable
        API({ uuid: data.uuid }).then(res => {
          this.$message.success(res.msg)
          this.getList()
        }).finally(() => load.close())
      })
    },
    edit(row) {
      switch (row.ruleCode) {
        case POINT_RULE.EVENT: this.$router.push('/base/surveyRule/event/edit?id=' + row.uuid); break
        case POINT_RULE.POINT: this.$router.push('/base/surveyRule/point/edit?id=' + row.uuid); break
      }
    },
    goDetail(row) {
      switch (row.ruleCode) {
        case POINT_RULE.EVENT: this.$router.push('/base/surveyRule/event/detail?id=' + row.uuid); break
        case POINT_RULE.POINT: this.$router.push('/base/surveyRule/point/detail?id=' + row.uuid); break
      }
    }
  }
}
</script>

  <style lang="scss" scoped>
  .mb-20 {
    margin-bottom: 20px;
  }
  .mr-40 {
    margin-right: 40px;
  }
  .mb-0 {
    margin-bottom: 0;
  }

  .g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .u-tips {
      line-height: 24px;
      background: rgba(#FF9645, 0.1);
      border-radius: 4px;
      color: #FF9645;
      padding-left: 12px;
      font-size: 12px;
      position: relative;
      margin-bottom: 12px;
      font-weight: 500;
      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background: #FF9645 ;
        position: absolute;
        left: 0;
        top: 6px;
      }
    }
  }
  </style>

