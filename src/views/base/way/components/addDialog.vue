<template>
  <div>
    <el-dialog
      :title="cmMaintainWayId ? '修改维系方式': '新增维系方式'"
      :visible.sync="asyncVisible"
      width="520px"
      @close="$refs.submitForm.resetFields();$emit('cancel')"
    >
      <el-form ref="submitForm" :model="submitForm" label-width="70px" action="javascript:void(0);">
        <el-form-item ref="maintainWay" label="维系方式" prop="maintainWay" :rules="[{ required: true, message: '请输入维系方式', trigger: ['blur']}]">
          <el-input
            v-model.trim="submitForm.maintainWay"
            placeholder="请输入维系方式"
            maxlength="50"
            clearable
          />
        </el-form-item>
        <el-form-item ref="remark" label="说明" prop="remark" class="remark">
          <el-input
            v-model="submitForm.remark"
            placeholder="请输入说明"
            type="textarea"
            :rows="4"
            maxlength="200"
            show-word-limit
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="asyncVisible=false">取消</el-button>
        <el-button type="primary" @click="submitHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>
<script>
import { addMaintainWay, updateMaintainWay } from '@/api/configuration'
export default {
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    name: {
      type: [String],
      default: ''
    },
    title: {
      type: [String],
      default: '新增维系方式'
    },
    cmMaintainWayId: {
      type: [Number, String],
      default: null
    },
    addForm: {
      type: [Object],
      default: () => {}
    }
  },
  data() {
    return {
      options: []
    }
  },
  computed: {
    asyncVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    submitForm() {
      return this.addForm
    }
  },
  watch: {
    // 'asyncVisible'(val) {
    //   if (val) {
    //     if (this.cmMaintainWayId) {
    //       this.submitForm = this.addForm
    //     } else {
    //       this.submitForm = {
    //         remark: null,
    //         maintainWay: null
    //       }
    //     }
    //   }
    // }
  },
  methods: {
    submitHandle() {
      this.$refs.submitForm.validate((valid) => {
        if (valid) {
          let METHODS = null
          if (this.cmMaintainWayId) {
            METHODS = updateMaintainWay
          } else {
            METHODS = addMaintainWay
          }
          const load = this.$load()
          METHODS({
            ...this.submitForm,
            cmMaintainWayId: this.cmMaintainWayId
          }).then(res => {
            load.close()
            this.$message.success(res.msg)
            this.$emit('change')
            this.asyncVisible = false
          }).catch(e => {
            load.close()
            this.$errorHandle(e)
          })
        }
      })
    }
  }
}
</script>
