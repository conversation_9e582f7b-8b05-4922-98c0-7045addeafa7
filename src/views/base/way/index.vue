<template>
  <div class="g-container">
    <el-card>
      <el-form ref="searchForm" :model="searchForm" label-width="70px">
        <el-row :gutter="30">
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="maintainWay" label="维系方式">
              <el-input v-model="searchForm.maintainWay" clearable placeholder="请输入维系方式" />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="timeList"
                clearable
                type="daterange"
                range-separator="至"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="请选择日期"
                end-placeholder="请选择日期"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6" class="fr text-right mb-20">
            <el-button :loading="tableLoading" type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            <el-button v-permission="['97060301']" type="primary" icon="el-icon-circle-plus-outline" @click="addHandle">新增</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card v-loading="tableLoading" class="g-container__btm mt-20">
      <el-table
        :data="tableData"
        border
        scroll
        style="width: 100%;"
      >
        <el-table-column prop="xh" label="序号" />
        <el-table-column
          prop="maintainWay"
          label="维系方式"
          :formatter="$formatterTable"
        />
        <el-table-column
          label="说明"
          prop="remark"
          :formatter="$formatterTable"
        >
          <template slot-scope="scope">
            <div class="row-two" :title="scope.row.remark">{{ scope.row.remark }}</div>
          </template>
        </el-table-column>
        <el-table-column
          prop="createUserName"
          label="创建人"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="createTime"
          label="操作"
          fixed="right"
          width="130"
        >
          <template slot-scope="scope">
            <el-button v-permission="['97060302']" type="text" @click="editHandle(scope.row)">编辑</el-button>
            <el-button v-permission="['97060303']" type="text" style="color: #FF6161" @click="delHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        style="margin-top: 20px;margin-bottom: 20px"
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        @pagination="getList('page')"
      />
    </el-card>
    <AddDialog :visible.sync="showAdd" :cm-maintain-way-id="cmMaintainWayId" :add-form="addForm" @cancel="addForm={}" @change="getList('page')" />
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import AddDialog from './components/addDialog.vue'
import { pageMaintainWay, deleteMaintainWay } from '@/api/configuration'
export default {
  name: 'BaseWay',
  components: { AddDialog },
  mixins: [getList],
  data() {
    return {
      listApi: pageMaintainWay,
      tableLoading: false,
      showAdd: false,
      searchForm: {
        maintainWay: null,
        createStartTime: null,
        createEndTime: null,
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      addForm: {},
      tableData: [],
      timeList: [],
      cmMaintainWayId: null
    }
  },
  watch: {
    'timeList'(val) {
      if (val) {
        this.searchForm.createStartTime = val[0]
        this.searchForm.createEndTime = val[1]
      } else {
        this.searchForm.createStartTime = null
        this.searchForm.createEndTime = null
      }
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    addHandle() {
      this.showAdd = true
      this.cmMaintainWayId = null
      this.addForm = {
        maintainWay: null,
        remark: null
      }
    },
    // 编辑
    editHandle(row) {
      this.showAdd = true
      this.cmMaintainWayId = row.cmMaintainWayId
      this.addForm = {
        maintainWay: row.maintainWay,
        remark: row.remark
      }
    },
    // 删除
    delHandle(row) {
      this.$confirm('确定要删除该维系方式吗?', '提示', {
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteMaintainWay({ cmMaintainWayId: row.cmMaintainWayId }).then((d) => {
          this.$message.success('删除成功!')
          this.getList()
          load.close()
        }).catch((e) => {
          load.close()
          this.$errorHandle(e)
        })
      }).catch(() => {
        this.$message.info('已取消删除!')
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .el-card__body {
      padding-bottom: 0;
    }
  }
  &__btm {
    flex: 1;
    overflow-y: auto;
    ::v-deep .el-card__body {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
  ::v-deep.el-date-editor .el-range-separator {
    width: 10%;
  }
}
</style>
