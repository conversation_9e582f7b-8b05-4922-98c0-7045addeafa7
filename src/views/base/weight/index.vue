<template>
  <div v-loading="tableLoading1" class="g-container">
    <el-card class="card-pd0">
      <el-form label-suffix="：">
        <el-row type="flex" justify="space-between">
          <el-row type="flex">
            <el-form-item label="版本名称" label-width="80px" class="mb-0 mr-40">
              <el-input v-model="searchForm1.pointWeightName" placeholder="请输入版本名称" clearable />
            </el-form-item>
            <el-form-item label="生效时段" label-width="80px" class="mb-0">
              <el-date-picker
                v-model="dateRange"
                type="monthrange"
                value-format="yyyy-MM"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
              />
            </el-form-item>
          </el-row>
          <el-form-item label="" class="mb-0">
            <el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <div class="g-table_full margin-t-20">
      <div class="u-tips">提示：未配置权重规则的月份，系统默认按照触点通过样框量加权计算总体满意度。</div>
      <el-row type="flex" justify="space-between" class="mb-20">
        <div>
          <CommonTabs :tabs="tabs" @changeTab="changeTab" />
        </div>
        <el-button
          v-permission="['9701010101']"
          type="primary"
          icon="el-icon-circle-plus-outline"
          @click="$router.push('/base/weight/add')"
        >
          新增版本
        </el-button>
      </el-row>
      <um-table-full :data="tableData1" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column prop="pointWeightName" label="版本名称">
          <template slot-scope="{ row }">
            <span class="row_2" :title="row.pointWeightName">{{ row.pointWeightName }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="effectStartTime" label="生效开始月份" width="140px" />
        <el-table-column prop="effectEndTime" label="生效结束月份" width="140px" />
        <el-table-column prop="createUser" label="创建人" width="120px" />
        <el-table-column prop="createTime" label="创建时间" width="200px" />
        <el-table-column width="160px" label="操作">
          <template slot-scope="{ row }">
            <el-button
              v-if="[2300011, 2300012].includes(searchForm1.statusCode)"
              v-permission="['9701010102']"
              type="text"
              @click="edit(row.stdPointWeightId)"
            >
              编辑
            </el-button>
            <el-button
              v-if="[2300011].includes(searchForm1.statusCode)"
              v-permission="['9701010104']"
              type="text"
              @click="operate(row.stdPointWeightId, row.pointWeightName, 2300012)"
            >
              发布
            </el-button>
            <el-button
              v-if="[2300011].includes(searchForm1.statusCode)"
              v-permission="['9701010104']"
              type="text"
              class="danger"
              @click="operate(row.stdPointWeightId, row.pointWeightName, 1)"
            >
              删除
            </el-button>
            <el-button
              v-if="[2300012, 2300013].includes(searchForm1.statusCode)"
              v-permission="['9701010105']"
              type="text"
              class="danger"
              @click="operate(row.stdPointWeightId, row.pointWeightName, 2300015)"
            >
              作废
            </el-button>
            <el-button
              v-if="[2300014, 2300015].includes(searchForm1.statusCode)"
              v-permission="['9701010105']"
              type="text"
              @click="operate(row.stdPointWeightId, row.pointWeightName)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList1('page')"
      />
    </div>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '../../../components/CommonTabs'
import { getPointWeightPage, changeStatusCode } from '@/api/base/weight'

export default {
  name: 'BaseWeight',
  components: { CommonTabs },
  mixins: [getList1],
  props: {},
  data() {
    return {
      listApi1: getPointWeightPage,
      dateRange: null,
      searchForm1: {
        pointWeightName: null,
        effectStartTime: null,
        effectEndTime: null,
        statusCode: 2300011
      },
      tableCount1: {},
      tabs: [
        {
          label: '草稿',
          value: 2300011,
          count: 0,
          key: 'draft'
        },
        {
          label: '已发布',
          value: 2300012,
          count: 0,
          key: 'publish'
        },
        {
          label: '生效中',
          value: 2300013,
          count: 0,
          key: 'active'
        },
        {
          label: '已过期',
          value: 2300014,
          count: 0,
          key: 'expired'
        },
        {
          label: '已作废',
          value: 2300015,
          count: 0,
          key: 'cancellation'
        }
      ]
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    tableCallBack1() {
      this.tabs.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    search() {
      if (this.dateRange) {
        [this.searchForm1.effectStartTime, this.searchForm1.effectEndTime] = this.dateRange
      } else {
        [this.searchForm1.effectStartTime, this.searchForm1.effectEndTime] = [null, null]
      }
      this.getList1()
    },
    operate(id, name, statusCode) {
      let tip = ''
      switch (statusCode) {
        case 1: tip = '删除'; break
        case 2300012: tip = '发布'; break
        case 2300015: tip = '作废'; break
      }
      this.$confirm('是否确定' + tip + '权重：' + name, '提示', {
        confirmButtonText: '确定',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            changeStatusCode({ stdPointWeightId: id, statusCode }).then(() => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    edit(id) {
      this.$router.push('/base/weight/add?id=' + id)
    }
  }
}
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}
.mr-40 {
  margin-right: 40px;
}
.mb-0 {
  margin-bottom: 0;
}

.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .u-tips {
    line-height: 24px;
    background: rgba(#FF9645, 0.1);
    border-radius: 4px;
    color: #FF9645;
    padding-left: 12px;
    font-size: 12px;
    position: relative;
    margin-bottom: 12px;
    font-weight: 500;
    &::before {
      content: '';
      width: 2px;
      height: 12px;
      background: #FF9645 ;
      position: absolute;
      left: 0;
      top: 6px;
    }
  }
}
</style>

