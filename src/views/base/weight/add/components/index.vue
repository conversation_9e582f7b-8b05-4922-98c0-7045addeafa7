<template>
  <el-form ref="searchForm" :model="searchForm" label-suffix="：">
    <el-row :gutter="20">
      <el-col :span="6">
        <el-form-item
          label="版本名称"
          label-width="80px"
          class="mb-0"
          prop="pointWeightName"
          :rules="[{
            required: true,
            message: '请输入版本名称',
            trigger: 'blur'
          }]"
        >
          <el-input v-model="searchForm.pointWeightName" placeholder="请输入问卷名称" clearable />
        </el-form-item>
      </el-col>
      <el-col :span="6">
        <el-form-item
          label="生效月份"
          label-width="80px"
          class="mb-0"
          prop="dateRange"
          :rules="[{
            required: true,
            message: '请选择生效月份',
            trigger: 'change'
          }]"
        >
          <el-date-picker
            v-model="searchForm.dateRange"
            type="monthrange"
            value-format="yyyy-MM"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
          />
        </el-form-item>
      </el-col>
      <el-form-item label="" label-width="80px" class="fr">
        <el-button type="primary" @click="save">保 存</el-button>
      </el-form-item>
    </el-row>
  </el-form>
</template>

<script>
export default {
  data() {
    return {
      searchForm: {
        pointWeightName: null,
        effectStartTime: null,
        effectEndTime: null,
        stdPointWeightId: null,
        dateRange: null
      }
    }
  },
  methods: {
    setData({ pointWeightName, effectStartTime, effectEndTime, stdPointWeightId }) {
      this.searchForm = {
        pointWeightName,
        effectStartTime,
        effectEndTime,
        stdPointWeightId,
        dateRange: effectStartTime ? [effectStartTime, effectEndTime] : null
      }
    },
    save() {
      this.$refs.searchForm.validate(valid => {
        if (!valid) return
        if (this.searchForm.dateRange) {
          [this.searchForm.effectStartTime, this.searchForm.effectEndTime] = this.searchForm.dateRange
        } else {
          [this.searchForm.effectStartTime, this.searchForm.effectEndTime] = [null, null]
        }
        this.$parent.submit({
          pointWeightName: this.searchForm.pointWeightName,
          effectStartTime: this.searchForm.effectStartTime,
          effectEndTime: this.searchForm.effectEndTime
        })
      })
    }
  }
}
</script>
