<script>
import { getPointList, addPointWeight, getPointWeightDetails, editPointWeight } from '@/api/base/weight'
import HeadForm from './components/index'
const marginY = 35
const BLOCK_LEFTS = []
const TITLE_LEFTS = []
export default {
  name: 'BaseWeightAdd',
  data() {
    return {
      treeData: {
        title: '总体满意度',
        nodeWidth: 232,
        nodeHeight: 56,
        children: [
          // {
          //   title: '传统满意度',
          //   nodeWidth: 232,
          //   nodeHeight: 56,
          //   marginX: 0,
          //   extend: ['input'],
          //   rate: 0,
          //   children: []
          // },
          // {
          //   title: '即时评价',
          //   nodeWidth: 232,
          //   nodeHeight: 56,
          //   marginX: 0,
          //   extend: ['input'],
          //   rate: 0,
          //   children: [
          // {
          //   title: '业务触点',
          //   nodeHeight: 56,
          //   nodeWidth: 232,
          //   marginX: 30,
          //   extend: ['input', 'add'],
          //   rate: 0,
          //   children: [
          //     {
          //       title: '业务触点',
          //       nodeHeight: 140,
          //       nodeWidth: 32,
          //       extend: ['clear'],
          //       marginX: 6
          //     }
          //   ]
          // },
          // {
          //   title: '行为触点',
          //   nodeHeight: 56,
          //   nodeWidth: 232,
          //   marginX: 30,
          //   extend: ['input', 'add'],
          //   rate: 0,
          //   children: [
          //     {
          //       title: '业务触点',
          //       nodeHeight: 140,
          //       nodeWidth: 32,
          //       extend: ['clear'],
          //       marginX: 6
          //     }
          //   ]
          // },
          // {
          //   title: '被动触点',
          //   nodeHeight: 56,
          //   nodeWidth: 232,
          //   marginX: 30,
          //   rate: 0,
          //   extend: ['input', 'add'],
          //   children: [
          //     {
          //       title: '业务触点',
          //       nodeHeight: 140,
          //       nodeWidth: 32,
          //       extend: ['clear'],
          //       marginX: 6
          //     }
          //   ]
          // },
          // {
          //   title: '被动触点',
          //   nodeHeight: 56,
          //   nodeWidth: 232,
          //   marginX: 30,
          //   rate: 0,
          //   extend: ['input', 'add'],
          //   children: [
          //     {
          //       title: '业务触点',
          //       nodeHeight: 140,
          //       nodeWidth: 32,
          //       extend: ['clear'],
          //       marginX: 6
          //     }
          //   ]
          // }
          //   ]
          // }
        ]
      },
      finalTree: [],
      finalMaxWidth: 0,
      minLeft: 0,
      pointWeightRateList: [],
      stdPointWeightId: null
    }
  },
  created() {
    this.stdPointWeightId = this.$route.query.id || null
    this.$route.query.id ? this.getEditPointList() : this.getPointList()
  },
  methods: {
    // 新增获取详情
    getPointList() {
      const load = this.$load()
      getPointList({ stdPointWeightId: this.stdPointWeightId }).then(res => {
        const arr = res.data || []
        this.treeData.children = arr.map(item => {
          return {
            title: item.pointTypeName,
            pointTypeId: item.pointTypeId,
            nodeHeight: 56,
            nodeWidth: 232,
            marginX: 30,
            extend: ['input', 'add'],
            rate: item.rate,
            children: item.pointList.map(_item => {
              return {
                title: _item.pointName,
                pointId: _item.pointId,
                nodeHeight: 140,
                nodeWidth: 32,
                extend: ['clear'],
                marginX: 6
              }
            })
          }
        })
      }).finally(() => load.close())
    },
    // 编辑获取详情
    getEditPointList() {
      const load = this.$load()
      getPointWeightDetails({ stdPointWeightId: this.stdPointWeightId }).then(res => {
        const { pointWeightName, effectStartTime, effectEndTime, pointWeightRateList = [] } = res.data || {}
        this.$refs.headForm.setData({
          pointWeightName, effectStartTime, effectEndTime, stdPointWeightId: this.stdPointWeightId
        })
        this.treeData.children = pointWeightRateList.map(item => {
          return {
            title: item.pointTypeName,
            pointTypeId: item.pointTypeId,
            stdPointWeightRateId: item.stdPointWeightRateId,
            nodeHeight: 56,
            nodeWidth: 232,
            marginX: 30,
            extend: ['input', 'add'],
            rate: item.rate,
            children: item.pointList.map(_item => {
              return {
                title: _item.pointName,
                pointId: _item.pointId,
                stdPointWeightItemId: _item.stdPointWeightItemId,
                nodeHeight: 140,
                nodeWidth: 32,
                extend: ['clear'],
                marginX: 6
              }
            })
          }
        })
      }).finally(() => load.close())
    },
    submit(data) {
      const arr = this.treeData.children
      const sum = arr.map(item => item.rate).reduce((prev, next) => {
        return +prev + +next
      })
      if (sum !== 100) {
        this.$message.warning('权重之和必须为100%！')
        return
      }
      const load = this.$load()
      const API = this.stdPointWeightId ? editPointWeight : addPointWeight
      API({ ...data, pointWeightRateList: this.treeData.children, stdPointWeightId: this.stdPointWeightId }).then(res => {
        this.$message.success(res.msg)
        setTimeout(() => {
          this.$router.back()
        }, 1000)
      }).finally(() => load.close())
    },
    computedPosition(startX, startY, treeItem, _parent = null, _index = null) {
      if (treeItem.children?.length) {
        let blockWidth = 0
        let lastItemPosition = {
          nextStartX: startX,
          nextStartY: startY + treeItem.nodeHeight + marginY
        }
        const children = []
        let flag = false
        treeItem.children.forEach((item, index) => {
          const length = treeItem.children.length
          if (length !== undefined && !flag && treeItem.children.length < 6) {
            lastItemPosition.nextStartX = lastItemPosition.nextStartX + treeItem.nodeWidth / 2 - treeItem.children.length / 2 * 38
            flag = true
          }
          const childrenPosition = this.computedPosition(lastItemPosition.nextStartX, lastItemPosition.nextStartY, item, treeItem, index)
          const diff = childrenPosition.width + item.marginX
          blockWidth += diff
          lastItemPosition = {
            nextStartX: childrenPosition.startX + diff,
            nextStartY: childrenPosition.startY
          }
          children.push(childrenPosition)
        })
        blockWidth = blockWidth < treeItem.nodeWidth ? treeItem.nodeWidth + (treeItem.marginX || 0) : blockWidth + (treeItem.marginX || 0)
        return {
          startX,
          startY,
          children,
          type: 'block',
          height: treeItem.blockHeight,
          width: blockWidth - treeItem.marginX,
          title: {
            startX,
            startY,
            type: 'text',
            _children: treeItem.children,
            _parent,
            _index,
            rate: treeItem.rate,
            title: treeItem.title,
            width: treeItem.nodeWidth,
            height: treeItem.nodeHeight,
            extend: treeItem.extend
          }
        }
      }
      treeItem.pointTypeId ? BLOCK_LEFTS.push(startX) : TITLE_LEFTS.push(startX)
      return {
        startX,
        startY,
        type: 'text',
        title: treeItem.title,
        height: treeItem.nodeHeight,
        width: treeItem.nodeWidth,
        extend: treeItem.extend,
        _children: treeItem.children,
        rate: treeItem.rate,
        _parent,
        _index
      }
    },
    shakeTree(blockNode) {
      if (blockNode.type === 'text') {
        return blockNode.startX
      }
      let firstChildCenterStarX = 0
      let lastChildCenterStartX = 0
      blockNode.children.forEach((e, index) => {
        const centerStartX = this.shakeTree(e)
        if (index === 0) {
          firstChildCenterStarX = centerStartX
        }
        if (index === blockNode.children.length - 1) {
          lastChildCenterStartX = centerStartX
        }
      })
      const lastChild = blockNode.children[blockNode.children.length - 1]
      const lasthChildWidth = typeof lastChild.title === 'object' ? lastChild.title.width : lastChild.width
      const newStartX = (lastChildCenterStartX + lasthChildWidth - firstChildCenterStarX) / 2 + firstChildCenterStarX - blockNode.title.width / 2
      blockNode.title.startX = newStartX
      BLOCK_LEFTS.push(newStartX)
      return newStartX
    },
    renderTree(node, vNodes = []) {
      let lineVNodes = []
      if (node.type === 'text') {
        vNodes.push(this.renderDom(node))
      } else {
        vNodes.push(this.renderDom(node.title))
        lineVNodes = lineVNodes.concat(this.renderLine(node))
        node.children.forEach(item => {
          this.renderTree(item, vNodes)
        })
      }
      lineVNodes.forEach(item => {
        vNodes.push(item)
      })
      return vNodes
    },
    renderDom(node) {
      const { startX, startY, title, width, height, extend = [] } = node
      return (<div class={[extend.includes('clear') && 'between', 'm-box']} style={{
        width: width + 'px',
        height: height + 'px',
        left: startX + 'px',
        top: startY + 'px'
      }}>
        { title }
        { extend.includes('input') && <div class="u-input-number">
          <el-input-number value={node.rate} class="custom-input" controls-position="right" size="mini" controls={false} onchange={(e) => {
            node.rate = e
            this.treeData.children[node._index].rate = e
          }} min={0} max={100} precision={0}></el-input-number>
          <span class="custom-input__suffix">%</span>
        </div> }
        {/* { extend.includes('add') && <i class="u-icon el-icon-circle-plus" onClick={() => this.addNode(node)} /> }
       { extend.includes('clear') && <i class="u-del el-icon-circle-close" onClick={() => this.delNode(node)} /> } */}
      </div>)
    },
    renderLine(node, vNodes = []) {
      const lineStartX = node.title.startX + node.title.width / 2
      const lineStartY = node.title.startY + node.title.height
      const halfY = lineStartY + marginY / 2
      const { vNode } = this.createLineDom(lineStartX, lineStartY, 1, halfY - lineStartY)
      vNodes.push(vNode)
      const arr = node.children.map((e) => {
        if (e.type === 'text') {
          const childCenterX = e.startX + e.width / 2
          const { vNode, left, top } = this.createLineDom(childCenterX, halfY, 1, e.startY - halfY, true)
          vNodes.push(vNode)
          return { left, top }
        } else {
          const childTitle = e.title
          const childTitleCenterX = childTitle.startX + childTitle.width / 2
          const { vNode, left, top } = this.createLineDom(childTitleCenterX, halfY, 1, childTitle.startY - halfY, true)
          vNodes.push(vNode)
          return { left, top }
        }
      })
      const startX = arr[0].left
      const endX = arr[arr.length - 1].left
      vNodes.push(this.createLineDom(startX, arr[0].top, endX - startX, 1).vNode)
      return vNodes
    },
    createLineDom(left, top, width, height, isTriangle = false) {
      return {
        left,
        top,
        vNode: (<div class={['m-line', isTriangle && 'triangle']} style={{
          width: width + 'px',
          height: height + 'px',
          left: left + 'px',
          top: top + 'px'
        }}></div>)
      }
    },
    addNode(node) {
      const length = node._children.length
      node._children.splice(Math.ceil(length / 2), 0, {
        title: '业务触点' + parseInt(Math.random() * 100),
        nodeHeight: 140,
        nodeWidth: 32,
        extend: ['clear'],
        marginX: 6
      })
    },
    delNode(node) {
      node._parent.children.splice(node._index, 1)
    },
    computedBlockMaxWidth() {
      BLOCK_LEFTS.sort((a, b) => a - b)
      TITLE_LEFTS.sort((a, b) => a - b)
      this.minLeft = BLOCK_LEFTS[0] < TITLE_LEFTS[0] ? BLOCK_LEFTS[0] : TITLE_LEFTS[0]
      const blockMax = BLOCK_LEFTS[BLOCK_LEFTS.length - 1] + 232
      const titleMax = TITLE_LEFTS[TITLE_LEFTS.length - 1] + 32
      if (blockMax > titleMax) {
        this.finalMaxWidth = blockMax - this.minLeft
      } else {
        this.finalMaxWidth = titleMax - this.minLeft
      }
    }
  },
  render() {
    BLOCK_LEFTS.splice(0)
    TITLE_LEFTS.splice(0)
    const node = this.computedPosition(0, 10, this.treeData)
    this.shakeTree(node)
    this.computedBlockMaxWidth()
    return (<div class="g-container">
      <HeadForm ref="headForm" />
      <div style="width: 100%;height: 100%;overflow-x: auto;padding-left: 5px">
        <div class="u-lines" style={{
          width: this.finalMaxWidth + 'px',
          transform: 'translateX(-' + this.minLeft + 'px)'
        }}>{this.renderTree(node)}</div>
      </div>
    </div>)
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  position: relative;
  width: 100%;
  height: 100%;
  overflow-x: auto;
  padding: 20px;
  background-color: #fff;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
}
.u-lines {
  position: relative;
  width: 1200px;
  margin: 0 auto;
}
.m-box {
  border: 1px solid #F5F7FA;
  border-radius: 2px;
  text-align: center;
  box-sizing: border-box;
  padding: 0 8px;
  display: flex;
  align-items: center;
  justify-content: center;
  color: #333;
  position: absolute;
  font-size: 14px;
  &.between {
    justify-content: space-between;
    flex-direction: column;
    padding: 12px 8px 8px;
  }
  .u-del {
    bottom: 10px;
    color: rgb(142, 153, 174);
    cursor: pointer;
    &:hover {
      color: $--color-danger;
    }
  }
}
.u-icon {
  position: absolute;
  left: 50%;
  transform: translateX(-50%);
  bottom: -25px;
  cursor: pointer;
  color: $--color-primary;
  z-index: 2;
  font-size: 20px;
  background-color: #fff;
  border-radius: 50%;
}
.m-line {
  position: absolute;
  background-color: #979797;
  &.triangle::after {
    content: '';
    position: absolute;
    bottom: -4px;
    left: 50%;
    transform: translateX(-50%);
    border: 4px solid transparent;
    border-top-color: #979797;
    border-top-width: 8px;
  }
}
.u-input-number {
  display: flex;
  align-items: center;
  position: relative;
}
.custom-input {
  width: 76px;
  margin-left: 10px;
  border: 1px solid transparent;
  border-right: none;
  &__suffix {
    background: #f5f7fa;
    height: 26px;
    display: flex;
    align-items: center;
    padding: 0 5px;
    border-top-right-radius: 4px;
    border-bottom-right-radius: 4px;
    border-left: none;
    font-size: 12px;
    color: #606266;
    position: absolute;
    right: 1px;
    top: 2px;
  }
  ::v-deep {
    .el-input__inner {
      padding-right: 30px;
    }
  }
}
</style>
