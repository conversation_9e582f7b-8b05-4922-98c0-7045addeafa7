<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="年度：">
          <el-date-picker
            v-model="searchForm1.year"
            type="year"
            value-format="yyyy"
            placeholder="请选择年"
          />
        </el-form-item>
      </template>
      <template #suffix>
        <el-button type="primary" :loading="tableLoading1" icon="el-icon-search" style="margin-bottom: 20px;" @click="getList1">搜索</el-button>
      </template>
    </UmSearchLayout>

    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row>
        <el-col :span="18">
          <CommonTabs :tabs="tabList" @changeTab="changeTab" />
        </el-col>
        <el-col :span="6">
          <el-button
            v-if="$checkPermission(['JCPZ_SFPZ_XZ'])"
            style="float: right"
            icon="el-icon-circle-plus-outline"
            type="primary"
            @click="addForm.stdAlgorithmPointId = null;addFormShow = true"
          >
            新增
          </el-button>
        </el-col>
      </el-row>
      <um-table-full :data="tableData1" class="margin-t-20" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column label="年度" prop="year">
          <template slot-scope="{ row }">
            <el-button :disabled="!$checkPermission(['JCPZ_SFPZ_BJ']) || searchForm1.statusCode === 2300012" type="text" @click="edit(row)">{{ row.year }}</el-button>
          </template>
        </el-table-column>
        <el-table-column :label="searchForm1.statusCode === 2300011 ? '创建人' : '发布人'" prop="createUser" />
        <el-table-column prop="createTime" :label="searchForm1.statusCode === 2300011 ? '创建时间' : '发布时间'" width="170" />
        <el-table-column label="操作" width="180">
          <template slot-scope="scope">
            <template v-if="searchForm1.statusCode === 2300011">
              <el-button v-if="$checkPermission(['JCPZ_SFPZ_BJPZ'])" type="text" @click="$router.push('/base/math/edit?id=' + scope.row.stdAlgorithmPointId)">编辑</el-button>
              <el-button v-if="$checkPermission(['JCPZ_SFPZ_FB'])" type="text" @click="changeStatus(scope.row, 2300012)">发布</el-button>
              <el-button
                v-if="$checkPermission(['JCPZ_SFPZ_SC'])"
                type="text"
                style="color: #FF6161;"
                @click="changeStatus(scope.row, 1)"
              >
                <span class="danger">删除</span>
              </el-button>
            </template>
            <template v-else>
              <el-button v-if="$checkPermission(['JCPZ_SFPZ_QXFB'])" type="text" @click="changeStatus(scope.row, 2300011)">取消发布</el-button>
              <el-button type="text" @click="$router.push('/base/math/detail?id=' + scope.row.stdAlgorithmPointId + '&isDetail=1')">详情</el-button>
            </template>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        @pagination="getList1('page')"
      />
    </div>
    <el-dialog
      :visible.sync="addFormShow"
      :title="`${addForm.stdAlgorithmPointId ? '编辑' : '新增'}`"
      :close-on-click-modal="false"
      width="400px"
      @close="$resetForm('addForm')"
    >
      <el-form ref="addForm" :model="addForm" label-width="60px">
        <el-form-item label="年度：" prop="year" :rules="{required: true, message: '请选择年度'}">
          <el-date-picker
            v-model="addForm.year"
            type="year"
            value-format="yyyy"
            style="width: 100%"
            placeholder="请选择年度"
          />
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="addFormShow = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '../../../components/CommonTabs'
import { getAlgorithmPointPage, changeStatusCode, addAlgorithmPoint, editAlgorithmPoint } from '@/api/base/math'
export default {
  name: 'BaseMath',
  components: {
    CommonTabs
  },
  mixins: [getList1],
  props: {},
  data() {
    return {
      listApi1: getAlgorithmPointPage,
      searchForm1: {
        year: null,
        statusCode: 2300011
      },
      tabList: [
        {
          label: '待发布',
          value: 2300011,
          count: 0,
          key: 'unpublished'
        },
        {
          label: '已发布',
          value: 2300012,
          count: 0,
          key: 'publish'
        }
      ],
      addFormShow: false,
      addForm: {
        year: null,
        stdAlgorithmPointId: null
      }
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    // tab上的统计数据
    tableCallBack1() {
      this.tabList.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    confirm() {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        const API = this.addForm.stdAlgorithmPointId ? editAlgorithmPoint : addAlgorithmPoint
        const load = this.$load()
        API(this.addForm).then(res => {
          this.$message.success(res.msg)
          this.addFormShow = false
          this.getList1()
        }).finally(() => load.close())
      })
    },
    edit(data) {
      this.addFormShow = true
      this.$nextTick(() => {
        this.addForm.stdAlgorithmPointId = data.stdAlgorithmPointId
        this.addForm.year = data.year
      })
    },
    /**
     * 改变计划状态
    */
    changeStatus(data, statusCode) {
      let str = ''
      let title = '提示'
      switch (statusCode) {
        case 2300012: str = `是否确定发布该年度：${data.year}？`; break
        case 2300011: title = '取消发布不影响已生成的统计数据'; str = `是否确定取消发布该年度：${data.year}？`; break
        case 1: str = `是否确定删除该年度：${data.year}？`; break
      }
      this.$confirm(str, title, {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then((_) => {
        const load = this.$load()
        changeStatusCode({ stdAlgorithmPointId: data.stdAlgorithmPointId, statusCode }).then((res) => {
          this.$message.success(res.msg)
          this.getList1()
        }).finally(_ => {
          load.close()
        })
      }).catch((_) => {
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
  }
  </style>

