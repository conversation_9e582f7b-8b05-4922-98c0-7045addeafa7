<template>
  <div class="g-container">
    <CommonTitle title="算法配置" show-bg>
      <!-- <el-button type="primary" @click="$router.push('/base/indicator/log')">保存</el-button> -->
    </CommonTitle>
    <div class="g-body">
      <div class="g-body__l">
        <div class="g-body__l--head">
          <span class="m-title">触点</span>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" class="g-body__l--content">
          <div
            v-for="item in listData"
            :key="item.pointId"
            class="m-block"
            :class="{active: item.pointId === editForm.pointId}"
            @click="changePoint(item)"
          >
            {{ item.pointName || '--' }}
          </div>
          <el-empty v-if="!listData.length" style="height: 100%;" description="暂无数据" />
        </el-scrollbar>
      </div>
      <div class="g-body__r">
        <el-empty v-if="!editForm.pointId" style="height: 100%;" description="请选择触点" />
        <template v-else>
          <div class="g-body__r--head">
            <span class="m-title">{{ editForm.pointName || '--' }}</span>
            <el-button v-if="!isDetail" type="primary" @click="save">保存</el-button>
          </div>
          <div class="g-body__r--content">
            <el-form ref="editForm" :model="editForm" :disabled="isDetail">
              <el-row>
                <el-col :span="12">
                  <el-form-item label="算法模型：" prop="modelTypeCode" :rules="{ required: true, message: '请选择算法模型'}">
                    <el-radio-group v-model="editForm.modelTypeCode">
                      <el-radio :label="MATH_MODEL.LEVEL" style="margin-right: 10px;">分级计算（样框量加权）</el-radio>
                      <el-radio :label="MATH_MODEL.DIRECT">直接计算</el-radio>
                    </el-radio-group>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="是否计入考核：" label-width="100px" prop="checkFlag" :rules="{ required: true, message: '请选择是否计入考核'}">
                    <el-select v-model="editForm.checkFlag">
                      <el-option label="是" :value="true" />
                      <el-option label="否" :value="false" />
                    </el-select>
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="地产权重：" prop="realEstateRate" :rules="{ required: true, message: '请输入地产权重'}">
                    <el-input-number v-model="editForm.realEstateRate" :min="0" :max="100" style="width: 100px;margin-right: 10px;" :controls="false" :precision="2" />%
                  </el-form-item>
                </el-col>
                <el-col :span="12">
                  <el-form-item label="物业权重：" label-width="100px" prop="certifiedPropertyRate" :rules="{ required: true, message: '请输入物业权重'}">
                    <el-input-number v-model="editForm.certifiedPropertyRate" :min="0" :max="100" style="width: 100px;margin-right: 10px;" :controls="false" :precision="2" />%
                  </el-form-item>
                </el-col>
              </el-row>
            </el-form>
          </div>
        </template>
      </div>
    </div>
  </div>
</template>

<script>
import { getAlgorithmPointInfo, editAlgorithmPointItem } from '@/api/base/math'
import CommonTitle from '@/components/CommonTitle'
import { MATH_MODEL } from '@/enum'
import { cloneDeep } from 'lodash'
export default {
  name: 'BaseMathEdit',
  components: { CommonTitle },
  data() {
    return {
      editForm: {
        stdAlgorithmPointItemId: null,
        modelTypeCode: null, // 算法模型
        realEstateRate: undefined, // 地产权重
        certifiedPropertyRate: undefined, // 物业权重
        pointId: null,
        checkFlag: null
      },
      stdAlgorithmPointItemId: null,
      MATH_MODEL,
      listData: [],
      isDetail: false
    }
  },
  async created() {
    const { id, isDetail } = this.$route.query
    this.isDetail = !!isDetail
    if (id) {
      this.stdAlgorithmPointItemId = id
      this.getAlgorithmPointInfo()
    }
  },
  methods: {
    // 获取指标树
    getAlgorithmPointInfo() {
      const load = this.$load()
      getAlgorithmPointInfo({ stdAlgorithmPointId: this.stdAlgorithmPointItemId }).then(res => {
        this.listData = res.data || []
        this.listData.length && !this.editForm.pointId && (this.editForm = this.listData[0])
      }).finally(() => load.close())
    },
    changePoint(data) {
      this.editForm = cloneDeep(data)
      this.$nextTick(() => {
        this.$refs.editForm.clearValidate()
      })
    },
    // 保存
    save() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const load = this.$load()
          editAlgorithmPointItem(this.editForm).then(res => {
            this.$message.success(res.msg)
            this.getAlgorithmPointInfo()
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>

  <style lang="scss" scoped>
  .g-container {
    background-color: #fff;
    height: 100%;
    display: flex;
    flex-direction: column;
    .m-title {
      color: #333;
      font-size: 16px;
      font-weight: 500;
    }

    .g-body {
      flex: 1;
      display: flex;
      margin-top: -20px;
      overflow: hidden;
      &__l {
        width: 250px;
        flex-shrink: 0;
        display: flex;
        flex-direction: column;
        box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
        &--head {
          display: flex;
          justify-content: space-between;
          padding: 0 10px 0 20px;
          height: 56px;
          align-items: center;
        }
        &--content {
          flex: 1;
          overflow-x: hidden;
          .m-block {
            padding: 0px 20px;
            line-height: 36px;
            overflow: hidden;
            text-overflow: ellipsis;
            white-space: no-wrap;
            cursor: pointer;
            &.active {
                background-color: $--color-background;
                color: $--color-primary;
            }
            &:hover {
                background-color: $--color-background;
            }
          }
        }
      }

      &__r {
        padding-left: 20px;
        padding-right: 20px;
        flex: 1;
        display: flex;
        flex-direction: column;
        overflow-x: hidden;
        &--head {
          height: 56px;
          display: flex;
          justify-content: space-between;
          align-items: center;
        }
        &--content {
          padding-bottom: 20px;
          flex: 1;
          overflow-y: auto;
        }
      }
    }
  }
  </style>

