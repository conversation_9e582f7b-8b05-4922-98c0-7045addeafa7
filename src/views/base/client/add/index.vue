<template>
  <div>
    <um-bread-crumb2 />
    <div class="common-box mt-20">
      <el-card shadow="never">
        <CommonTitle :title="`问卷名称：${templateName}`" />
        <div class="tips margin-b-20">
          <span style="margin-right: 8px">满足以下</span>
          <el-select v-model="addForm.relateConditionType">
            <el-option
              v-for="opt in options"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
          <span style="margin-left: 8px">条件时，需要安排维系</span>
        </div>
        <el-table :data="tableData">
          <el-table-column type="index" label="序号" width="100" fixed="left" align="center" />
          <el-table-column prop="questionName" label="问题名称" :formatter="$formatterTable" width="300" />
          <el-table-column prop="questionTypeCode" label="问题类型" :formatter="$formatterTable" width="200">
            <template slot-scope="scope">
              <div v-if="scope.row.questionTypeCode === 9701401">单选题</div>
              <div v-if="scope.row.questionTypeCode === 9701402">多选题</div>
              <div v-if="scope.row.questionTypeCode === 9701403">填空题</div>
              <div v-if="scope.row.questionTypeCode === 9701404">打分题</div>
              <div v-if="scope.row.questionTypeCode === 9701405">文本描述</div>
            </template>
          </el-table-column>
          <el-table-column prop="questionItemList" label="需要维系的问题答案" :formatter="$formatterTable">
            <template slot-scope="{ row }">
              <el-select v-if="row.questionItemList && row.questionItemList.length" v-model="row.quesTemplateQuestionItemIds" clearable style="width: 100%" multiple>
                <el-option
                  v-for="opt in row.questionItemList"
                  :key="opt.quesTemplateQuestionItemId"
                  :label="opt.itemName"
                  :value="opt.quesTemplateQuestionItemId"
                />
              </el-select>
            </template>
          </el-table-column>
        </el-table>
        <div class="chengguo">
          <div class="footer-btn">
            <el-button @click="$router.go(-1)">取 消</el-button>
            <el-button type="primary" @click="handleConfirm">保 存</el-button>
          </div>
        </div>
      </el-card>
    </div>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle'
import { addRule, getQuestionListByQuesTemplateId, showRuleDetail, editRule } from '@/api/configuration'
export default {
  name: 'ShortboardTasksAdd',
  components: { CommonTitle },
  props: {},
  data() {
    return {
      types: 9706402,
      options: [
        { label: '任一', value: 9706402 },
        { label: '全部', value: 9706401 }
      ],
      addForm: {
        relateConditionType: 9706402,
        quesTemplate: []
      },
      tableData: [],
      templateName: null,
      quesTemplateId: null,
      cmRuleId: null,
      detailsInfo: {} // 详情数据
    }
  },
  created() {
    this.templateName = this.$route.query.templateName || '-'
    this.quesTemplateId = this.$route.query.quesTemplateId
    this.cmRuleId = this.$route.query.cmRuleId
    this.getTable()
  },
  mounted() {},
  methods: {
    // 编辑获取详情
    getInfo() {
      showRuleDetail({
        cmRuleId: this.cmRuleId
      }).then(res => {
        this.detailsInfo = res.data
        this.templateName = res.data.questionName
        this.addForm.relateConditionType = this.detailsInfo.relateConditionType // 9706401: 全部 9706402: 任一
        this.tableData.map(item => {
          this.detailsInfo.quesTemplate.forEach(li => {
            if (item.quesTemplateQuestionId === li.quesTemplateQuestionId) {
              item.quesTemplateQuestionItemIds = li.quesTemplateQuestionItemIds
            }
            return item
          })
        })
      })
    },
    handleConfirm() {
      const quesTemplate = []
      this.tableData.forEach(item => {
        if (item.quesTemplateQuestionItemIds && item.quesTemplateQuestionItemIds.length) {
          quesTemplate.push({
            quesTemplateQuestionId: item.quesTemplateQuestionId,
            quesTemplateQuestionItemIds: item.quesTemplateQuestionItemIds && item.quesTemplateQuestionItemIds.length ? item.quesTemplateQuestionItemIds : null
          })
        }
      })
      const load = this.$load()
      let METHODS = null
      if (this.cmRuleId) {
        METHODS = editRule
      } else {
        METHODS = addRule
      }
      METHODS({
        ...this.addForm,
        quesTemplate: quesTemplate,
        quesTemplateId: this.quesTemplateId,
        cmRuleId: this.cmRuleId
      }).then(res => {
        load.close()
        this.$message.success(res.msg)
        this.$router.go(-1)
      }).catch(e => {
        load.close()
        this.$errorHandle(e)
      })
    },
    // 根据问卷id查询所有问题
    getTable() {
      getQuestionListByQuesTemplateId({
        quesTemplateId: this.quesTemplateId
      }).then(res => {
        this.tableData = res.data
        if (this.cmRuleId) {
          this.getInfo()
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.common-box {
  background: #FFFFFF;
  border-radius: 12px;
  .tips {
    height: 52px;
    background: #F8FAFB;
    border-radius: 8px;
    padding-left: 15px;
    box-sizing: border-box;
    display: flex;
    align-items: center;
  }
}
.footer-btn {
  width: 196px;
  margin: 0 auto;
  height: 56px;
  display: flex;
  align-items: center;
}
.chengguo {
  padding-left: 20px;
  padding-top: 10px;
}
</style>
