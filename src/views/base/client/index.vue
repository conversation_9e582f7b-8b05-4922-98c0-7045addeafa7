<template>
  <div class="g-container">
    <el-card>
      <el-form ref="searchForm" :model="searchForm" label-width="70px">
        <el-row :gutter="30">
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="quesTemplateName" label="问卷名称">
              <el-input v-model="searchForm.quesTemplateName" clearable placeholder="请输入问卷名称" />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item label="创建时间">
              <el-date-picker
                v-model="timeList"
                clearable
                type="daterange"
                range-separator="至"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                start-placeholder="请选择日期"
                end-placeholder="请选择日期"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6" class="fr text-right mb-20">
            <el-button :loading="tableLoading" type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
            <el-button v-permission="['97060201']" type="primary" icon="el-icon-circle-plus-outline" @click="addHandle">新增</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card v-loading="tableLoading" class="g-container__btm mt-20">
      <el-table
        :data="tableData"
        border
        scroll
        style="width: 100%;"
      >
        <el-table-column prop="xh" label="序号" />
        <el-table-column
          prop="questionName"
          label="问卷名称"
          :formatter="$formatterTable"
        >
          <template slot-scope="scope">
            <el-button type="text" class="row-two" :title="scope.row.questionName" @click="toInfo(scope.row)">
              {{ scope.row.questionName || '--' }}
            </el-button>
          </template>
        </el-table-column>
        <el-table-column
          label="问题数量"
          prop="questionNumber"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="maintainNumber"
          label="需维系问题"
          :formatter="$formatterTable"
        />
        <el-table-column
          label="创建人"
          prop="createUserName"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          :formatter="$formatterTable"
        />
        <el-table-column
          label="操作"
          fixed="right"
          width="130"
        >
          <template slot-scope="scope">
            <el-button v-permission="['97060202']" type="text" @click="editHandle(scope.row)">编辑</el-button>
            <el-button v-permission="['97060203']" style="color: #FF6161" type="text" @click="delHandle(scope.row)">删除</el-button>
          </template>
        </el-table-column>
      </el-table>

      <pagination
        style="margin-top: 20px;margin-bottom: 20px"
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        @pagination="getList('page')"
      />
    </el-card>
    <el-dialog
      title="新增"
      :visible.sync="showModule"
      width="520px"
      @close="$refs.addForm.resetFields()"
    >
      <el-form ref="addForm" :model="addForm" label-width="70px">
        <el-form-item ref="quesTemplateId" label="问卷名称" prop="quesTemplateId" :rules="[{ required: true, message: '请选择问卷', trigger: ['change']}]">
          <el-select v-model="addForm.quesTemplateId" clearable filterable style="width: 100%" @change="getName">
            <el-option
              v-for="opt in templateList"
              :key="opt.quesTemplateId"
              :label="opt.templateName"
              :value="opt.quesTemplateId"
            />
          </el-select>
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="showModule=false">取消</el-button>
        <el-button type="primary" @click.native="submitHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { pageBoard, selectQuesTemplateList, deleteRule } from '@/api/configuration'
export default {
  name: 'BaseClient',
  mixins: [getList],
  data() {
    return {
      addForm: {
        quesTemplateId: null
      },
      listApi: pageBoard,
      tableLoading: false,
      approvalNumber: 0,
      searchForm: {
        quesTemplateName: null,
        createBeginTime: null,
        createEndTime: null,
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      showModule: false,
      tableData: [],
      timeList: [],
      templateList: [],
      templateName: null,
      type: 'add'
    }
  },
  watch: {
    'timeList'(val) {
      if (val) {
        this.searchForm.createBeginTime = val[0]
        this.searchForm.createEndTime = val[1]
      } else {
        this.searchForm.createBeginTime = null
        this.searchForm.createEndTime = null
      }
    }
  },
  activated() {
    this.getList()
  },
  methods: {
    getModule() {
      selectQuesTemplateList().then(res => {
        this.templateList = res.data
      })
    },
    // 跳转详情
    toInfo(row) {
      this.$router.push({
        path: '/wenjuan/preview',
        query: {
          id: row.quesTemplateId,
          title: row.questionName
        }
      })
    },
    // 获取问卷名称
    getName() {
      const res = this.templateList.find(item => item.quesTemplateId === this.addForm.quesTemplateId)
      this.templateName = res.templateName
    },
    addHandle() {
      this.showModule = true
      this.getModule()
      // this.$router.push('/base/client/add')
    },
    submitHandle() {
      this.$refs.addForm.validate((valid) => {
        if (valid) {
          this.showModule = false
          this.$router.push({
            path: '/base/client/add',
            query: {
              quesTemplateId: this.addForm.quesTemplateId,
              templateName: this.templateName
            }
          })
        }
      })
    },
    // 编辑
    editHandle(row) {
      this.$router.push({
        path: '/base/client/update',
        query: {
          quesTemplateId: row.quesTemplateId,
          cmRuleId: row.cmRuleId,
          templateName: row.templateName
        }
      })
    },
    // 删除
    delHandle(row) {
      this.$confirm('是否确认删除?', '提示', {
        confirmButtonText: '确定',
        customClass: 'del-model',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteRule({ cmRuleId: row.cmRuleId }).then(res => {
          this.getList()
          load.close()
          this.$message({
            type: 'success',
            message: '删除成功!'
          })
        }).catch(e => {
          load.close()
        })
      }).catch(() => {
        this.$message({
          showClose: true,
          type: 'info',
          message: '已取消删除'
        })
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  ::v-deep {
    .el-card__body {
      padding-bottom: 0;
    }
  }
  &__btm {
    flex: 1;
    overflow-y: auto;
    ::v-deep .el-card__body {
      height: 100%;
      display: flex;
      flex-direction: column;
    }
  }
  ::v-deep.el-date-editor .el-range-separator {
    width: 10%;
  }
}
</style>
