<template>
  <div class="g-container flex-col">
    <um-table-full
      :data="tableData"
      scroll
    >
      <el-table-column type="index" width="100" label="序号" />
      <el-table-column label="规则名称" prop="name" width="160" />
      <el-table-column label="说明" prop="desc" />
      <el-table-column label="操作" width="120">
        <template slot-scope="scope">
          <el-button v-if="$checkPermission(scope.row.permission)" type="text" @click="rowEdit(scope.row.dictCode)">编辑</el-button>
        </template>
      </el-table-column>
    </um-table-full>
    <el-dialog :visible.sync="timeDialogShow" title="推送时间设置" width="306px" :close-on-click-modal="false">
      <el-row type="flex" align="middle" justify="center">
        每日 <el-time-picker
          v-model="timeForm.beginTime"
          class="hide-prefix"
          format="HH:mm"
          value-format="HH:mm"
          placeholder="请选择"
          :picker-options="{
            selectableRange: '00:00:00 - ' + (timeForm.endTime || '23:59') + ':00'
          }"
          :clearable="false"
        /> 至 <el-time-picker
          v-model="timeForm.endTime"
          class="hide-prefix"
          format="HH:mm"
          value-format="HH:mm"
          :picker-options="{
            selectableRange: (timeForm.beginTime || '00:00') + ':00 - 23:59:00'
          }"
          placeholder="请选择"
          :clearable="false"
        />
      </el-row>
      <div class="m-tips">
        <i class="el-icon-warning-outline" /><span>提示：{{ timeForm.endTime || '--' }}至次日{{ timeForm.beginTime || '--' }}的推送任务会留到次日{{ timeForm.beginTime || '--' }}进行推送，避免打扰客户</span>
      </div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="timeDialogShow = false">取 消</el-button>
        <el-button type="primary" :disabled="!timeForm.startTime && !timeForm.endTime" @click="editSendTimeRule">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog :visible.sync="festivalDialogShow" title="节假日" width="600px" :close-on-click-modal="false" destroy-on-close>
      <el-row type="flex" align="middle" justify="center">
        <el-col :span="10">
          <el-scrollbar wrap-class="u-calendar__scrollbar">
            <div v-for="item in festivalForm.timeList" :key="item.cfgHolidaysId" class="u-times__item">
              <span>{{ item.startTime }} ～ {{ item.endTime }}</span>
              <i class="el-icon-circle-close" @click="delFestival(item)" />
            </div>
            <el-empty v-if="!festivalForm.timeList.length" title="暂无数据" />
          </el-scrollbar>
        </el-col>
        <el-col :span="14" style="display: flex;flex-direction: column;align-items: center;">
          <Calendar ref="calendar" />
          <el-button type="primary" @click="setFestival">设置为节假日</el-button>
        </el-col>
      </el-row>
    </el-dialog>
    <el-dialog :visible.sync="scoreDialogShow" title="绩效转换系数" width="400px" :close-on-click-modal="false" destroy-on-close>
      <el-form ref="scoreForm" :model="scoreForm">
        <el-row type="flex" align="middle" style="margin-bottom: 20px;">
          转换分乘以
          <el-form-item prop="satisfaction" :rules="[{ required: true, message: '请输入'}]" style="margin-bottom: 0!important;">
            <el-input-number v-model="scoreForm.satisfaction" :controls="false" controls-position="right" :min="0" :max="1" :precision="2" />
          </el-form-item>
          作为总体满意度绩效分
        </el-row>
        <el-row type="flex" align="middle">
          转换分乘以
          <el-form-item prop="propertyService" :rules="[{ required: true, message: '请输入'}]" style="margin-bottom: 0!important;">
            <el-input-number v-model="scoreForm.propertyService" :controls="false" controls-position="right" :min="0" :max="1" :precision="2" />
          </el-form-item>
          作为物业服务满意度绩效分
        </el-row>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="scoreDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="editPerformanceConversionRule">确 定</el-button>
      </span>
    </el-dialog>
    <Cycle :show.sync="weekDialogShow" />
    <Deep :show.sync="deepDialogShow" />
  </div>
</template>

<script>
import Calendar from '@/components/Calendar'
import { getSendTimeRule, editSendTimeRule, getPerformanceConversionRule, editPerformanceConversionRule, getHolidaysRule, deleteHolidaysRule, addHolidaysRule } from '@/api/base/rule'
import Cycle from './components/Cycle'
import Deep from './components/Deep.vue'
const DICT_CODE = {
  TIME: 1100501,
  FESTIVAL: 1100503,
  SCORE: 1100502,
  WEEK: 1100504,
  DEEP: 1100505
}

export default {
  name: 'BaseRule',
  components: { Calendar, Cycle, Deep },
  data() {
    return {
      DICT_CODE,
      tableData: [{
        name: '每日推送时间限制',
        desc: '用于设置每日推送时间限制，避免夜间打扰客户。',
        dictCode: DICT_CODE.TIME,
        permission: ['JCPZ_XTTYGZ_BJMRTSSJXZ']
      }, {
        name: '节假日',
        desc: '用于设置节假日，避免节假日推送打扰客户。',
        dictCode: DICT_CODE.FESTIVAL,
        permission: ['JCPZ_XTTYGZ_BJJJR']
      },
      // {
      //   name: '绩效转换系数',
      //   desc: '用于设置转换分计算绩效分的转换系数。',
      //   dictCode: DICT_CODE.SCORE
      // },
      {
        name: '回访周期',
        desc: '用于设置调研的月度时间规则',
        dictCode: DICT_CODE.WEEK,
        permission: ['JCPZ_XTTYGZ_BJHFZQ']
      },
      {
        name: '深访问卷配置',
        desc: '用于设置深访计划的问卷有效期和推送的规则',
        dictCode: DICT_CODE.DEEP,
        permission: ['JCPZ_XTTYGZ_SFWJPZ']
      }],
      timeDialogShow: false,
      timeForm: {
        beginTime: '',
        endTime: ''
      },
      scoreDialogShow: false,
      scoreForm: {
        satisfaction: undefined,
        propertyService: undefined
      },
      weekDialogShow: false,
      festivalDialogShow: false,
      deepDialogShow: false,
      festivalForm: {
        timeList: []
      }
    }
  },
  methods: {
    async rowEdit(dictCode) {
      try {
        switch (dictCode) {
          case DICT_CODE.TIME:
            await this.getSendTimeRule()
            this.timeDialogShow = true
            break
          case DICT_CODE.FESTIVAL:
            await this.getHolidaysRule()
            this.festivalDialogShow = true
            break
          case DICT_CODE.SCORE:
            await this.getPerformanceConversionRule()
            this.scoreDialogShow = true
            break
          case DICT_CODE.WEEK:
            this.weekDialogShow = true
            break
          case DICT_CODE.DEEP:
            this.deepDialogShow = true
            break
        }
      } catch (error) {
        console.log(error)
      }
    },
    // 获取时间配置规则
    getSendTimeRule() {
      const load = this.$load()
      return getSendTimeRule().then(res => {
        const { beginTime, endTime } = res.data
        this.timeForm.beginTime = beginTime || ''
        this.timeForm.endTime = endTime || ''
      }).finally(() => load.close())
    },
    // 编辑时间配置规则
    editSendTimeRule() {
      this.$confirm('是否确定保存该推送时间设置？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        editSendTimeRule(this.timeForm).then(res => {
          this.$message.success(res.msg)
          this.timeDialogShow = false
        }).finally(() => load.close())
      })
    },
    // 获取绩效配置规则
    getPerformanceConversionRule() {
      const load = this.$load()
      return getPerformanceConversionRule().then(res => {
        const { propertyService, satisfaction } = res.data
        this.scoreForm.propertyService = propertyService
        this.scoreForm.satisfaction = satisfaction
      }).finally(() => load.close())
    },
    // 编辑绩效配置规则
    editPerformanceConversionRule() {
      this.$refs.scoreForm.validate(valid => {
        if (!valid) return
        this.$confirm('是否确定保存该绩效转换系数？', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then(() => {
          const load = this.$load()
          editPerformanceConversionRule(this.scoreForm).then(res => {
            this.$message.success(res.msg)
            this.scoreDialogShow = false
          }).finally(() => load.close())
        })
      })
    },
    // 获取节假日配置
    getHolidaysRule() {
      const load = this.$load()
      return getHolidaysRule().then(res => {
        this.festivalForm.timeList = res.data || []
      }).finally(() => load.close())
    },
    // 删除节假日
    delFestival(data) {
      this.$confirm(`是否删除节假日：${data.startTime} ～ ${data.endTime} ？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteHolidaysRule({ cfgGeneralRuleDateId: data.cfgGeneralRuleDateId }).then(res => {
          this.$message.success(res.msg)
          this.getHolidaysRule()
        }).finally(() => load.close())
      })
    },
    // 设置节假日
    setFestival() {
      const res = this.$refs.calendar.formatDate()
      if (!res) {
        this.$message.warning('请先选择日期范围！')
        return
      }
      this.$confirm(`是否设置 ${res.startTime} ～ ${res.endTime} 为节假日？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        addHolidaysRule(res).then(res => {
          this.$message.success(res.msg)
          this.$refs.calendar.initData()
          this.getHolidaysRule()
        }).finally(() => load.close())
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.g-container {
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  height: 100%;
  padding: 20px;
  ::v-deep {
    .el-date-editor.hide-prefix {
      width: 80px;
      margin-left: 8px;
      margin-right: 8px;
      &:last-child {
        margin-right: 0;
      }
      .el-input__prefix {display: none;}
      .el-input__inner {
        padding-left: 12px;
        padding-right: 12px;
      }
    }
    .el-input-number.is-without-controls {
      width: 80px;
      margin-left: 8px;
      margin-right: 8px;
    }
  }
  .m-tips {
    color: $--color-danger;
    font-size: 12px;
    display: flex;
    padding: 8px 20px 0px;
    line-height: 17px;
    i {
      line-height: 17px;
    }
    span {
      flex: 1;
      overflow: hidden;
      word-break: break-all;
      margin-left: 5px;
    }
  }
  .u-times {
    &__item {
      line-height: 32px;
      display: flex;
      align-items: center;
      cursor: pointer;
      border-radius: 4px;
      padding-left: 10px;
      padding-right: 10px;
      margin-right: 10px;
      margin-bottom: 10px;
      justify-content: space-between;
      &:last-child {
        margin-bottom: 0;
      }
      &:hover {
        background-color: #f5f5f5;
      }
      i {
        color: $--color-danger;
      }
    }
  }
}
</style>
<style lang="scss">
.u-calendar__scrollbar {
  height: 330px;
  border-right: 1px dashed #f5f5f5;
  overflow-x: hidden;
  margin-bottom: 0!important;
}
</style>
