<template>
  <div>
    <el-dialog :visible.sync="weekDialogShow" title="回访周期" width="700px" :close-on-click-modal="false" @close="$resetForm('weekForm')">
      <el-form ref="weekForm" v-loading="saveLoading" :model="weekForm">
        <el-row type="flex" align="middle">
          以
          <el-form-item prop="startMonth" style="margin-bottom: 0!important;" :rules="[{ required: true, message: '请选择'}]">
            <el-select v-model="weekForm.startMonth" placeholder="请选择" style="width: 100px;margin-left: 10px;" @change="weekChange('startMonth')">
              <el-option label="上月" :value="MONTH_TYPE.BEFORE_MONTH" />
              <el-option label="本月" :value="MONTH_TYPE.CURRENT_MONTH" />
            </el-select>
          </el-form-item>
          <el-form-item prop="startDay" style="margin-bottom: 0!important;" :rules="[{ required: true, message: '请输入'}]">
            <el-input-number v-model="weekForm.startDay" :controls="false" controls-position="right" :min="1" :max="31" :precision="0" @blur="weekDayBlur('startDay')" />
          </el-form-item>
          至
          <el-form-item prop="endMonth" style="margin-bottom: 0!important;" :rules="[{ required: true, message: '请选择'}]">
            <el-select v-model="weekForm.endMonth" placeholder="请选择" style="width: 100px;margin-left: 10px;" @change="weekChange('endMonth')">
              <el-option label="本月" :value="MONTH_TYPE.CURRENT_MONTH" />
              <el-option label="次月" :value="MONTH_TYPE.AFTER_MONTH" />
            </el-select>
          </el-form-item>
          <el-form-item prop="endDay" style="margin-bottom: 0!important;" :rules="[{ required: true, message: '请输入'}]">
            <el-input-number v-model="weekForm.endDay" :controls="false" controls-position="right" :min="1" :max="31" :precision="0" @blur="weekDayBlur('endDay')" />
          </el-form-item>
          作为通用月度回访周期
        </el-row>
      </el-form>

      <el-row type="flex" justify="space-between" align="middle" style="margin-top: 10px">
        <span class="u-title">特殊情况处理：</span>
        <el-button icon="el-icon-circle-plus-outline" type="primary" @click="editForm.cfgGeneralRuleDateId = null;editFormShow = true">添加</el-button>
      </el-row>
      <um-table-full v-loading="tableLoading" :data="tableData" scroll style="margin-top: 20px">
        <!-- <el-table-column label="触点" prop="pointName" /> -->
        <el-table-column label="调研月份" prop="month" />
        <el-table-column label="特殊设置">
          <template slot-scope="{ row }">
            {{ row.endTime }} 提前结束
          </template>
        </el-table-column>
        <el-table-column label="操作" width="110px">
          <template slot-scope="{ row }">
            <el-button type="text" @click="edit(row)">编辑</el-button>
            <el-button type="text" @click="deleteSpecialRule(row)"><span class="danger">删除</span></el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList('page')"
      />
      <!-- <span slot="footer" class="dialog-footer">
        <el-button @click="weekDialogShow = false">取 消</el-button>
        <el-button type="primary" @click="editReturnRule">保 存</el-button>
      </span> -->
    </el-dialog>
    <el-dialog :visible.sync="editFormShow" :title="`${editForm.cfgGeneralRuleDateId ? '编辑' : '添加'}`" width="400px" :close-on-click-modal="false" @close="$resetForm('editForm');">
      <el-form ref="editForm" title="添加" :model="editForm" label-width="70px" label-suffix="：">
        <el-form-item label="月份" prop="month" :rules="[{required: true, message: '请选择月份'}]">
          <el-date-picker
            v-model="editForm.month"
            type="month"
            style="width: 100%;"
            placeholder="请选择"
            value-format="yyyy-MM"
            clearable
            @change="editForm.endTime = null"
          />
        </el-form-item>
        <el-form-item label="特殊规则" prop="endTime" :rules="[{required: true, message: '请选择'}]">
          <el-date-picker
            v-model="editForm.endTime"
            type="date"
            value-format="yyyy-MM-dd"
            placeholder="请选择"
            style="margin-right: 10px;"
            clearable
            :disabled="!editForm.month"
            :picker-options="{
              disabledDate: (date) => {
                const month = new Date(editForm.month)
                return !(new Date(month.getFullYear(), month.getMonth()) < date && new Date(month.getFullYear(), month.getMonth() + 1) > date)
              },
            }"
          />提前结束
        </el-form-item>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button @click="editFormShow = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import { getReturnRule, editReturnRule, addSpecialRule, editSpecialRule, deleteSpecialRule } from '@/api/base/rule'
import { MONTH_TYPE } from '@/enum'
import getList from '@/mixins/getList'
export default {
  name: 'BaseRuleCycle',
  mixins: [getList],
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      MONTH_TYPE,
      listApi: getReturnRule,
      weekForm: {
        startMonth: null,
        startDay: undefined,
        endMonth: null,
        endDay: undefined
      },
      saveLoading: false,
      editFormShow: false,
      editForm: {
        cfgGeneralRuleDateId: null,
        pointId: null,
        month: null,
        endTime: null
      }
    }
  },
  computed: {
    weekDialogShow: {
      get() {
        if (this.show) {
          this.getList()
        }
        return this.show
      },
      set(v) {
        this.$emit('update:show', v)
      }
    }
  },
  methods: {
    afterApiCallBack({ generalReturnRule }) {
      this.weekForm = generalReturnRule
    },
    // 回访周期更改月份联动
    weekChange(type) {
      switch (type) {
        case 'startMonth': this.weekForm.startMonth === MONTH_TYPE.BEFORE_MONTH ? this.weekForm.endMonth = MONTH_TYPE.CURRENT_MONTH : this.weekForm.endMonth = MONTH_TYPE.AFTER_MONTH; break
        case 'endMonth': this.weekForm.endMonth === MONTH_TYPE.CURRENT_MONTH ? this.weekForm.startMonth = MONTH_TYPE.BEFORE_MONTH : this.weekForm.startMonth = MONTH_TYPE.CURRENT_MONTH; break
      }
      this.editReturnRule()
    },
    // 回访周期输入框联动
    weekDayBlur(type) {
      switch (type) {
        case 'startDay': this.weekForm.endDay = this.weekForm.startDay; break
        case 'endDay': this.weekForm.startDay = this.weekForm.endDay; break
      }
      this.editReturnRule()
    },
    // 编辑回访周期
    editReturnRule() {
      this.$refs.weekForm.validate(valid => {
        if (!valid) return
        this.saveLoading = true
        editReturnRule(this.weekForm).then(res => {
        }).catch(() => {
          this.$message.error('保存失败')
        }).finally(() => {
          this.saveLoading = false
        })
      })
    },
    edit(data) {
      this.editFormShow = true
      this.$nextTick(() => {
        this.editForm = { ...data }
      })
    },
    // 删除指标、满意、不满意原因
    deleteSpecialRule(data) {
      this.$confirm('是否确定删除该周期？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteSpecialRule({ cfgGeneralRuleDateId: data.cfgGeneralRuleDateId }).then(res => {
          this.$message.success(res.msg)
          this.getList()
        }).finally(() => load.close())
      })
    },
    confirm() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        const load = this.$load()
        const API = this.editForm.cfgGeneralRuleDateId ? editSpecialRule : addSpecialRule
        API(this.editForm).then(res => {
          this.$message.success(res.msg)
          this.getList()
          this.editFormShow = false
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scope>
.u-title {
    font-size: 16px;
    color: #333;
    font-weight: 500;
}
</style>
