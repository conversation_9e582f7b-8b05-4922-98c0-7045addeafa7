<template>
  <el-dialog :visible.sync="deepDialogShow" title="深访问卷推送配置" width="980px" :close-on-click-modal="false" @close="$resetForm('deepForm')">
    <el-form
      ref="deepForm"
      v-loading="saveLoading"
      :model="deepForm"
      label-width="130px"
      label-position="left"
    >
      <el-row :gutter="30">
        <el-col :span="24" class="flex">
          <el-form-item
            prop="questionnaireTypeId"
            :rules="[{
              required: true, trigger: 'change', message: '请选择问卷类型'
            }]"
            label="问卷类型"
          >
            <div slot="label">问卷类型<span style="font-size: 12px;">(对客展示)</span>：</div>
            <el-select v-model="deepForm.questionnaireTypeId" placeholder="请选择" style="width: 100%" clearable>
              <el-option v-for="item in questionTypes" :key="item.cfgDictId" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="flex">
          <el-form-item
            prop="quesEffectValue"
            :rules="[{
              required: true, trigger: 'change', message: '请输入'
            }]"
            label="问卷有效期："
          >
            <div class="errorTime" style="margin-left: -16px">
              <el-form-item
                ref="quesEffectValue"
                prop="quesEffectValue"
                label=""
                label-width="0"
              >
                <el-input-number
                  v-model="deepForm.quesEffectValue"
                  style="width: 110px"
                  :min="0"
                  :max="999"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">天</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="flex">
          <el-form-item
            prop="channelTypes"
            :rules="[{
              required: true, validator: validatePlanMsgContent, trigger: 'change'
            }]"
            label="推送通道："
          >
            <pushChannel ref="pushChannel" v-model="deepForm.channelTypes" style="width: 350px;" />
          </el-form-item>
        </el-col>
        <el-col :span="24" class="flex">
          <el-form-item
            prop="surveyRate"
            label="推送比例："
            :rules="[{ required: true, message: '请输入推送比例', trigger: 'blur' }]"
          >
            <div class="errorTime" style="margin-left: -16px">
              <el-form-item
                ref="surveyRate"
                prop="surveyRate"
                label=""
                label-width="0"
              >
                <el-input-number
                  v-model="deepForm.surveyRate"
                  style="width: 110px"
                  :min="0"
                  :max="100"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">%</span>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24" class="flex">
          <el-form-item
            prop="retrySendFlag"
            label="失败补偿："
            :rules="[{ required: true, }]"
          >
            <el-switch v-model="deepForm.retrySendFlag" />
          </el-form-item>
          <div v-if="deepForm.retrySendFlag" class="errorTime">
            <div>对推送失败的消息，在</div>
            <el-form-item
              prop="retrySendDay"
              label=""
              :rules="[{ required: deepForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
            >
              <el-input-number
                v-model="deepForm.retrySendDay"
                style="width: 110px"
                :min="0"
                :max="31"
                :precision="0"
                :controls="false"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <span class="margin-r-14 margin-l-10">天</span>
            <el-form-item
              prop="retrySendHour"
              label=""
              :rules="[{ required: deepForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
            >
              <el-input-number
                v-model="deepForm.retrySendHour"
                style="width: 110px"
                :min="0"
                :max="24"
                :precision="0"
                :controls="false"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <span class=" margin-r-14 margin-l-10">时</span>
            <el-form-item
              :rules="[{ required: deepForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
              prop="retrySendMinute"
              label=""
            >
              <el-input-number
                v-model="deepForm.retrySendMinute"
                style="width: 110px"
                :min="0"
                :max="60"
                :precision="0"
                :controls="false"
                placeholder="请输入"
                clearable
              />
            </el-form-item>
            <span class="margin-l-10">分钟后再推送一次</span>
          </div>
          <div />
        </el-col>
        <el-col :span="24" class="flex">
          <el-form-item
            prop="recycleSendFlag"
            label="回收补偿："
            :rules="[{ required: true, }]"
          >
            <el-switch v-model="deepForm.recycleSendFlag" />
          </el-form-item>
          <div v-if="deepForm.recycleSendFlag" class="errorTime">
            <div class="f-flex">
              当问卷发放
              <el-form-item
                prop="recycleSendDay"
                label=""
                :rules="[{ required: deepForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="deepForm.recycleSendDay"
                  style="width: 110px"
                  :min="0"
                  :max="31"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">天</span>
              <el-form-item
                :rules="[{ required: deepForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                prop="recycleSendHour"
                label=""
              >
                <el-input-number
                  v-model="deepForm.recycleSendHour"
                  style="width: 110px"
                  :min="0"
                  :max="24"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">时</span>
              <el-form-item
                :rules="[{ required: deepForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                prop="recycleSendMinute"
                label=""
              >
                <el-input-number
                  v-model="deepForm.recycleSendMinute"
                  style="width: 110px"
                  :min="0"
                  :max="60"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">分后，对未回收的问卷再推送一次</span>
            </div>
          </div>
        </el-col>
      </el-row>
    </el-form>
    <div slot="footer">
      <el-button @click="deepDialogShow = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { getDepthPoint, eidtDepthPoint } from '@/api/base/rule'
import { CHANNEL_TYPE, DICT_CODE } from '@/enum'
import { getDictList } from '@/api/common'
import pushChannel from '@/views/plan/components/pushChannel/index.vue'

export default {
  name: 'BaseRuleCycle',
  components: {
    pushChannel
  },
  props: {
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      deepForm: {
        questionnaireTypeId: null,
        surveyRate: undefined, // 推送比例
        retrySendFlag: false, // 是否失败补偿
        channelTypes: [
          {
            title: '短信消息',
            channelTypeCode: CHANNEL_TYPE.MSG,
            isChoose: false,
            firstContent: ''
          }
        ], // 推送渠道
        smsContent: null,
        recycleSendFlag: false, // 回收补偿标识
        quesEffectValue: undefined, // 问卷有效期值
        retrySendDay: undefined, // 失败补偿天
        retrySendHour: undefined, // 失败补偿小时
        retrySendMinute: undefined, // 失败补偿分钟
        recycleSendDay: undefined, // 回收补偿天
        recycleSendHour: undefined, // 回收补偿小时
        recycleSendMinute: undefined // 回收补偿分钟
      },
      saveLoading: false,
      questionTypes: []
    }
  },
  computed: {
    deepDialogShow: {
      get() {
        if (this.show) {
          this.getDetail()
        }
        return this.show
      },
      set(v) {
        this.$emit('update:show', v)
      }
    }
  },
  created() {
    this.getDictList(DICT_CODE.QUESTION_TYPE, 'questionTypes')
  },
  methods: {
    getDetail() {
      const load = this.$load()
      getDepthPoint().then(res => {
        const obj = res.data
        obj && (this.deepForm = {
          ...obj,
          retrySendFlag: !!obj.retrySendFlag,
          recycleSendFlag: !!obj.recycleSendFlag,
          retrySendDay: obj.retrySendDay === null ? undefined : obj.retrySendDay, // 失败补偿天
          retrySendHour: obj.retrySendHour === null ? undefined : obj.retrySendHour, // 失败补偿小时
          retrySendMinute: obj.retrySendMinute === null ? undefined : obj.retrySendMinute, // 失败补偿分钟
          recycleSendDay: obj.recycleSendDay === null ? undefined : obj.recycleSendDay, // 回收补偿天
          recycleSendHour: obj.recycleSendHour === null ? undefined : obj.recycleSendHour, // 回收补偿小时
          recycleSendMinute: obj.recycleSendMinute === null ? undefined : obj.recycleSendMinute, // 回收补偿分钟
          channelTypes: [
            {
              title: '短信消息',
              channelTypeCode: CHANNEL_TYPE.MSG,
              isChoose: true,
              firstContent: obj.smsContent
            }
          ]
        })
        load.close()
      }).catch(() => {
        load.close()
      })
    },
    // 获取满意度数据字典
    getDictList(dictCode, key) {
      return getDictList({ dictCode }).then(res => {
        this[key] = res.data || []
      })
    },
    validatePlanMsgContent(rule, value, callback) {
      const length = value.length
      const checkLength = value.filter(i => i.isChoose).length
      if (checkLength === 0 || length === 0) {
        const text = '请选择推送通道'
        return callback(text)
      }
      const msg = value.find(i => i.channelTypeCode === CHANNEL_TYPE.MSG) // 短信
      if (msg.isChoose && !msg.firstContent) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      callback()
    },
    confirm() {
      this.$refs.deepForm.validate(valid => {
        if (!valid) return
        this.deepForm.smsContent = this.deepForm.channelTypes[0].firstContent
        const load = this.$load()
        eidtDepthPoint({
          ...this.deepForm,
          retrySendFlag: +this.deepForm.retrySendFlag,
          recycleSendFlag: +this.deepForm.recycleSendFlag,
          channelTypes: undefined
        }).then(res => {
          this.$message.success(res.msg)
          this.deepDialogShow = false
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scope>
.u-title {
  font-size: 16px;
  color: #333;
  font-weight: 500;
}
.errorTime{
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-left: 18px;
  .el-form-item__content{
    margin-left: 8px !important;
  }
}
</style>
