<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="计划编号：">
          <el-input v-model="searchForm1.planCode" placeholder="请输入计划编号" clearable />
        </el-form-item>
        <el-form-item label="计划名称：">
          <el-input v-model="searchForm1.planName" placeholder="请输入计划名称" clearable />
        </el-form-item>
        <el-form-item label="问卷名称：">
          <el-input v-model="searchForm1.quesTemplateName" placeholder="请输入问卷名称" clearable />
        </el-form-item>
        <el-form-item label="关联触点：">
          <el-cascader
            v-model="searchForm1.pointIds"
            :options="pointOptions"
            collapse-tags
            style="width: 100%;"
            filterable
            :props="{
              multiple: true,
              emitPath: false
            }"
            clearable
          />
        </el-form-item>
        <el-form-item label="调研范围：" prop="planScope">
          <UmBusOrgan v-model="searchForm1.projDto" is-stage />
        </el-form-item>
        <el-form-item :label="searchTimeLabel[searchForm1.statusCode]">
          <el-date-picker
            v-model="dateRange"
            clearable
            type="datetimerange"
            range-separator="至"
            format="yyyy-MM-dd HH:mm:ss"
            value-format="yyyy-MM-dd HH:mm:ss"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width:100%"
          />
        </el-form-item>
      </template>
      <template #suffix>
        <el-button type="primary" :loading="tableLoading1" icon="el-icon-search" style="margin-bottom: 20px;" @click="search">搜索</el-button>
      </template>
    </UmSearchLayout>

    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row>
        <el-col :span="18">
          <CommonTabs :tabs="tabList" @changeTab="changeTab">
            <div slot="btn_2" slot-scope="{data}">
              <div>
                {{ data.label }}
                <span v-if="data.count !== null && data.count !== undefined">({{ data.count }})</span>
              </div>
              <div style="font-size: 12px;line-height: 1;transform: translateY(-5px);">
                审核通过
              </div>
            </div>
          </commontabs>
        </el-col>
        <el-col :span="6">
          <el-button
            v-if="$checkPermission(['DYJHGL_SFDY_XZJH'])"
            style="float: right"
            icon="el-icon-circle-plus-outline"
            type="primary"
            @click="$router.push('/plan/depth/add')"
          >
            新增计划
          </el-button>
        </el-col>
      </el-row>
      <template v-if="![PLAN_STATUS.IN_AUDIT, PLAN_STATUS.PASS, PLAN_STATUS.COMPLANT].includes(searchForm1.statusCode)">
        <um-table-full :data="tableData1" class="margin-t-20" scroll>
          <el-table-column prop="xh" label="序号" width="80" />
          <el-table-column
            label="计划编号"
            prop="planCode"
            min-width="180"
          />
          <el-table-column
            label="计划名称"
            prop="name"
            min-width="200"
            show-overflow-tooltip
          >
            <template slot-scope="scope">
              <div class="ellipsis-btn" @click="$router.push(`/plan/depth/info?id=${scope.row.uuid}`)">
                {{ scope.row.name | formatterTable }}
              </div>
            </template>
          </el-table-column>
          <el-table-column
            label="调研范围"
            min-width="200"
            show-overflow-tooltip
          >
            <span slot-scope="{ row }">{{ row.scopeNames ? row.scopeNames.join('、') : '全部' }}</span>
          </el-table-column>
          <el-table-column prop="pointNames" label="关联触点" min-width="180" show-overflow-tooltip>
            <template slot-scope="{ row }">{{ row.pointNames ? row.pointNames.join('、') : '--' }}</template>
          </el-table-column>
          <el-table-column prop="creator" label="创建人" width="120" />
          <el-table-column prop="createTime" label="创建时间" width="170" />
          <el-table-column label="操作" width="180" fixed="right">
            <template slot-scope="scope">
              <template v-if="hasAuthPlan.includes(scope.row.uuid) || $checkPermission(['DYJHGL_SFDY_GLYCZ'])">
                <el-button v-if="$checkPermission(['DYJHGL_SFDY_BJ']) && [PLAN_STATUS.UN_PUBLISH, PLAN_STATUS.RETURN].includes(scope.row.statusCode)" type="text" @click="$router.push('/plan/depth/edit?id=' + scope.row.uuid)">编辑</el-button>
                <el-button v-if="$checkPermission(['DYJHGL_SFDY_FB']) && scope.row.statusCode === PLAN_STATUS.UN_PUBLISH" type="text" @click="publishPlan(scope.row)">发布</el-button>
                <el-button
                  v-if="$checkPermission(['DYJHGL_SFDY_SC']) && scope.row.statusCode === PLAN_STATUS.UN_PUBLISH"
                  type="text"
                  style="color: #FF6161;"
                  @click="deletePlan(scope.row)"
                >
                  删除
                </el-button>
              </template>
              <el-button v-if="$checkPermission(['DYJHGL_SFDY_XQ']) && scope.row.statusCode === !PLAN_STATUS.UN_PUBLISH" type="text" @click="publishPlan(scope.row)">详情</el-button>
            </template>
          </el-table-column>
        </um-table-full>
        <pagination
          :total="tableTotal1"
          :page.sync="searchForm1.page.pageNum"
          :limit.sync="searchForm1.page.pageSize"
          @pagination="getList1('page')"
        />
      </template>
      <under-nav
        v-else
        ref="underNav"
        :params="searchForm1"
        :status-code="searchForm1.statusCode"
      />
    </div>
  </div>
</template>

<script>
import { pageList, publishPlan, deletePlan, getOverlap, hasAuthPlan } from '@/api/plan/depth'
import getList1 from '@/mixins/getList1'
import UnderNav from './UnderNav.vue'
import { PLAN_STATUS, PLAN_RANGE } from '@/enum'
import CommonTabs from '../../../components/CommonTabs'
import UmBusOrgan from '@/components/UmBusOrgan'
import getPointList from '@/mixins/getPointList'
export default {
  name: 'PlanDepth',
  components: {
    UnderNav,
    CommonTabs,
    UmBusOrgan
  },
  mixins: [getList1, getPointList],
  props: {},
  data() {
    return {
      PLAN_STATUS,
      option: [
        {
          label: '全部',
          value: PLAN_RANGE.ALL
        },
        {
          label: '指定项目',
          value: PLAN_RANGE.PROJECT
        },
        {
          label: '指定客户',
          value: PLAN_RANGE.CUSTOM
        }
      ],
      listApi1: pageList,
      dateRange: null,
      searchForm1: {
        planCode: null,
        planName: null,
        quesTemplateName: null,
        timeBegin: null,
        timeEnd: null,
        statusCode: PLAN_STATUS.UN_PUBLISH,
        pointIds: null,
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null,
          projectId: null,
          stageId: null
        }
      },
      tabList: [
        {
          label: '待发布',
          value: PLAN_STATUS.UN_PUBLISH,
          count: 0,
          key: 'draft'
        },
        {
          label: '审批中',
          value: PLAN_STATUS.IN_AUDIT,
          count: 0,
          key: 'auditing'
        },
        {
          label: '调研中',
          value: PLAN_STATUS.PASS,
          count: 0,
          key: 'survey'
        },
        {
          label: '已完成',
          value: PLAN_STATUS.COMPLANT,
          count: 0,
          key: 'finish'
        },
        {
          label: '审批不通过',
          value: PLAN_STATUS.NO_PASS,
          count: 0,
          key: 'reject'
        },
        {
          label: '驳回',
          value: PLAN_STATUS.RETURN,
          count: 0,
          key: 'return'
        }
      ],
      searchTimeLabel: {
        [PLAN_STATUS.UN_PUBLISH]: '更新时间：',
        [PLAN_STATUS.IN_AUDIT]: '更新时间：',
        [PLAN_STATUS.PASS]: '发布时间：',
        [PLAN_STATUS.COMPLANT]: '结束时间：',
        [PLAN_STATUS.NO_PASS]: '更新时间：',
        [PLAN_STATUS.RETURN]: '更新时间：'
      },
      hasAuthPlan: [] // 有操作权限的计划id集合
    }
  },
  watch: {
    dateRange(v) {
      if (v) {
        this.searchForm1.timeBegin = v[0]
        this.searchForm1.timeEnd = v[1]
      } else {
        this.searchForm1.timeBegin = null
        this.searchForm1.timeEnd = null
      }
    }
  },
  created() {
    this.getPointOption(false, 2200042)
  },
  activated() {
    this.search() // 缓存过来的刷新页面
  },
  methods: {
    search() {
      if (![PLAN_STATUS.IN_AUDIT, PLAN_STATUS.PASS, PLAN_STATUS.COMPLANT].includes(this.searchForm1.statusCode)) {
        this.getList1()
      } else {
        this.$nextTick(() => {
          this.$refs.underNav.getList1()
        })
      }
    },
    // tab上的统计数据
    tableCallBack1({ stateCount, list = [] }) {
      // 获取数据的操作权限，返回的id代表有操作权限
      list.length && hasAuthPlan(list.map(item => item.uuid)).then(res => {
        this.hasAuthPlan = res.data || []
      })
      const count = stateCount || this.tableCount1
      this.tabList.forEach(item => {
        item.count = count[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.search()
    },
    /**
     * 删除计划方法
     * @param spPlanId
     * @param planName
     */
    deletePlan({ uuid, name }) {
      this.$confirm(`是否确定删除计划：${name}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then((_) => {
        const load = this.$load()
        deletePlan({ uuid }).then((res) => {
          this.$message.success('操作成功')
          this.getList1()
        }).finally(_ => {
          load.close()
        })
      }).catch((_) => {
      })
    },
    /**
     * 发布方法
     * @param planName
     * @param spPlanId
     */
    publishPlan({ uuid, name }, cb) {
      this.$confirm(`是否确定发布计划：${name}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(async(_) => {
        const load = this.$load()
        try {
          const errInfo = await getOverlap({ uuid })
          if (errInfo.msg) {
            this.$confirm(errInfo.msg, '以下触点-月份-项目本年已存在调研，本次计划将不调研', {
              showClose: false
            }).then(() => {
              this.confirmPublish({ uuid }, load, cb)
            }).catch(() => {
              load.close()
            })
            return
          }
          this.confirmPublish({ uuid }, load, cb)
        } catch (error) {
          load.close()
        }
      }).catch((_) => {
      })
    },
    confirmPublish(params, load, cb) {
      publishPlan(params).then((res) => {
        this.$message.success('操作成功')
        load.close()
        if (typeof cb === 'function') {
          cb()
          return
        }
        this.getList1()
      }).catch(_ => {
        load.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.zwsj {
  margin: 100px auto;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;

  .zwsj-h2 {
    font-size: 14px;
    color: #909399;
    line-height: 24px;
  }
}

.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

.ellipsis-btn {
  cursor: pointer;
  white-space: nowrap;
  overflow: hidden;
  text-overflow: ellipsis;
  display: inline-block;
  width: 100%;
  color: $--color-primary;
}

</style>
