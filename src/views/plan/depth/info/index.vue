<template>
  <div class="m_plan_info">
    <el-form class="detailForm">
      <el-card shadow="never">
        <div class="g_card_bg">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">基础信息</div>
          </div>
        </div>
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="12">
            <el-form-item label-width="126px" label="计划名称：">
              <span>{{ detail.name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label-width="126px" label="深访问卷：">
              <span>{{ detail.quesTemplateName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label-width="126px" label="选择调研题目：">
              <span>{{ detail.requiredQuestionItemNames && detail.requiredQuestionItemNames.length ? detail.requiredQuestionItemNames.join('、') : '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="选择调研触点及对应月份：">
              <el-table :data="detail.triggerNodes">
                <el-table-column label="序号" type="index" />
                <el-table-column prop="pointName" label="触点" min-width="300" />
                <el-table-column prop="surveyMonths" label="考核月份" min-width="300">
                  <template slot-scope="scope">
                    {{ scope.row.surveyMonths ? scope.row.surveyMonths.join('、') : '--' }}
                  </template>
                </el-table-column>
                <el-table-column label="对客问卷名称" prop="questionnaireName" width="300" />
              </el-table>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label-width="126px" label="调研范围：">
              <span>{{ detail.scopeNames ? detail.scopeNames.join('、') : '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col v-if="detail.remark" :span="24">
            <el-form-item label-width="126px" label="补充说明：">
              <div>以下触点-月份-项目本年已存在调研，本次推送计划将不调研</div>
              <el-input :value="detail.remark" type="textarea" resize="none" :rows="5" disabled />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never">
        <div class="g_card_bg">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">推送规则</div>
          </div>
        </div>
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="12">
            <el-form-item label-width="126px" label="是否推送短信：">
              {{ detail.sendSmsFlag ? '是' : '否' }}
            </el-form-item>
          </el-col>
          <el-col v-if="detail.sendSmsFlag" :span="12">
            <el-form-item label-width="126px" label="短信费用归属：">
              <span>{{ detail.smsFeeName }}</span>
            </el-form-item>
          </el-col>
          <el-col style="margin-top: -20px;margin-bottom: 20px;padding-left: 10px;color: #999;" :span="24">备注：是否推送短信都会默认会通过小程序推送该问卷。</el-col>
          <el-col :span="24">
            <el-form-item label-width="126px" label="当月调研结束后：">
              <span>{{ detail.relativeSurveyMonth }}月后{{ detail.relativeSurveyDay }}号推送</span>
            </el-form-item>
          </el-col>
          <el-col style="padding-left: 10px;color: #999;margin-top: -20px;margin-bottom: 20px" :span="24">备注：若选择月份超过了配置的推送日期或审批时间过超过了推送日期，则对应月份不会调研。</el-col>
        </el-row>
      </el-card>
      <el-card shadow="never">
        <div class="g_card_bg">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">呈文内容</div>
          </div>
        </div>
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="24">
            <el-form-item label="呈文摘要：">
              <el-input :value="detail.oaApplyContent" type="textarea" resize="none" :rows="5" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label-width="126px" label="附件：">
              <um-upload-file v-model="detail.files" :limit="5" disabled :size="200 * 1024" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { getDetail } from '@/api/plan/depth'
import { PLAN_RANGE } from '@/enum'
export default {
  name: 'PlanSatisfactionInfo',
  data() {
    return {
      PLAN_RANGE,
      detail: {
      }
    }
  },
  created() {
    const { id } = this.$route.query
    this.getDetail(id)
  },
  methods: {
    getDetail(uuid) {
      const load = this.$load()
      getDetail({ uuid }).then(res => {
        const { files } = res.data
        const obj = {
          ...res.data,
          files: files?.map(item => {
            return {
              filePath: item,
              fileName: item.slice(item.lastIndexOf('/') + 1)
            }
          })
        }

        this.detail = obj
      }).finally(() => load.close())
    }

  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/modules/plan';

</style>
