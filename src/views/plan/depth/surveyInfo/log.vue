<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form label-suffix="：">
        <el-row type="flex" justify="space-between">
          <el-row type="flex">
            <el-form-item label="操作人" label-width="80px" class="mb-0 mr-40">
              <el-input v-model="searchForm.operatorName" placeholder="请输入" clearable />
            </el-form-item>
            <el-form-item label="操作时间" label-width="80px" class="mb-0">
              <el-date-picker
                v-model="dateRange"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
          </el-row>
          <el-form-item label="" class="mb-0">
            <el-button :loading="tableLoading" type="primary" icon="el-icon-search" @click="search">查询</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>
    <div v-loading="tableLoading" class="g-table_full margin-t-20">
      <um-table-full :data="tableData" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column prop="creteTime" label="操作时间" width="200px" />
        <el-table-column prop="operatorName" label="操作人" width="120px" />
        <el-table-column prop="content" label="操作内容" />
        <el-table-column prop="operate" label="操作" width="120">
          <template slot-scope="{ row }">
            <el-button type="text" @click="showDetail(row)">详情</el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList('page')"
      />
    </div>
    <LogDetail :id="logId" :show.sync="showDetailDialog" />
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { getLog } from '@/api/plan/depth'
import LogDetail from './components/LogDetail.vue'

export default {
  name: 'PlanDepthSurveyInfoLog',
  components: { LogDetail },
  mixins: [getList],
  props: {},
  data() {
    return {
      listApi: getLog,
      dateRange: null,
      showDetailDialog: false,
      logId: null,
      searchForm: {
        planUuid: null,
        operatorName: null,
        timeBegin: null,
        timeEnd: null
      }
    }
  },
  created() {
    this.searchForm.planUuid = this.$route.query.id
    this.getList()
  },
  activated() {
    this.getList()
  },
  methods: {
    search() {
      if (this.dateRange) {
        [this.searchForm.timeBegin, this.searchForm.timeEnd] = this.dateRange
      } else {
        [this.searchForm.timeBegin, this.searchForm.timeEnd] = [null, null]
      }
      this.getList()
    },
    showDetail(data) {
      this.logId = data.id
      this.showDetailDialog = true
    }
  }
}
</script>

  <style lang="scss" scoped>
  .mb-20 {
    margin-bottom: 20px;
  }
  .mr-40 {
    margin-right: 40px;
  }
  .mb-0 {
    margin-bottom: 0;
  }

  .g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    .u-tips {
      line-height: 24px;
      background: rgba(#FF9645, 0.1);
      border-radius: 4px;
      color: #FF9645;
      padding-left: 12px;
      font-size: 12px;
      position: relative;
      margin-bottom: 12px;
      font-weight: 500;
      &::before {
        content: '';
        width: 2px;
        height: 12px;
        background: #FF9645 ;
        position: absolute;
        left: 0;
        top: 6px;
      }
    }
  }
  </style>

