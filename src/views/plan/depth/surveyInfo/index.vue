
<template>
  <div class="g-container_flex">
    <UmSearchLayout label-width="100px">
      <template #topfix>
        <div class="m-top-title">
          <div>计划名称：{{ planName ||"--" }}</div>
          <div class="margin-l-20">触点：{{ pointName||"--" }}</div>
        </div>
        <div class="m-block">
          <div v-for="item in topDataConfig" :key="item.label" class="m-block__item">
            <div class="m-block__item--num margin-b-10">{{ topData[item.key] || 0 }}{{ item.unit }}</div>
            <span class="m-block__item--label">{{ item.label }}</span>
          </div>
        </div>
      </template>
      <template #default>
        <el-form-item prop="orgProjectUuid" label="区域/城市：">
          <UmBusOrgan v-model="searchForm1.projDto" />
        </el-form-item>
        <el-form-item prop="orgProjectUuid" label="项目/分期：">
          <UmBusProject v-model="searchForm1.projDto" :area-company-id="searchForm1.projDto.areaCompanyId" :city-company-id="searchForm1.projDto.cityCompanyId" />
        </el-form-item>
        <el-form-item label="姓名：" prop="houseIdLike">
          <el-input v-model="searchForm1.customerName" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="手机号：" prop="houseIdLike">
          <el-input v-model="searchForm1.customerMobile" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="楼栋：">
          <UmBusHouse v-model="houseDto" :proj-dto="searchForm1.projDto" value-key="buildingId" />
        </el-form-item>
        <el-form-item label="单元：">
          <UmBusHouse v-model="houseDto" :proj-dto="searchForm1.projDto" value-key="unitId" />
        </el-form-item>
        <el-form-item label="房号：">
          <UmBusHouse v-model="houseDto" :proj-dto="searchForm1.projDto" value-key="houseId" />
        </el-form-item>
        <el-form-item label="装修类型：" prop="decorateTypeCode">
          <el-select v-model="searchForm1.decorateTypeCode" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="毛坯" :value="DECORATION_STATUS.SIMPLE" />
            <el-option label="精装" :value="DECORATION_STATUS.FINE" />
          </el-select>
        </el-form-item>
        <el-form-item prop="month" label="考核月份：">
          <el-select v-model="searchForm1.month" clearable style="width: 100%;">
            <el-option v-for="(item, index) in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二', ]" :key="item" :label="item + '月'" :value="index + 1" />
          </el-select>
        </el-form-item>
        <el-form-item prop="openMonth" label="深访月份：">
          <el-date-picker
            v-model="searchForm1.openMonth"
            type="month"
            format="yyyy年MM月"
            value-format="yyyy-MM"
            placeholder="请选择"
            style="width: 100%"
            clearable
          />
        </el-form-item>
        <el-form-item label="回收时间">
          <el-date-picker
            v-model="timeList"
            clearable
            type="datetimerange"
            range-separator="至"
            format="yyyy-MM-dd HH:mm"
            value-format="yyyy-MM-dd HH:m"
            start-placeholder="开始时间"
            end-placeholder="结束时间"
            style="width:100%"
          />
        </el-form-item>
        <el-form-item prop="rawPointIds" label="所属触点：">
          <el-cascader
            v-model="searchForm1.rawPointIds"
            :options="pointOptions"
            collapse-tags
            style="width: 100%;"
            filterable
            :props="{
              multiple: true,
              emitPath: false
            }"
            clearable
          />
        </el-form-item>
      </template>
    </UmSearchLayout>
    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row type="flex" justify="space-between">
        <CommonTabs class="margin-b-20" :tabs="tabs" @changeTab="changeTab" />
        <div>
          <template v-if="(hasAuthPlan || $checkPermission(['DYJHGL_SFDY_GLYCZ'])) && $checkPermission(['DYJHGL_SFDY_DYXQ_BDY'])">
            <el-button v-show="searchForm1.tabType === 0" type="primary" @click="batchCancel">批量取消调研</el-button>
          </template>
          <template v-if="(hasAuthPlan || $checkPermission(['DYJHGL_SFDY_GLYCZ'])) && $checkPermission(['DYJHGL_SFDY_DYXQ_HFDY'])">
            <el-button v-show="searchForm1.tabType === 2" type="primary" @click="batchRetry">批量恢复调研</el-button>
          </template>
          <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_CZRZ'])" type="primary" @click="$router.push('/plan/depth/surveyInfoLog?id=' + searchForm1.uuid)">操作日志</el-button>
          <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_DC'])" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button>
          <el-button type="primary" :loading="tableLoading1" icon="el-icon-search" style="margin-bottom: 20px;" @click="getList1">搜索</el-button>
        </div>
      </el-row>

      <um-table-full
        ref="table"
        :data="tableData1"
        scroll
        style="width: 100%;"
        row-key="uuid"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          v-if="searchForm1.tabType !== 1 && searchForm1.tabType!== 3"
          key="selection"
          reserve-selection
          type="selection"
          :selectable="selectable"
          fixed="left"
          width="50"
        />
        <el-table-column key="xh" fixed="left" prop="xh" width="80" label="序号" />
        <el-table-column
          key="customerName"
          label="姓名"
          prop="customerName"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="customerMobile"
          label="电话"
          prop="customerMobile"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="buildingName"
          label="楼栋"
          prop="buildingName"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="unitName"
          label="单元"
          prop="unitName"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="doorNo"
          label="房号"
          prop="doorNo"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="areaCompanyName"
          label="所属大区"
          prop="areaCompanyName"
          :formatter="$formatterTable"
          width="120"
        />
        <el-table-column
          key="cityCompanyName"
          label="城市"
          prop="cityCompanyName"
          :formatter="$formatterTable"
          width="120"
        />
        <el-table-column
          key="projectName"
          label="项目"
          prop="projectName"
          :formatter="$formatterTable"
          width="140"
        />
        <el-table-column
          key="stageName"
          label="分期"
          prop="stageName"
          :formatter="$formatterTable"
          width="100"
        />
        <el-table-column
          key="rawPointName"
          label="所属触点"
          prop="rawPointName"
          :formatter="$formatterTable"
          width="140"
        />
        <el-table-column
          key="decorationStandardName"
          label="装修类型"
          prop="decorationStandardName"
          :formatter="$formatterTable"
          width="100"
        />
        <el-table-column
          key="actualSendTime"
          label="调研时间"
          prop="actualSendTime"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          v-show="searchForm1.tabType === 1"
          key="recycleTime"
          label="回收时间"
          prop="recycleTime"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          key="sendRuleCode"
          label="调研方式"
          prop="sendRuleCode"
          min-width="250"
        >
          <template slot-scope="{ row }">
            <div v-for="(item,index) in row.sendRecords" :key="index" class="f-flex-ac">
              <div>{{ item.channelTypeName }}</div>
              <div :class="['m-tag', pushStatusColor[item.statusCode]]">{{ pushStatus[item.statusCode] }}</div>
              <el-popover
                v-if="item.failReason"
                placement="bottom"
                trigger="hover"
                :content="item.failReason"
              >
                <img slot="reference" src="~@/assets/plan/tips.png" style="width: 16px;height: 16px;cursor: pointer;margin-top: 8px" alt="">
              </el-popover>
            </div>
            <div v-if="!row.sendRecords || !row.sendRecords.length">--</div>
          </template>
        </el-table-column>
        <el-table-column
          v-if="searchForm1.tabType === 2"
          key="remark"
          label="说明"
          prop="remark"
          min-width="200"
          :formatter="$formatterTable"
        />
        <el-table-column
          label="操作"
          min-width="160"
          fixed="right"
        >
          <!-- 后台权限和超级管理员权限满足其一 -->
          <template v-if="searchForm1.tabType !== 3 && (hasAuthPlan || $checkPermission(['DYJHGL_SFDY_GLYCZ']))" slot-scope="scope">
            <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_BDY']) && searchForm1.tabType === 0 && scope.row.statusCode === 2200341 && scope.row.operateFlag" type="text" @click="cancel(scope.row)">不调研</el-button>
            <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_CXTS']) && searchForm1.tabType === 0 && scope.statusCode === 2200344" type="text" @click="registration(scope.row)">重新推送</el-button>
            <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_CKWJ']) && searchForm1.tabType === 1" type="text" @click="seeDj(scope.row)">查看答卷</el-button>
            <!-- 已作废状态不现实作废 -->
            <template v-if="searchForm1.tabType === 1">
              <el-button v-if="scope.row.statusCode !== 2200348 && $checkPermission(['DYJHGL_SFDY_DYXQ_ZF'])" type="text" @click="del(scope.row)"><span class="danger">作废</span></el-button>
              <span v-else>已作废</span>
            </template>
            <el-button v-if="$checkPermission(['DYJHGL_SFDY_DYXQ_HFDY']) && searchForm1.tabType === 2 && scope.row.operateFlag" type="text" @click="retry(scope.row)">恢复调研</el-button>
          </template>
        </el-table-column>
      </um-table-full>

      <pagination
        style="margin-top: 20px;"
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        @pagination="getList1('page')"
      />
    </div>
  </div>
</template>

<script>
import { getTopData, quesPageList } from '@/api/plan/common'
import { operateQues } from '@/api/plan/depth'
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs'
import { exportSurveyDetail } from '@/api/export'
import exportData from '@/mixins/exportData'
import { DECORATION_STATUS } from '@/enum'
import getPointList from '@/mixins/getPointList'
export default {
  name: 'PlanDepthSurveyInfo',
  components: { CommonTabs },
  mixins: [getList1, exportData, getPointList],
  props: {
    type: {
      type: String,
      default: ''
    }
  },
  data() {
    const pushStatus = {
      2200342: '推送中',
      2200343: '推送成功',
      2200344: '推送失败'
    }
    const pushStatusColor = {
      2200342: 'pending',
      2200343: 'success',
      2200344: 'error'
    }
    return {
      DECORATION_STATUS,
      topData: {},
      exportApi: exportSurveyDetail,
      pushStatus,
      pushStatusColor,
      listApi1: quesPageList,
      hasAuthPlan: false, // 后台控制的按钮操作权限
      tableCount: {},
      searchForm1: {
        uuid: null,
        planId: null,
        tabType: 3,
        decorationStandard: null,
        recycleTimeBegin: null,
        recycleTimeEnd: null,
        surveyMonth: null,
        openMonth: null,
        customerInfo: null,
        doorNo: null,
        month: null,
        buildingId: null,
        unitId: null,
        projDto: {},
        rawPointIds: [],
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      tabs: [
        {
          label: '全部',
          value: 3,
          count: 0,
          key: 'all'
        },
        {
          label: '待回收',
          value: 0,
          count: 0,
          key: 'unRecycle'
        },
        {
          label: '已回收',
          value: 1,
          count: 0,
          key: 'recycled'
        },
        {
          label: '不调研',
          value: 2,
          count: 0,
          key: 'unSurvey'
        }
      ],
      timeList: [],
      topDataConfig: [
        {
          label: '样框量',
          key: 'frameNum'
        },
        {
          label: '样本量',
          key: 'totalSendNum'
        },
        {
          label: '平均答题时长',
          key: 'avgAnswerTime'
        },
        {
          label: '今日回收',
          key: 'todayRecycleNum'
        },
        {
          label: '回收率',
          key: 'recycleRate',
          unit: '%'
        }
      ],
      houseDto: {
        buildingId: null,
        unitId: null,
        houseId: null
      },
      multipleSelection: [],
      planName: '', // 计划名称
      pointName: '' // 触点
    }
  },
  watch: {
    'timeList'(val) {
      if (val) {
        this.searchForm1.recycleTimeBegin = val[0]
        this.searchForm1.recycleTimeEnd = val[1]
      } else {
        this.searchForm1.recycleTimeBegin = null
        this.searchForm1.recycleTimeEnd = null
      }
    },
    multipleSelection(v) {
      if (!v.length) {
        this.$refs.table?.el.clearSelection()
      }
    }
  },
  created() {
    const { id, uuid, planName, pointName, hasAuthPlan } = this.$route.query
    this.hasAuthPlan = !!+hasAuthPlan
    this.searchForm1.planId = id
    this.searchForm1.uuid = uuid
    this.planName = planName
    this.pointName = pointName
    this.getTopData()
    this.getList1()
    this.getPointOption(false, 2200042)
  },
  methods: {
    // 获取头部统计数据
    getTopData() {
      getTopData({ planId: this.searchForm1.planId }).then(res => {
        this.topData = res.data || {}
      })
    },
    // 判断是否可以选中
    selectable(row, index) {
      if (this.searchForm1.tabType === 0) {
        return row.statusCode === 2200341
      }
      return !!row.operateFlag
    },
    // 客户选择
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    beforeApiCallBack1(params) {
      this.searchForm1.buildingId = this.houseDto.buildingId
      this.searchForm1.unitId = this.houseDto.unitId
      this.searchForm1.doorNo = this.houseDto.houseId
    },
    changeTab(value) {
      this.searchForm1.tabType = value
      this.multipleSelection = []
      this.getList1()
    },
    tableCallBack1() {
      this.tabs.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    // 重新推送
    registration(row) {
      this.$confirm(`是否确定向当前用户重新推送问卷：${row.customerName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200433, [row.uuid])
        })
    },
    seeDj(row) {
      const info = JSON.stringify({
        customerName: row.customerName,
        customerAllMobile: row.customerMobile,
        houseInfo: row.projectName ? row.projectName + '-' + row.stageName + '-' + row.buildingName + '-' + row.unitName + '-' + row.doorNo : '--'
      })
      this.$router.push({
        path: '/plan/depth/watch',
        query: {
          id: row.uuid,
          info: info,
          showInformation: 0
        }
      })
    },
    // 不调研
    cancel(row) {
      this.$confirm(`是否确定不调研当前用户：${row.customerName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200431, [row.uuid]).then(() => {
            const index = this.multipleSelection.findIndex(item => item.uuid === row.uuid)
            this.multipleSelection.splice(index, 1)
          })
        })
    },
    // 【批量】不调研
    batchCancel() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请先选择要恢复的用户')
        return
      }
      this.$confirm('是否确定批量取消调研当前选中用户？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200431, this.multipleSelection.map(item => item.uuid)).then(() => {
            this.multipleSelection = []
          })
        })
    },
    // 恢复调研
    retry(row) {
      this.$confirm(`是否确定恢复调研当前用户：${row.customerName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200432, [row.uuid]).then(() => {
            const index = this.multipleSelection.findIndex(item => item.uuid === row.uuid)
            this.multipleSelection.splice(index, 1)
          })
        })
    },
    // 【批量】恢复调研
    batchRetry() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请先选择要恢复的用户')
        return
      }
      this.$confirm('是否确定批量恢复调研当前选中用户？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200432, this.multipleSelection.map(item => item.uuid)).then(() => {
            this.multipleSelection = []
          })
        })
    },
    // 作废
    del(row) {
      this.$confirm(`是否确定作废当前调研用户的问卷信息：${row.customerName}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      })
        .then(() => {
          this.handleOperate(2200434, [row.uuid])
        })
    },
    handleOperate(operateCode, uuids) {
      const load = this.$load()
      return operateQues({
        operateCode,
        planUuid: this.searchForm1.uuid,
        uuids
      }).then((d) => {
        load.close()
        this.getList1()
        this.$message({
          showClose: true,
          message: '操作成功',
          type: 'success'
        })
      }).catch(e => {
        this.$errorHandle(e)
        load.close()
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.m-block {
  display: flex;
  // height: 100px;
  padding: 10px 0;
  background: #F8FAFB;
  border-radius: 12px;
  margin-bottom: 20px;
  &__item {
    display: flex;
    flex-direction: column;
    flex: 1;
    position: relative;
    align-items: center;
    justify-content: center;
    color: #31415F;
    font-size: 14px;
    &::after {
      content: '';
      position: absolute;
      width: 1px;
      height: 50px;
      border: 1px dashed #9CA8C3;
      right: 0;
      top: 50%;
      transform: translateY(-50%) scaleX(0.5);
    }
    &:last-child {
      &::after {
        display: none;
      }
    }
    &--num {
      font-size: 24px;
      color: #333;
      font-weight: 700;
    }
    &--label {
      font-size: 14px;
      color: #333;
      font-weight: 400;
    }
  }
}
.m-tag {
  height: 16px;
  line-height: 16px;
  border-radius: 8px;
  padding: 0 4px;
  font-size: 12px;
  margin: 0 5px;
  &.pending {
    color: #005DCF;
    background-color: rgba(#005DCF, 0.20);
  }
  &.success {
    color: #36C35A;
    background-color: rgba(54,195,90,0.20);
  }
  &.error {
    color: #FF6161;
    background-color: rgba(255,97,97,0.20);
  }
}
.m-house {
  display: flex;
  align-items: center;
  &__name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__sex {
    margin: 0 5px;
    flex-shrink: 0;
  }
  &__tag {
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background-color: $--color-primary;
    color: #fff;
    font-size: 12px;
    line-height: 17px;
    margin-left: 5px;
    padding: 0 8px;
    flex-shrink: 0;
  }
}
.m-top-title{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
</style>
