<template>
  <el-dialog :visible.sync="showDialog" title="详情" width="1200px">
    <um-table-full :data="tableData" scroll>
      <el-table-column
        key="customerName"
        label="姓名"
        prop="customerName"
        min-width="200"
        :formatter="$formatterTable"
      />
      <el-table-column
        key="customerMobile"
        label="电话"
        prop="customerMobile"
        min-width="200"
        :formatter="$formatterTable"
      />
      <el-table-column
        key="buildingName"
        label="楼栋"
        prop="buildingName"
        min-width="200"
        :formatter="$formatterTable"
      />
      <el-table-column
        key="unitName"
        label="单元"
        prop="unitName"
        min-width="200"
        :formatter="$formatterTable"
      />
      <el-table-column
        key="doorNo"
        label="房号"
        prop="doorNo"
        min-width="200"
        :formatter="$formatterTable"
      />
      <el-table-column
        key="areaCompanyName"
        label="所属大区"
        prop="areaCompanyName"
        :formatter="$formatterTable"
        width="120"
      />
      <el-table-column
        key="cityCompanyName"
        label="城市"
        prop="cityCompanyName"
        :formatter="$formatterTable"
        width="120"
      />
      <el-table-column
        key="projectName"
        label="项目"
        prop="projectName"
        :formatter="$formatterTable"
        width="140"
      />
      <el-table-column
        key="stageName"
        label="分期"
        prop="stageName"
        :formatter="$formatterTable"
        width="100"
      />
      <el-table-column
        key="rawPointName"
        label="所属触点"
        prop="rawPointName"
        :formatter="$formatterTable"
        width="140"
      />
    </um-table-full>
    <pagination
      :total="tableTotal"
      :page.sync="searchForm.page.pageNum"
      :limit.sync="searchForm.page.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList('page')"
    />
  </el-dialog>
</template>

<script>
import getList from '@/mixins/getList'
import { getLogDetail } from '@/api/plan/depth'

export default {
  name: 'PlanDepthSurveyInfoLog',
  mixins: [getList],
  props: {
    id: {
      type: [String, Number],
      default: null
    },
    show: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      listApi: getLogDetail,
      dateRange: null,
      searchForm: {
        id: null
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.show
      },
      set(v) {
        this.$emit('update:show', v)
      }
    }
  },
  watch: {
    show(val) {
      if (val) {
        this.searchForm.id = this.id
        this.getList()
      }
    }
  },
  methods: {
  }
}
</script>
