<template>
  <div class="m_plan_add">
    <el-form
      ref="planForm"
      :model="planForm"
      label-position="left"
      style="overflow: hidden;"
      @submit.native.prevent
    >
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">基础信息</div>
      </div>
      <div class="g-plan-form">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item ref="name" label-width="126px" label="计划名称：" prop="name" :rules="[{ required: true, message: '请输入计划名称' }]">
              <el-input
                v-model="planForm.name"
                style="width: 100%"
                maxlength="20"
                placeholder="请输入计划名称"
                clearable
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesTemplateId" label-width="126px" label="深访问卷：" prop="quesTemplateId" :rules="[{ required: true, message: '请选择调研问卷' }]">
              <el-select v-model="planForm.quesTemplateId" v-loading="quesTempLoading" filterable clearable style="width: 100%" @change="questionChange">
                <el-option
                  v-for="opt in quesTemList"
                  :key="opt.quesTemplateId"
                  :label="opt.name"
                  :value="opt.quesTemplateId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item ref="questionItems" label-width="126px" label="选择调研题目：" prop="pointId">
              <el-select-tree
                ref="optionTree"
                v-model="planForm.questionItems"
                v-loading="optionLoading"
                :options="optionList"
                filterable
                multiple
                show-checkbox
                clearable
                collapse-tags
                :show-all-levels="true"
                :props="{
                  multiple: true,
                }"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item ref="triggerNodes" label="选择调研触点及对应月份：" prop="triggerNodes" :rules="[{ required: true, type: 'array', message: '请添加' }]">
              <el-table :data="planForm.triggerNodes">
                <el-table-column label="序号" type="index" />
                <el-table-column prop="pointId" label="触点" min-width="300">
                  <template slot-scope="scope">
                    <el-form-item :prop="`triggerNodes[${scope.$index}].pointId`" style="margin-bottom: 0!important" :rules="{ required: true, trigger: 'change', message: '请选择调研触点' }">
                      <UmBusScene
                        v-show="scope.row.isEdit"
                        v-model="scope.row.pointId"
                        :all="false"
                        :need-special="false"
                        placeholder="请选择触点"
                        :rule-code="2200042"
                        :disabled-data="disabledPoint(scope.$index)"
                        @nodeChange="node => {
                          nodeChange(node, scope)
                        }"
                      />
                      <span v-show="!scope.row.isEdit">{{ scope.row.pointName }}</span>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="考核月份" min-width="300">
                  <div slot="header">
                    考核月份<el-tooltip class="item" effect="dark" placement="top-start">
                      <div slot="content">不支持跨年，需保证考核月份选在一年内。<br>例如：触点准业主1的深访的调研月份最大能选择到25年1月-25年12月。</div>
                      <span class="el-icon-question" />
                    </el-tooltip>
                  </div>
                  <template slot-scope="scope">
                    <el-form-item :prop="`triggerNodes[${scope.$index}].surveyMonths`" style="margin-bottom: 0!important" :rules="{ required: true, type: 'array', trigger: 'change', message: '请选择调研触点' }">
                      <el-date-picker
                        v-show="scope.row.isEdit"
                        v-model="scope.row.surveyMonths"
                        style="width: 100%"
                        type="months"
                        clearable
                        :picker-options="{
                          disabledDate: (date) => {
                            const curDate = new Date()
                            curDate.setMonth(curDate.getMonth() - 2)
                            return date < curDate || date.getFullYear() < new Date().getFullYear()
                          },
                        }"
                        placeholder="请选择月份"
                        value-format="yyyy-MM"
                      />
                      <span v-show="!scope.row.isEdit">{{ scope.row.surveyMonths && scope.row.surveyMonths.join('、') }}</span>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="对客问卷名称" width="300">
                  <template slot-scope="scope">
                    <el-form-item :prop="`triggerNodes[${scope.$index}].questionnaireName`" style="margin-bottom: 0!important" :rules="{ required: true, trigger: 'blur', message: '请输入对客问卷名称' }">
                      <el-input v-show="scope.row.isEdit" v-model="scope.row.questionnaireName" placeholder="请选择触点" style="width: 100%" disabled />
                      <span v-show="!scope.row.isEdit">{{ scope.row.questionnaireName }}</span>
                    </el-form-item>
                  </template>
                </el-table-column>
                <el-table-column label="操作" width="120">
                  <template slot-scope="scope">
                    <el-button v-show="scope.row.isEdit" type="text" @click="handleSave(scope)">保存</el-button>
                    <el-button v-show="!scope.row.isEdit" type="text" @click="handleEdit(scope)">编辑</el-button>
                    <el-button type="text" class="danger" @click="handleDelete(scope)">删除</el-button>
                  </template>
                </el-table-column>
              </el-table>
              <el-row type="flex" justify="center">
                <el-button type="text" icon="el-icon-circle-plus-outline" @click="addNodes">添加</el-button>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item ref="projDtos" label-width="126px" label="调研范围：" prop="projDtos" :rules="[{ required: true, type: 'array', message: '请选择指定项目' }]">
              <el-select-tree
                ref="elSelectTree"
                v-model="planForm.projDtos"
                is-leaf
                style="width: 100%"
                placeholder="请选择"
                :options="organList"
                clearable
                filterable
                :show-all-levels="false"
                :check-strictly="false"
                collapse-tags
                :default-props="{
                  multiple: true,
                  label: 'name',
                  value: 'code',
                }"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="g-plan-title-icon mt45">
        <div class="u-txt-icon" />
        <div class="u-txt-title">推送规则</div>
      </div>
      <div class="g-rule-form">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label-width="126px" label="是否推送短信：" prop="sendSmsFlag" :rules="[{ required: true, message: '请选择是否推送短信', trigger: 'change' }]">
              <el-radio-group v-model="planForm.sendSmsFlag">
                <el-radio :label="1" :disabled="!hasSmsAuth">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="planForm.sendSmsFlag" :span="12">
            <el-form-item label-width="126px" label="短信费用归属：" prop="smsFeeId" :rules="[{ required: planForm.sendSmsFlag, message: '请选择短信费用归属', trigger: 'change' }]">
              <el-select v-model="planForm.smsFeeId" placeholder="请选择" clearable>
                <el-option v-for="item in smsOrgans" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="margin-bottom: 10px;padding-left: 10px;color: #999;transform: translateY(-5px);" :span="24">备注：是否推送短信都会默认会通过小程序推送该问卷。</el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              label-width="126px"
              label="失败补偿："
              :rules="[{ required: true, }]"
            >
              <div slot="label">
                考核调研结束
                <el-tooltip class="item" effect="dark" content="若选择月份超过了配置的推送日期或审批时间超过了推送日期，则对应月份不会调研。例:要对25年1月的准业主1深访，配置考核结束1月后5号推送，若审批通过时间超过2月5号，25年1月的准业主1将不会推送深访问卷。" placement="top-start">
                  <span class="el-icon-question" />
                </el-tooltip>：
              </div>
            </el-form-item>
            <div class="errorTime">
              <el-form-item
                prop="relativeSurveyMonth"
                label=""
                :rules="[{ required: true, message: '请输入', trigger: 'change' }]"
              >
                <el-select v-model="planForm.relativeSurveyMonth" clearable placeholder="请选择" style="width: 100px;">
                  <el-option :value="1">1</el-option>
                  <el-option :value="2">2</el-option>
                </el-select>
              </el-form-item>
              <span class="margin-r-14 margin-l-10 margin-r-10">月后</span>
              <el-form-item
                prop="relativeSurveyDay"
                label=""
                :rules="[{ required: true, message: '请输入', trigger: 'change' }]"
              >
                <el-select v-model="planForm.relativeSurveyDay" clearable placeholder="请选择" style="width: 100px;">
                  <el-option v-for="item in 31" :key="item" :value="item">{{ item }}</el-option>
                </el-select>
              </el-form-item>
              <span class=" margin-r-14 margin-l-10">号推送</span>
            </div>
            <div />
          </el-col>
          <el-col style="padding-left: 10px;color: #999;" :span="24">备注：若选择月份超过了配置的推送日期或审批时间过超过了推送日期，则对应月份不会调研。</el-col>
        </el-row>
      </div>
      <div class="g-plan-title-icon mt45">
        <div class="u-txt-icon" />
        <div class="u-txt-title">呈文内容</div>
      </div>
      <div class="g-rule-form">
        <el-col :span="24">
          <el-form-item prop="oaApplyContent" label="呈文摘要：" label-position="top" :rules="[{ required: true, trigger: 'change', message: '请输入呈文摘要' }]">
            <el-input
              v-model="planForm.oaApplyContent"
              placeholder="请输入"
              clearable
              type="textarea"
              :rows="6"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件：" label-width="126px" prop="files" :rules="[{ required: true, type: 'array', trigger: 'change', message: '请上传附件' }]">
            <um-upload-file v-model="planForm.files" :limit="5" :size="200 * 1024" />
          </el-form-item>
        </el-col>
      </div>
    </el-form>
    <div class="m-fixed__btm">
      <el-button type="primary" plain @click="save(0)">保存</el-button>
      <el-button type="primary" @click="save(1)">立即发布</el-button>
    </div>
  </div>
</template>

<script>
import {
  getQuesTemplateList
} from '@/api/plan/satisfactionSurvey'
import { getQuesPainItem, addPlan, updatePlan, getDetail, overlap } from '@/api/plan/depth'
import { getTreeStage } from '@/api/common'
import { getMaxRangeCheckedData, getCheckedNodes, formatProjDtos, computedCheckedNodes } from './utils'
import { cloneDeep } from 'lodash'
import { flattenTree } from '@/utils'
export default {
  name: 'PlanDepthAdd',
  data() {
    return {
      planForm: {
        uuid: null,
        quesTemplateId: null, // 深访问卷id
        questionClientId: null, // 痛点题目id
        questionItems: [], //  痛点选项id
        projDtos: [], // 选择范围
        sendSmsFlag: null, // 是否推送短信
        smsFeeId: null, // 短信归属id
        smsFeeName: null, // 短信归属名称
        relativeSurveyMonth: null, // 相对月份
        relativeSurveyDay: null, // 相对天数
        oaApplyContent: null, // 呈文摘要
        files: [],
        triggerNodes: [] // 触点
      },
      hasSmsAuth: true,
      quesTemList: [], // 调研问卷下拉
      quesTempLoading: false, // 获取问卷loading
      optionList: [], // 痛点题目选项
      optionLoading: false, // 获取选项loading
      organList: [], // 区域城市项目分期下拉
      smsOrgans: [
        { label: '珠海大区', value: '1099006', authCode: ['DYJHGL_SFDY_XZJH_ZHDX'] },
        { label: '股份本部', value: '1099008', authCode: ['DYJHGL_SFDY_XZJH_GFDX'] },
        { label: '华南大区', value: '1099004', authCode: ['DYJHGL_SFDY_XZJH_HNDX'] },
        { label: '华东大区', value: '1099001', authCode: ['DYJHGL_SFDY_XZJH_HDDX'] },
        { label: '北方大区', value: '1099003', authCode: ['DYJHGL_SFDY_XZJH_BFDX'] },
        { label: '物业', value: '2000000', authCode: ['DYJHGL_SFDY_XZJH_WYDX'] }
      ]
    }
  },
  async created() {
    // 判断用户是否有选择推送短信权限
    if (this.$checkPermission(['DYJHGL_SFDY_XZJH_FSDX'])) {
      this.hasSmsAuth = true
      this.planForm.sendSmsFlag = 1
    } else {
      this.hasSmsAuth = false
      this.planForm.sendSmsFlag = 0
    }
    this.getQuesTemplateList()
    const { id } = this.$route.query
    const load = this.$load()
    try {
      // 编辑回显的时候需要处理下项目回显，因提交的选择项目数据经过特殊处理，故先加载这个接口
      await this.getTreeStage()
      if (id) {
        this.planForm.uuid = id
        this.getDetail()
      }
    } catch (error) {

    } finally {
      load.close()
    }
  },
  methods: {
    getDetail() {
      const load = this.$load()
      getDetail({ uuid: this.planForm.uuid }).then(res => {
        const { scopeIds = [] } = res.data
        const obj = {
          ...res.data,
          pointId: '' + res.data.pointId,
          files: res.data.files?.map(item => {
            return {
              filePath: item,
              fileName: item.slice(item.lastIndexOf('/') + 1)
            }
          }),
          triggerNodes: res.data.triggerNodes?.map(item => {
            item.isEdit = false
            return item
          }),
          questionItems: res.data.requiredQuestionItemClientIds,
          projDtos: scopeIds ? computedCheckedNodes(scopeIds, this.organList) : []
        }
        this.planForm = obj
        this.getQuesPainItem()
      }).finally(() => load.close())
    },
    // 切换问卷
    questionChange() {
      this.optionList = []
      this.planForm.questionItems = []
      if (this.planForm.quesTemplateId) {
        this.getQuesPainItem()
      }
    },
    // 获取项目数据
    getTreeStage() {
      return getTreeStage().then(res => {
        this.organList = res.data || []
        const flatTree = flattenTree(this.organList)
        // 过滤掉短信权限，把有权限的code删除掉，留下的是没有权限的【卧推100公斤的健身大神产品提的】
        // 如果只有某个大区的权限，则别的大区就不能选
        // 如果只有某一个项目权限，则表示有这个大区权限
        const smsCodes = ['1099006', '1099004', '1099001', '1099003']
        flatTree.forEach(item => {
          try {
            smsCodes.forEach((_item, _index) => {
              if (item.codes.includes(_item) && !item.disabled) {
                smsCodes.splice(_index, 1)
                throw new Error('stop')
              }
            })
          } catch (error) {

          }
        })
        // 短信权限过滤，根据按钮权限和项目数据权限过滤
        this.smsOrgans = this.smsOrgans.filter(item => {
          return this.$checkPermission(item.authCode) && !smsCodes.includes(item.value)
        })
        console.log(smsCodes)

        // this.organList = this.filterDisabledNodes(res.data || [])
      })
    },
    // 递归过滤掉父子节点都disabled的数据
    filterDisabledNodes(nodes) {
      return nodes.filter(node => {
        if (node.children && node.children.length) {
          node.children = this.filterDisabledNodes(node.children)
          // 如果当前节点disabled且所有子节点都被过滤掉了,则过滤掉当前节点
          if (node.disabled && node.children.length === 0) {
            return false
          }
        } else if (node.disabled) {
          // 叶子节点且disabled则过滤掉
          return false
        }
        return true
      })
    },
    // 获取深访问卷
    getQuesTemplateList() {
      this.quesTempLoading = true
      getQuesTemplateList().then(res => {
        this.quesTemList = res.data || []
        if (this.quesTemList.length === 1) { // 只有一份问卷，默认选中
          this.planForm.quesTemplateId = this.quesTemList[0].quesTemplateId
          this.getQuesPainItem()
        }
      }).finally(() => { this.quesTempLoading = false })
    },
    // 获取问卷痛点题目选项
    getQuesPainItem() {
      this.optionLoading = true
      getQuesPainItem({ quesTemplateId: this.planForm.quesTemplateId }).then(res => {
        const arr = res.data || []
        this.optionList = arr.map(item => {
          !item.clientItemId && (item.clientItemId = 'group-' + Math.random() * 10000000)
          return {
            label: item.name || item.typeName,
            value: item.clientItemId,
            children: item.itemList.map(_item => {
              return {
                label: _item.name,
                value: _item.clientItemId
              }
            })
          }
        })
      }).finally(() => { this.optionLoading = false })
    },
    nodeChange(node, scope) {
      const nodeData = node[0]?.data
      if (!nodeData) {
        this.planForm.triggerNodes[scope.$index].pointName = ''
        this.planForm.triggerNodes[scope.$index].questionnaireName = ''
      } else {
        this.planForm.triggerNodes[scope.$index].pointName = nodeData.ownerStageName + '/' + nodeData.label
        this.planForm.triggerNodes[scope.$index].questionnaireName = nodeData.depthQuestionnaireName
      }
    },
    // 禁用除当前项之外的触点¨
    disabledPoint(index) {
      return this.planForm.triggerNodes.slice(0, index).concat(this.planForm.triggerNodes.slice(index + 1)).map(item => item.pointId)
    },
    // 添加调研处带你及对应月份
    addNodes() {
      this.planForm.triggerNodes.push({
        isEdit: true,
        pointId: null,
        pointName: null,
        questionnaireName: null,
        surveyMonths: []
      })
    },
    handleEdit(scope) {
      scope.row.isEdit = true
    },
    async handleSave(scope) {
      try {
        await Promise.all([`triggerNodes[${scope.$index}].questionnaireName`, `triggerNodes[${scope.$index}].pointId`, `triggerNodes[${scope.$index}].surveyMonths`].map(field => {
          return new Promise((resolve, reject) => {
            this.$refs.planForm.validateField(field, err => {
              if (err) reject(err)
              resolve()
            })
          })
        }))
        // 判断选择的月份不能跨年
        const years = new Set(scope.row.surveyMonths.map(date => date.split('-')[0]))
        if (years.size > 1) {
          this.$message.warning('考核月份不能跨年')
          return
        }
        scope.row.isEdit = false
      } catch (error) {

      }
    },
    handleDelete(scope) {
      this.planForm.triggerNodes.splice(scope.$index, 1)
    },
    /**
     * 计划保存、发布
     * @param {int} oaFlag 0 保存  1发布
     */
    save(oaFlag) {
      this.$refs.planForm.validate(async valid => {
        if (!valid) return
        if (this.planForm.triggerNodes.some(item => item.isEdit)) {
          this.$message.warning('请保存所有调研触点及对应月份')
          return
        }
        const arr = cloneDeep(this.$refs.elSelectTree?.$children[0].getNodes()) || []
        getMaxRangeCheckedData(arr) // 递归处理数据
        const params = {
          ...this.planForm,
          oaFlag, // 是否发布
          questionItems: this.formatQuestionItems(),
          questionClientId: this.quesTemList.filter(item => item.quesTemplateId === this.planForm.quesTemplateId)[0]?.painQuestionId,
          smsFeeName: this.smsOrgans.find(item => item.value === this.planForm.smsFeeId)?.label,
          projDtos: formatProjDtos(getCheckedNodes(arr)),
          files: this.planForm.files.map(item => item.filePath)
        }

        const load = this.$load()
        try {
          if (oaFlag === 1) { // 立即发布，需要校验
            const errInfo = await overlap(params)
            if (errInfo.msg) {
              this.$confirm(errInfo.msg, '以下触点-月份-项目本年已存在调研，本次计划将不调研', {
                showClose: false
              }).then(() => {
                this.saveUpdate(params, load)
              }).catch(() => {
                load.close()
              })
              return
            }
          }
          this.saveUpdate(params, load)
        } catch (error) {
          load.close()
        }
      })
    },
    saveUpdate(params, load) {
      const API = this.planForm.uuid ? updatePlan : addPlan
      API(params).then(res => {
        this.$alert(res.msg || '操作成功', '提示', {
          type: 'success',
          showClose: false
        }).then(() => {
          this.$router.back()
        })
      }).finally(() => load.close())
    },
    formatQuestionItems() {
      const arr = []
      this.optionList.forEach(item => {
        if (Array.isArray(item.children) && item.children.length) {
          item.children.forEach(_item => {
            if (this.planForm.questionItems.includes(_item.value)) {
              arr.push({
                itemName: _item.label,
                itemClientId: _item.value
              })
            }
          })
        } else if (this.planForm.questionItems.includes(item.value)) {
          arr.push({
            itemName: item.label,
            itemClientId: item.value
          })
        }
      })
      return arr
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../../../styles/modules/plan';
.u-tip {
  line-height: 24px;
  font-size: 12px;
  font-weight: 400;
  &.mt-10 {
    margin-top: 10px;
  }
}
.icon {
  font-size: 12px;
  color: #F8716B;
  margin-right: 4px;
}
.btn-tips {
  font-size: 12px;
}
.errorTime{
  display: flex;
  height: 32px;
  line-height: 32px;
  ::v-deep .el-form-item__content{
    margin-left: 0px !important;
  }
}
</style>
