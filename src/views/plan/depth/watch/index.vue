<template>
  <div class="g-container">
    <el-row type="flex" justify="space-between">
      <el-form style="flex: 1">
        <el-form-item label="客户信息：">
          {{ customerInfo.customerName || '--' }}（{{ customerInfo.customerAllMobile || '--' }}）
        </el-form-item>
        <el-form-item label="房产信息：">
          <div v-if="customerInfo.houseInfo" class="m-tag">{{ customerInfo.houseInfo }}</div>
          <span v-else>--</span>
        </el-form-item>
      </el-form>
    </el-row>
    <iframe class="m-preview" :src="url" />
  </div>
</template>

<script>
export default {
  name: 'PlanDepthPreview',
  data() {
    return {
      customerInfo: {},
      url: null
    }
  },
  created() {
    const { id, info, showInformation } = this.$route.query
    const url = process.env.NODE_ENV !== 'localdev' ? location.origin + '/client/depth' : 'http://localhost:9528/depth'
    this.url = url + '?uuid=' + id + '&showInformation=' + showInformation // 1 显示信息，0 不显示
    try {
      this.customerInfo = JSON.parse(info)
    } catch (error) {

    }
  },
  methods: {
  }
}
</script>

  <style lang="scss" scoped>
  .g-container {
    background-color: #fff;
    height: 100%;
    width: 660px;
    margin:0 auto;
    border-radius: 12px;
    box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
    padding: 20px 50px;
    display: flex;
    flex-direction: column;
    ::v-deep {
      .el-form-item__label {
        color: #999!important;
      }
    }
    .m-tag {
      background: #F5F7FA;
      line-height: 28px;
      color: #333;
      font-size: 12px;
      padding: 0 12px;
      display: inline-block;
    }
    .m-preview {
      width: 560px;
      flex: 1;
      border: 1px solid rgba(216,220,230,1);
      border-radius: 8px;
      padding: 20px;
    }
  }
  </style>

