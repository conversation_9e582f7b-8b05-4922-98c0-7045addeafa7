<template>
  <div>
    <el-card shadow="never" class="margin-b-20">
      <div class="m-top-title">
        <div>计划名称：{{ planName||"--" }}</div>
        <div class="margin-l-20">触点：{{ pointName ||"--" }}</div>
      </div>
      <el-form v-model="searchForm" label-width="80px" class="margin-t-20">
        <el-row type="flex" :gutter="20" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="区域/城市：">
              <UmBusOrgan v-model="searchForm.projDto" style="width: 100%;" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目/分期：">
              <UmBusProject v-model="searchForm.projDto" style="width: 100%;" :area-company-id="searchForm.projDto.areaCompanyId" :city-company-id="searchForm.projDto.cityCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item prop="rawPointIdList" label="所属触点：">
              <el-cascader
                v-model="searchForm.rawPointIdList"
                :options="pointOptions"
                style="width: 100%;"
                collapse-tags
                filterable
                :props="{
                  multiple: true,
                  emitPath: false
                }"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="深访月份：">
              <el-date-picker
                v-model="searchForm.openMonthList"
                type="months"
                format="yyyy-MM"
                value-format="yyyy-MM"
                placeholder="请选择"
                clearable
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="考核月份：">
              <el-date-picker
                v-model="searchForm.monthList"
                type="months"
                format="yyyy-MM"
                value-format="yyyy-MM"
                placeholder="请选择"
                :clearable="false"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-row type="flex" justify="end">
              <div><el-button type="primary" icon="el-icon-search" @click="getQuestionList">搜索</el-button></div>
              <div class="f-mg-l10">
                <el-button v-if="$checkPermission(['DYJHGL_SFDY_TJFX_DC'])" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportChangeChart">导出</el-button>
              </div>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div class="g-container">
      <!-- 整体问卷分析 -->
      <div>
        <el-row type="flex" align="middle" justify="space-between" class="margin-b-20">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">数据概况</div>
          </div>
          <span>数据更新时间：{{ allOverviewInfo.updateTime | formatterTable }}</span>
        </el-row>

        <Statistics :list="overviewInfo" :data-info="allOverviewInfo" class="mb-36" />
        <div class="g-plan-title-icon margin-b-20">
          <div class="u-txt-icon" />
          <div class="u-txt-title">统计报表</div>
        </div>
        <components
          :is="QUESTION_TYPE[item.questionTypeCode]"
          v-for="item in questionList"
          :id="item.questionId"
          ref="dynamicComponent"
          :key="item.questionId"
          :question-type-code="item.questionTypeCode"
          :index="item.xh"
          is-depth
          :title="item.questionName"
        />
        <el-empty v-if="!questionList.length" :image="require('@/assets/plan/zanwu.png')" />
      </div>
    </div>
  </div>
</template>

<script>
import Statistics from '../../components/analyse/components/Statistics'
import Quota from '../../components/analyse/components/Quota'
import TableQuestion from '../../components/analyse/components/TableQuestion'
import TableFileList from '../../components/analyse/components/TableFileList'
import ChartQuestion from '../../components/analyse/components/ChartQuestion'
import CommonTitle from '@/components/CommonTitle'
import { getDataGeneral, getQuestionList } from '@/api/plan/common'
import UmBusOrgan from '@/components/UmBusOrgan'
import UmBusProject from '@/components/UmBusProject'
import exportData from '@/mixins/exportData'
import { exportQuestionnaireAnalysis } from '@/api/export'
import getPointList from '@/mixins/getPointList'
const QUESTION_TYPE = {
  2200081: 'ChartQuestion', // '单选题',
  2200082: 'ChartQuestion', // '多选题',
  2200083: 'ChartQuestion', // '打分题',
  2200084: 'TableQuestion', // '填空题',
  // 2200085: 'TableQuestion' // '文本描述',
  2200086: 'TableFileList' // '附件题',
}
export default {
  name: 'PlanSatisfactionSurveyAnalyse',
  components: { Statistics, Quota, CommonTitle, TableQuestion, TableFileList, ChartQuestion, UmBusOrgan, UmBusProject },
  mixins: [exportData, getPointList],
  props: {},
  data() {
    const DATE = new Date()
    const currentMonth = DATE.getFullYear() + '-' + (DATE.getMonth() + 1).toString().padStart(2, '0')
    return {
      exportApi: exportQuestionnaireAnalysis,
      overviewInfo: [
        {
          label: '样框量',
          key: 'frameNum'
        },
        {
          label: '样本量',
          key: 'recycleNum'
        },
        {
          label: '回收率',
          key: 'recycleRate',
          unit: '%'
        },
        {
          label: '平均答题时长',
          key: 'avgAnswerTime'
        }
      ],
      allOverviewInfo: {},
      QUESTION_TYPE,
      questionList: [],
      searchForm: {
        projDto: {},
        openMonthList: [],
        monthList: [currentMonth],
        rawPointIdList: [],
        planUuid: null,
        type: 2
      },
      planName: '', // 计划名称
      pointName: '', // 触点
      imageList: [] // 导出echart图片
    }
  },
  created() {
    const { planName, pointName, uuid } = this.$route.query
    this.planName = planName
    this.pointName = pointName
    this.searchForm.planUuid = uuid
    this.getDataGeneral()
    this.getPointOption(false, 2200042)
    this.getQuestionData()
  },
  methods: {
    async getQuestionData() {
      const load = this.$load()
      try {
        await this.getQuestionList()
      } catch (error) {

      } finally {
        load.close()
      }
    },
    // 获取所有问题列表
    getQuestionList() {
      if (!this.searchForm.monthList.length) {
        this.$message.warning('请选择考核月份')
        return
      }
      // 校验月份是否在同一年度
      const years = this.searchForm.monthList.map(date => date.split('-')[0])
      const uniqueYears = [...new Set(years)]
      if (uniqueYears.length > 1) {
        this.$message.warning('考核月份必须在同一年度内')
        return
      }
      this.searchForm.month = this.searchForm.monthList[0]
      this.questionList = []
      return getQuestionList(this.searchForm).then(res => {
        const data = res.data || []
        let xh = 0
        this.questionList = data.map(item => {
          if (item.questionTypeCode !== 2200085) {
            xh++
          }
          return {
            ...item,
            xh
          }
        })
      })
    },
    // 获取数据概况
    getDataGeneral() {
      return getDataGeneral({ ...this.searchForm, planId: this.$route.query.id }).then(res => {
        this.allOverviewInfo = res.data || {}
      })
    },
    formatExportParams() {
      const params = { ...this.searchForm, imageList: this.imageList }
      return params
    },
    async exportChangeChart() {
      if (Array.isArray(this.$refs.dynamicComponent) && this.$refs.dynamicComponent.filter(item => item.exportChart).length) {
        try {
          this.imageList = await Promise.all(this.$refs.dynamicComponent.filter(item => item.exportChart).map(item => item.exportChart()))
          this.exportData()
        } catch (e) {
          console.log(e)
        }
      } else {
        this.$message.warning('暂无数据')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  // height: calc(100vh - 76px);
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  border-radius: 12px;
  padding: 20px;
  overflow-y: auto;
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-36 {
    margin-bottom: 36px;
  }
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  .u-more {
    display: flex;
    color: #999999;
    flex-shrink: 0;
    cursor: pointer;
    &:hover {
      color: $--color-primary;
    }
    i {
      margin-left: 4px;
    }
  }
}
.m-top-title{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
</style>

