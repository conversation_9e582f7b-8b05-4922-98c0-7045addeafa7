
<template>
  <div>
    <el-dialog
      title="短信模板"
      :visible.sync="templateVisible"
      width="727px"
      center
      :destroy-on-close="true"
      :before-close="handleClose"
    >
      <div class="f-tac f-mg-b20 btn-tem">
        <div :class="isFlag===1?'isActive':'noActive'" @click="isFlag=1">模板商城</div>
        <div :class="isFlag===2?'isActive':'noActive'" @click="isFlag=2">企业模板</div>
      </div>
      <el-radio-group
        v-model="searchForm.categoryUuid"
        class="f-mg-b20"
        @change="getMsgList()"
      >
        <el-radio-button
          v-for="(item, i) in categoryList"
          :key="i"
          :label="item.categoryUuid"
        >
          {{ item.name }}
        </el-radio-button>
      </el-radio-group>
      <el-table :data="tableData" :show-header="false">
        <el-table-column width="80px">
          <template #default="scope">
            <!-- label值要与el-table数据id实现绑定 -->
            <el-radio v-model="unitInfo.msgMould" :label="scope.row.msgMould" @change="handleRowChange(scope.row)">{{ "" }}</el-radio>
          </template>
        </el-table-column>
        <el-table-column prop="content">
          <template slot-scope="scope">
            <div class="mgsMoule-radio">
              <UmToolTip
                class="u-name"
                :content="scope.row.content"
                :color="'#6C7B96'"
                is-underline="none"
                width="95%"
                style="display: inline"
              />
            </div>
          </template>
        </el-table-column>
      </el-table>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        class="f-mg-t20"
        @pagination="() => getMsgList('page')"
      />
      <div class="f-tac f-mg-t20">
        <el-button type="primary" @click="checkMsgMould"> 确定选择 </el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>

export default {
  name: 'TemplateConfig',
  props: {
    visible: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      isFlag: 1,
      categoryList: [{ name: '全部', categoryUuid: null }, { name: '简约', categoryUuid: 1 }, { name: '卡通', categoryUuid: 2 }],
      tableData: [
        {
          msgMould: 1,
          content: '上海市普陀区金沙江路 123 弄'
        },
        {
          msgMould: 2,
          content: '上海市普陀区金沙江路 456 弄'
        },
        {
          msgMould: 3,
          content: '上海市普陀区金沙江路 789 弄'
        },
        {
          msgMould: 4,
          content: '上海市普陀区金沙江路 000 弄'
        }
      ],
      tableTotal: 0,
      searchForm: {
        templateType: 1,
        categoryUuid: null,
        page: {
          pageNum: 1,
          pageSize: 10
        }
      },
      msgMould: '',
      unitInfo: {
        msgMould: '',
        content: ''
      }
    }
  },
  computed: {
    templateVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    handleRowChange(data) {
      if (data) {
        this.unitInfo.msgMould = data.msgMould
        this.unitInfo.content = data.content
      }
    },
    getMsgList() {
      // if (state != 'page') {
      //   this.page.pageNum = 1
      // }
      //
      // const load = this.$loading()
      // const parmas = {
      //   projectUuid: this.uuid,
      //   categoryUuid: this.categoryUuid,
      //   status: 1,
      //   name: '',
      //   page: this.page
      // }
      // getTemplateList(parmas)
      //     .then((res) => {
      //       this.tableData = res.data.list
      //       this.tableTotal = res.data.page.total
      //       this.msgMould = res.data.list[0].content
      //     })
      //     .finally((_) => {
      //       load.close()
      //     })
    },
    handleClose() {
      this.templateVisible = false
    },
    checkMsgMould() {
      this.templateVisible = false
      if (!this.unitInfo.msgMould) return
      this.$emit('sureChoose', this.unitInfo)
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep .btn-tem{
  width: 216px;
  height: 32px;
  background: url("~@/assets/plan/btn-template.png") no-repeat;
  background-size: 100% 100%;
  display: flex;
  justify-content: center;
  align-items: center;
  font-size: 14px;
  margin: 0 auto;
  cursor: pointer;
  .isActive{
    width: 104px;
    height: 24px;
    line-height: 24px;
    background: url("~@/assets/plan/btn-active.png") no-repeat;
    background-size: 100% 100%;
    color: #FFFFFF;
  }
  .noActive{
    width: 104px;
    height: 24px;
    line-height: 24px;
    background: url("~@/assets/plan/btn-noActive.png") no-repeat;
    background-size: 100% 100%;
    color: #6C7B96;
  }
}

</style>
