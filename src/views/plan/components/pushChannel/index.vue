
<template>
  <div>
    <div class="g_content_box">
      <div v-for="(item,index) in val" :key="index" class="m-item">
        <div class="m-item-top">
          <el-checkbox v-model="item.isChoose" :disabled="disabled">{{ item.title }}</el-checkbox>
          <el-button v-if="!disabled" type="primary" icon="el-icon-edit" class="m-l-12" size="mini" plain @click="editDialog(item)">
            编辑通知模版
          </el-button>
        </div>
        <div v-if="item.channelTypeCode === CHANNEL_TYPE.MSG" class="m-item-content row-four">
          {{ item.firstContent }}
        </div>
        <div v-if="item.channelTypeCode === CHANNEL_TYPE.PUBLIC" class="m-item-content">
          <div class="flex">
            <div class="label ">项目房产：</div>
            <div class="value" :title="item.firstContent"> {{ item.firstContent }}</div>
          </div>
          <div class="flex">
            <div class="label">服务类型：</div>
            <div class="value row-three" :title="item.secondContent"> {{ item.secondContent }}</div>
          </div>
        </div>
        <div v-if="item.channelTypeCode === CHANNEL_TYPE.MINI" class="m-item-content">
          <div class="flex">
            <div class="label ">问卷主题：</div>
            <div class="value" :title="item.firstContent"> {{ item.firstContent }}</div>
          </div>
          <div class="flex">
            <div class="label">备注：</div>
            <div class="value row-three" :title="item.secondContent"> {{ item.secondContent }}</div>
          </div>
        </div>
      </div>
    </div>
    <seedConfig :visible.sync="visible" :item-data="itemData" />
  </div>
</template>

<script>
import { CHANNEL_TYPE } from '@/enum'
import seedConfig from './seedConfig'
import emitter from 'element-ui/lib/mixins/emitter'

export default {
  name: 'PushChannel',
  components: { seedConfig },
  mixins: [emitter],
  model: {
    prop: 'val',
    event: 'change'
  },
  props: {
    val: {
      type: Array,
      default: () => []
    },
    validateEvent: {
      type: Boolean,
      default: true
    },
    disabled: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      CHANNEL_TYPE,
      visible: false,
      itemData: {}
    }
  },
  watch: {
    val: {
      handler(val) {
        if (this.validateEvent) {
          this.dispatch('ElFormItem', 'el.form.change', val)
        }
      },
      deep: true
    }
  },
  methods: {
    editDialog(item) {
      this.itemData = item
      this.visible = true
    },
    busValidate() {
      if (this.validateEvent) {
        this.dispatch('ElFormItem', 'el.form.change', this.val)
      }
    }
  }
}
</script>

<style scoped lang="scss">
// 显示两行
.row-one{
  overflow: hidden;
  text-overflow: ellipsis;
  width: 100%;
}
// 显示4行
.row-four{
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 4;
  width: 100%;
  word-break: break-all;
}
.row-three{
  display: -webkit-box;
  overflow: hidden;
  -webkit-box-orient: vertical;
  text-overflow: ellipsis;
  -webkit-line-clamp: 3;
  width: 100%;
  word-break: break-all;
}
.g_content_box{
  border-radius: 8px;
  border: 1px solid #D8DCE6;
  padding: 20px;
  display: flex;
  overflow-x: auto;
  width: 100%;
  .m-item{
    position: relative;
    min-width: 33.3%;
    padding: 0 20px;
    display: flex;
    flex-direction: column;
    &:first-child:last-child {
      width: 100%;
    }
    .m-item-top{
      display: flex;
      justify-content: space-between;
      .img{
        width:13px ;
        height: 13px;
        vertical-align: bottom;
      }
    }
    .m-item-content{
      margin-top: 12px;
      background: #F7F8F9;
      border-radius: 6px;
      padding: 8px 12px 0;
      line-height: 24px;
      font-size: 12px;
      color: #31415F;
      // height: 116px;
      min-height: 80px;
      flex: 1;
      .label{
        width: 60px;
        color: #9CA8C3;
      }
      .value {
        flex: 1;
        overflow: hidden;
      }
      .val{
        word-break: break-all;
      }
    }
      &:after{
        content: '';
        position: absolute;
        top: 0;
        right: -20px;
        height: 100%;
        margin: 0 20px;
        border-right: 1px solid #D8DCE6;
    }
    &:last-child{
      padding-right: 0;
      &:after{
        display: none;
      }
    }
    &:first-child{
      padding-left: 0;
    }
  }
}
::v-deep {
  .el-form-item__label {
    color: #999!important;
  }
  .el-checkbox__label {
    color: #333!important;
  }
}
</style>
