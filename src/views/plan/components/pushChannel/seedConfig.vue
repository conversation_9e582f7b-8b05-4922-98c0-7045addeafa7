
<template>
  <div>
    <el-dialog
      :title="title"
      :visible.sync="editVisible"
      width="840px"
      center
      :destroy-on-close="true"
      :before-close="handleClose"
      append-to-body
    >
      <div class="flex">
        <div class="m-dialog-left">
          <div :class="itemData.channelTypeCode === CHANNEL_TYPE.MSG ? 'm-phone-top':'m-phone-topOther'">
            <div class="topTime">{{ topTime }}</div>
            <!-- <div class="topType" /> -->
            <div v-if="itemData.channelTypeCode === CHANNEL_TYPE.MSG" class="avatar">
              <el-avatar :src="circleUrl" :size="46" />
              <div>李大明</div>
            </div>
            <div :class="itemData.channelTypeCode === CHANNEL_TYPE.MSG ? 'icon' : 'icon-user'" />
          </div>
          <div class="m-phone-content">
            <div class="time">
              <div v-if="itemData.channelTypeCode === CHANNEL_TYPE.MSG">短信</div>
              <div>{{ time }}</div>
            </div>
            <div v-if="itemData.channelTypeCode === CHANNEL_TYPE.MSG" class="seedMessage">
              {{ msgRealContent.name }}<span class="u-total">字符数：{{ msgRealContent.length }}</span>
            </div>
            <div v-else class="seedMessage_other">
              <!--              :style="{minHeight:itemData.channelTypeCode===2?'':''}"-->
              <div v-if="itemData.channelTypeCode===CHANNEL_TYPE.MINI" class="item-top">
                <div class="flexCenter">
                  <el-avatar :src="circleUrl" :size="32" />
                  <div class="txt">问卷调查</div>
                </div>
                <div class="item-menu" />
              </div>
              <div class="item-content" :style="{'paddingTop':itemData.channelTypeCode===CHANNEL_TYPE.PUBLIC?'0':''}">
                <div class="flexBetween" style="align-items: flex-end">
                  <div class="title">
                    {{ itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '活动通知' : itemData.channelTypeCode === CHANNEL_TYPE.MINI ? '问卷主题' : '' }}
                  </div>
                  <div class="date">{{ dayTime }}</div>
                </div>
                <div class="info">
                  <div v-if="itemData.channelTypeCode!==CHANNEL_TYPE.MSG" class="info-item">
                    <div class="label">{{ itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '项目房产' : '问卷主题' }}：</div>
                    <div class="value">{{ dialogForm.firstContent||'--' }}</div>
                  </div>
                  <div class="info-item">
                    <div class="label">
                      {{ itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '服务类型' : '备注' }}：
                    </div>
                    <div class="value">{{ dialogForm.secondContent||'--' }}</div>
                  </div>
                </div>
              </div>
              <div class="item-footer" :class="itemData.channelTypeCode===CHANNEL_TYPE.PUBLIC?'no-border':''">
                <div>{{ itemData.channelTypeCode===CHANNEL_TYPE.MINI?'进入小程序查看':itemData.channelTypeCode===CHANNEL_TYPE.PUBLIC?'查看详情':'' }}</div>
                <div style="color:#9CA8C3 ">></div>
              </div>
              <div v-if="itemData.channelTypeCode===CHANNEL_TYPE.MINI" class="item-footer no-border">
                <div>拒收消息</div>
                <div style="color:#9CA8C3 ">></div>
              </div>
            </div>
          </div>
          <div v-if="itemData.channelTypeCode===CHANNEL_TYPE.MSG" class="m-phone-footer flex">
            <div class="btn-camera" />
            <div class="btn-expression" />
            <div class="btn-input f-flex-acjcsb">
              <div class="input-val">短信</div>
              <div class="input-icon" />
            </div>
          </div>
          <div v-else class="m-phone-footer flex">
            <div class="btn-keyboard" />
            <div
              v-for="(item, i) in modularList"
              :key="i"
              class="btn-name f-flex-ajc"
            >
              <div class="name-icon" />
              {{ item.name }}
            </div>
          </div>
        </div>
        <div class="m-dialog-right">
          <el-form ref="dialogForm" :model="dialogForm " label-position="left" label-suffix="：" label-width="70px" :rules="rules">
            <el-form-item
              v-if="itemData.channelTypeCode===CHANNEL_TYPE.MSG"
              ref="firstContent"
              prop="firstContent"
              label="短信内容"
            >
              <el-input
                ref="textarea"
                v-model="dialogForm.firstContent"
                placeholder="请输入"
                :autosize="{ minRows: 4, maxRows: 10 }"
                type="textarea"
                maxlength="100"
                clearable
                show-word-limit
                class="f-mg-b12"
              />
            </el-form-item>
            <div v-if="itemData.channelTypeCode===CHANNEL_TYPE.MSG" class="flex">
              <div class="btn-text" style="padding-left: 75px;margin-right: 10px" @click="pushInput('#姓名#')">#姓名#</div>
              <div class="btn-text" @click="pushInput('#链接#')">#链接#</div>
            </div>
            <el-form-item
              v-if="itemData.channelTypeCode !== CHANNEL_TYPE.MSG"
              ref="firstContent"
              prop="firstContent"
              :label="itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '项目房产' : '问卷主题'"
            >
              <el-input
                v-model.trim="dialogForm.firstContent"
                :placeholder="itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '请输入项目房产' : '请输入问卷主题'"
                maxlength="20"
                clearable
              />
            </el-form-item>
            <el-form-item
              v-if="itemData.channelTypeCode !== CHANNEL_TYPE.MSG"
              ref="secondContent"
              prop="secondContent"
              :label="itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '服务类型' : '备注'"
            >
              <el-input
                v-model.trim="dialogForm.secondContent"
                :placeholder="itemData.channelTypeCode === CHANNEL_TYPE.PUBLIC ? '请输入服务类型' : '请输入备注'"
                :maxlength="20"
                clearable
              />
            </el-form-item>
          </el-form>
          <div class="m-footer">
            <el-button type="primary" plain @click="editVisible = false">
              取消
            </el-button>
            <el-button type="primary" @click="editSubmit()"> 确定 </el-button>
          </div>
        </div>
      </div>
    </el-dialog>
    <templateConfig :visible.sync="templateVisible" @sureChoose="sureChoose" />
  </div>
</template>

<script>
import templateConfig from './templateConfig'
import { CHANNEL_TYPE } from '@/enum'
export default {
  name: 'SeedConfig',
  components: { templateConfig },
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    itemData: {
      type: Object,
      default: () => {}
    }
  },
  data() {
    return {
      CHANNEL_TYPE,
      modularList: [{ name: '模块一' }, { name: '模块二' }, { name: '模块三' }],
      circleUrl: 'https://cube.elemecdn.com/3/7c/3ea6beec64369c2642b92c6726f1epng.png',
      time: '',
      timeID: null,
      topTime: '',
      dayTime: '',
      dialogForm: {
        firstContent: '',
        secondContent: '',
        thirdContent: ''
      },
      detailsList: [
        { label: '会员中心', value: '1' },
        { label: '领券中心', value: '2' },
        { label: '商城首页', value: '3' },
        { label: '我的权益', value: '4' }
      ],
      rules: {
        firstContent: [{ required: true, message: '请输入', trigger: 'change' }],
        secondContent: [{ required: true, message: '请输入', trigger: 'blur' }
        ],
        thirdContent: [{ required: true, message: '请输入', trigger: 'blur' }]
      },
      templateVisible: false

    }
  },
  computed: {
    editVisible: {
      get() {
        if (this.visible) {
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.dialogForm.firstContent = this.itemData.firstContent
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.dialogForm.secondContent = this.itemData.secondContent
          // eslint-disable-next-line vue/no-side-effects-in-computed-properties
          this.dialogForm.thirdContent = this.itemData.thirdContent
          this.currentTime()
        }
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    },
    title() {
      let str = ''
      switch (this.itemData.channelTypeCode) {
        case CHANNEL_TYPE.MSG: str = '短信通知设置'; break
        case CHANNEL_TYPE.PUBLIC: str = '公众号消息通知设置'; break
        case CHANNEL_TYPE.MINI: str = '小程序订阅消息通知设置'; break
      }
      return str
    },
    msgRealContent() {
      const dynamicName = '李大明'
      const dynamicUrl = 'https://wxaurl.cn/JG5LtkWBKkt'
      const str = this.dialogForm.firstContent.replace(/#姓名#/g, dynamicName).replace(/#链接#/g, dynamicUrl)
      return {
        name: str,
        length: str.length
      }
    }
  },
  methods: {
    getTime() {
      const yy = new Date().getFullYear()
      const mm = new Date().getMonth() + 1 < 10 ? (new Date().getMonth() + 1) : new Date().getMonth() + 1
      const dd = new Date().getDate() < 10 ? '0' + new Date().getDate() : new Date().getDate()
      const hh = new Date().getHours()
      const mf =
          new Date().getMinutes() < 10 ? '0' + new Date().getMinutes() : new Date().getMinutes()
      const ss =
          new Date().getSeconds() < 10 ? '0' + new Date().getSeconds() : new Date().getSeconds()
      this.time = yy + '年' + mm + '月' + dd + '日' + ' ' + hh + ':' + mf + ':' + ss
      this.topTime = hh + ':' + mf
      this.dayTime = mm + '月' + dd + '日'
    },
    currentTime() {
      this.getTime()
      this.timeID = setInterval(this.getTime, 1000)
    },
    getMsgTemplateShow() {
      this.templateVisible = true
    },
    pushInput(firstContent) {
      if (this.dialogForm.firstContent?.length + firstContent.length > 100) {
        this.$message.warning('短信内容长度超出，请删除部分文案后重新操作！')
        return
      }
      const textarea = this.$refs.textarea.$el.getElementsByTagName('textarea')[0]
      const cursorPosStart = textarea.selectionStart
      const cursorPosEnd = textarea.selectionEnd
      const currentValue = textarea.value
      const newValueInserted = currentValue.substring(0, cursorPosStart) + firstContent + currentValue.substring(cursorPosEnd)

      // 更新 textarea 的值并设置新的光标位置
      textarea.value = newValueInserted
      textarea.setSelectionRange(cursorPosStart + firstContent.length, cursorPosStart + firstContent.length)
      this.dialogForm.firstContent = newValueInserted
      textarea.focus()
    },
    editSubmit() {
      this.$refs.dialogForm.validate(valid => {
        if (valid) {
          this.editVisible = false
          Object.assign(this.itemData, this.dialogForm)
          // this.$emit('submitMsg', Object.assign(this.itemData, this.dialogForm))
        }
      })
    },
    handleClose() {
      this.editVisible = false
      // clearInterval(this.timeID)
    },
    sureChoose(e) {
      this.dialogForm.secondContent = e.content
    }
  }
}
</script>

<style scoped lang="scss">
.m-dialog-left{
  width: 375px;
  height: 600px;
  border-radius: 12px;
  flex-shrink: 0;
  overflow: hidden;
  border: 1px solid #ECEDEE;
  display: flex;
  flex-direction: column;
  .m-phone-top{
    width: 100%;
    height: 117px;
    background: url("~@/assets/plan/phone-top.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    .topTime{
      color: #020202;
      font-size: 14px;
      position: absolute;
      left: 35px;
      top: 18px;
    }
    .avatar{
      position: absolute;
      left: 50%;
      bottom: 5px;
      transform: translate(-50%,0);
      text-align: center;
    }
    .icon{
      position: absolute;
      right: 10px;
      top: 50%;
      width: 24px;
      height: 24px;
      transform: translate(0,-50%);
      background: url("~@/assets/plan/icon-ic.png") no-repeat;
      background-size: 100% 100%;
    }
  }
  .m-phone-topOther{
    width: 100%;
    height: 78px;
    background: url("~@/assets/plan/phone-topOther.png") no-repeat;
    background-size: 100% 100%;
    position: relative;
    .topTime{
      color: #020202;
      font-size: 14px;
      position: absolute;
      left: 35px;
      top: 18px;
    }
    .topType{
      position: absolute;
      left: 50%;
      bottom: 2px;
      font-weight: 500;
      color: #333333;
      font-size: 20px;
      transform: translate(-50%,0);
    }
    .icon-user{
      position: absolute;
      right: 10px;
      bottom: 6px;
      width: 24px;
      height: 24px;
      transform: translate(0,0);
      background: url("~@/assets/plan/icon-user.png") no-repeat;
      background-size: 100% 100%;
    }
  }

  .m-phone-content{
    background: #ECEDEE;
    flex: 1;
    min-height: 300px;
    padding-left: 12px;
    padding-top: 20px;
    position: relative;
    .time{
      text-align: center;
      font-size: 12px;
      color: #999999;
    }
    .seedMessage{
      position: absolute;
      left: 12px;
      top: 84px;
      width: 241px;
      min-height: 72px;
      background: #FFFFFF;
      border-radius: 12px;
      box-shadow: 0px 1px 3px 2px rgba(223,223,223,0.2);
      padding: 5px 10px;
      font-size: 14px;
      line-height: 20px;
      color: #333333;
      word-break: break-all;
      &:before {
        content: '';
        position: absolute;
        border: 4px solid transparent;
        border-right: 4px solid #ffffff;
        left: -8px;
        bottom: 18px;
      }
      .u-total {
        position: absolute;
        width: 80px;
        right: -85px;
        bottom: 0px;
        text-align: left;
        color: #909399;
        font-weight: 300;
        font-size: 12px;
      }
    }
    .seedMessage_other{
      margin-top: 10px;
      width: 350px;
      //min-height: 317px;
      background: #FFFFFF;
      box-shadow: 0px 1px 3px 2px rgba(223,223,223,0.2);
      border-radius: 5px;
      padding: 12px 16px 0px 16px;
      .item-top{
        display: flex;
        align-items: center;
        justify-content: space-between;
        border-bottom: 1px solid #ECEDEE;
        padding-bottom: 12px;
        .txt{
          margin-left: 5px;
          color: #333;
          font-size: 14px;
        }
        .item-menu{
          width: 16px;
          height: 16px;
          background: url("~@/assets/plan/icon-menu.png") no-repeat;
          background-size: 100% 100%;
        }
      }
      .item-content{
        padding: 20px 0;
        border-bottom: 1px solid #ECEDEE;
        .title{
          font-weight: 500;
          font-size: 16px;
          color: #333333;
        }
        .date{
          font-size: 12px;
          color: #9CA8C3;
        }
        .info{
          margin-top: 15px;
          .info-item{
            display: flex;
            font-size: 14px;
            margin-bottom: 10px;
            .label{
              min-width: 60px;
              color: #9CA8C3;
            }
            .value{
              color: #31415F;
              word-break: break-all;
              // line-height: 20px;
            }
            &:nth-child(3){
              margin-bottom: 0;
            }
          }
        }
      }
      .item-footer{
        padding:10px 0 ;
        display: flex;
        justify-content: space-between;
        align-items: center;
        border-bottom: 1px solid #ECEDEE;
        font-size: 14px;
        color: #31415F;
      }
      .no-border{
        border: none;
      }
    }
  }
  .m-phone-footer{
    display: flex;
    align-items: center;
    height: 50px;
    background: #FFFFFF;
    box-shadow: 0px 0px 2px 0px rgba(223,223,223,0.5);
    .btn-keyboard {
      width: 24px;
      height: 24px;
      background: url("~@/assets/plan/keyboard-icon.png") no-repeat
      center / 100% 100%;
      margin: 0 18px;
    }
    .btn-camera {
      width: 28px;
      height: 28px;
      background: url("~@/assets/plan/camera-icon.png") no-repeat
      center / 100% 100%;
      margin-left: 20px;
    }
    .btn-expression {
      width: 28px;
      height: 28px;
      background: url("~@/assets/plan/expression-icon.png") no-repeat
      center / 100% 100%;
      margin: 0 30px;
    }
    .btn-input {
      width: 219px;
      height: 32px;
      border-radius: 16px;
      border: 1px solid #ecedee;
      padding: 0 8px 0 16px;
      .input-val {
        line-height: 32px;
      }
      .input-icon {
        width: 24px;
        height: 24px;
        background: url("~@/assets/plan/input-icon.png") no-repeat
        center / 100% 100%;
      }
    }
    .btn-name {
      width: 104px;
      border-left: 1px solid #e7e7e7;

      .name-icon {
        width: 10px;
        height: 8px;
        background: url("~@/assets/plan/more-icon.png") no-repeat
        center / 100% 100%;
        margin-right: 8px;
      }
    }
  }
}
.m-dialog-right{
  flex: 1;
  margin-left: 20px;
  position: relative;
  .m-footer{
    text-align: center;
    width: 100%;
    position: absolute;
    bottom: 0;
    left: 50%;
    transform: translate(-50%,0);
  }
}
.btn-text{
  user-select: none;
  color:$--color-primary;
  font-size: 14px;
  cursor: pointer;
}
</style>
