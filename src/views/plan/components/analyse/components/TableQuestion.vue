<template>
  <div class="mb-20">
    <div class="ques-title">第{{ numberToWords(index) }}题：{{ title }}</div>
    <div class="hsl"><div class="hs-nei">回收量：{{ tableTotal1 }}</div></div>
    <um-table-full
      v-loading="tableLoading1"
      :data="tableData1"
      height="340px"
    >
      <el-table-column
        prop="xh"
        width="100"
        label="序号"
      />
      <el-table-column
        prop="answer"
        label="答案"
        show-overflow-tooltip
      />
    </um-table-full>
    <pagination
      :total="tableTotal1"
      :page.sync="searchForm1.page.pageNum"
      :limit.sync="searchForm1.page.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList1('page')"
    />
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import { pageAnswer } from '@/api/plan/common'
import { numberToWords } from '@root/src/utils/index'
export default {
  name: 'TableQuestion',
  mixins: [getList1],
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
    index: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      listApi1: pageAnswer,
      numberToWords,
      searchForm1: {
        questionId: null
      }
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(v) {
        if (v) {
          this.searchForm1.questionId = v
          this.searchForm1 = {
            ...this.searchForm1,
            ...this.$parent.searchForm
          }
          this.getList1()
        }
      }
    }
  },
  methods: {
  }
}
</script>

<style lang="scss" scoped>
.ques-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}
.hsl {
  width: 100%;
  padding: 6px 0;
  background: #E5EEFA;
  border-radius: 4px;
  margin-bottom: 12px;
  .hs-nei {
    padding-left: 14px;
    font-size: 12px;
    color: #005DCF;
    font-weight: 500;
    border-left: 4px solid #005DCF;
  }
}
</style>
