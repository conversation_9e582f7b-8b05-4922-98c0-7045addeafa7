<template>
  <div :id="`chat_${keyId}`" ref="echarts" class="chart" />
</template>
<script>
export default {
  props: {
    xData: {
      type: Array,
      default: () => []
    },
    yData: {
      type: Array,
      default: () => []
    },
    keyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    xData: {
      immediate: true,
      deep: true,
      handler(v) {
        if (this.$refs.chart) {
          this.initChart()
        }
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.chart && this.chart.resize()
    },
    initChart() {
      this.chart = window.echarts.init(this.$refs.echarts)
      const option = {
        grid: {
          borderWidth: 0,
          top: '10',
          right: '20',
          left: '45',
          bottom: '20',
          textStyle: {
            color: '#fff'
          }
        },
        tooltip: {
          trigger: 'axis',
          axisPointer: {
            type: 'line',
            lineStyle: {
              color: '#4F85FB',
              width: 2
            }
          },
          backgroundColor: 'rgba(6, 12, 43, 0.70)', // 修改背景颜色
          borderColor: '#FFF', // 修改字体颜色
          extraCssText: 'box-shadow: none;border: none',
          formatter: (params) => { // 使用formatter函数修改需要的样式
            const res = `<div class='line-chart__tooltip' style="font-size: 14px;">
                <span style="color: #fff;">${params[0].value}%</span>
              </div>`
            return res
          }
        },
        xAxis: {
          type: 'category',
          axisTick: {
            show: false
          },
          data: this.xData
        },
        axisLabel: {
          interval: 0,
          formatter: function(value) {
            return value.length > 6 ? value.slice(0, 6) + '...' : value
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          splitLine: {
            interval: 5,
            lineStyle: {
              type: 'dashed'
            }
          },
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            data: this.yData,
            type: 'line',
            smooth: true,
            color: '#4F85FB',
            areaStyle: {
              color: new window.echarts.graphic.LinearGradient(0, 0, 0, 1, [
                {
                  offset: 0,
                  color: 'rgba(79, 133, 251, 0.2)'
                },
                {
                  offset: 1,
                  color: 'rgba(79, 133, 251, 0.01)'
                }
              ])
            }
          }
        ]
      }
      this.chart.setOption(option)
    },
    async exportChart() {
      if (!this.chart) return false
      try {
        return {
          key: this.keyId,
          image: this.chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          })
        }
      } catch (error) {
        return false
      }
    }
  }
}
</script>
<style lang="scss" scoped>
  .chart {
    width: 100%;
    height: calc(100% - 54px);
    padding: 20px;
  }
</style>
<style>
.line-chart__tooltip {
  width: 57px;
  text-align: center;
}
</style>

