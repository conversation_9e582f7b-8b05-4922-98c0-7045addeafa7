<template>
  <div class="m-block">
    <div v-for="item in list" :key="item.label" class="m-block__item">
      <div class="m-block__item--num">{{ dataInfo[item.key] || 0 }}{{ item.unit }}</div>
      <span class="m-block__item--label">{{ item.label }}</span>
    </div>
  </div>
</template>

<script>
export default {
  name: 'Statistics',
  props: {
    list: {
      type: Array,
      default: () => []
    },
    // 后台返回的数据应该是个对象
    dataInfo: {
      type: Object,
      default: () => ({})
    }
  }
}
</script>

<style lang="scss" scoped>
.m-block {
    display: flex;
    height: 100px;
    background: #F8FAFB;
    border-radius: 12px;
    &__item {
        display: flex;
        flex-direction: column;
        flex: 1;
        position: relative;
        align-items: center;
        justify-content: center;
        color: #31415F;
        font-size: 14px;
        &::after {
            content: '';
            position: absolute;
            width: 1px;
            height: 50px;
            border: 1px dashed #9CA8C3;
            right: 0;
            top: 50%;
            transform: translateY(-50%) scaleX(0.5);
        }
        &:last-child {
            &::after {
                display: none;
            }
        }
        &--num {
            font-family: Akrobat-Bold, Akrobat;
            font-weight: bold;
            font-size: 28px;
        }
    }
}
</style>
