<template>
  <div class="mb-20">
    <div class="ques-title">第{{ numberToWords(index) }}题：{{ title }}</div>
    <div class="hsl"><div class="hs-nei">回收量：{{ tableTotal1 }}</div></div>
    <um-table-full
      v-loading="tableLoading1"
      :data="tableData1"
      height="340px"
      :span-method="objectSpanMethod"
    >
      <el-table-column
        prop="xh"
        width="60"
        label="序号"
      />
      <el-table-column
        prop="typeName"
        min-width="150"
        label="分类"
        show-overflow-tooltip
      />
      <el-table-column
        prop="content"
        min-width="150"
        label="答案"
        show-overflow-tooltip
      />
      <el-table-column
        min-width="250px"
        prop="fileList"
        label="附件"
        show-overflow-tooltip
      >
        <template slot-scope="{row}">
          <div v-if="row.filePathList && row.filePathList.length">
            <div v-for="(file,idx) in row.filePathList" :key="idx" class="flex">
              <div class="primary f-cursor" @click="handlePreview(file)">{{ formatUrL(file) }}</div>
            </div>
          </div>
          <div v-else>-</div>
        </template>
      </el-table-column>
    </um-table-full>
    <pagination
      :total="tableTotal1"
      :page.sync="searchForm1.page.pageNum"
      :limit.sync="searchForm1.page.pageSize"
      background
      layout="total, sizes, prev, pager, next, jumper"
      @pagination="getList1('page')"
    />
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import { pageFile } from '@/api/plan/common'
import { extractFileNameAndType, numberToWords } from '@/utils'
export default {
  name: 'TableFileList',
  mixins: [getList1],
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
    index: {
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      listApi1: pageFile,
      numberToWords,
      searchForm1: {
        questionId: null
      }
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(v) {
        if (v) {
          this.searchForm1.questionId = v
          this.searchForm1 = {
            ...this.searchForm1,
            ...this.$parent.searchForm
          }
          this.getList1()
        }
      }
    }
  },
  methods: {
    tableCallBack1({ list }) {
      const resArr = []
      list.forEach(item => {
        item.rowSpan = 1
        if (item.likeContent || item.complainContent || item.likeFilePathList?.length || item.complainFilePathList?.length) {
          resArr.push({
            xh: item.xh,
            typeName: '点赞',
            content: item.likeContent,
            filePathList: item.likeFilePathList || [],
            rowSpan: 2
          }, {
            xh: item.xh,
            typeName: '吐槽',
            content: item.complainContent,
            rowSpan: 0,
            filePathList: item.complainFilePathList || []
          })
        } else {
          resArr.push(item)
        }
      })
      console.log(resArr)
      this.tableData1 = resArr
    },
    objectSpanMethod({ row, column, rowIndex, columnIndex }) {
      if (columnIndex === 0) {
        return {
          rowspan: row.rowSpan,
          colspan: 1
        }
      }
    },
    handlePreview(file) {
      window.open(file)
    },
    formatUrL(file) {
      const { fileName, fileType } = extractFileNameAndType(file)
      return `${fileName}.${fileType}`
    }
  }
}
</script>

<style lang="scss" scoped>
.ques-title {
  font-size: 14px;
  font-weight: 600;
  margin-bottom: 12px;
}
.hsl {
  width: 100%;
  padding: 6px 0;
  background: #E5EEFA;
  border-radius: 4px;
  margin-bottom: 12px;
  .hs-nei {
    padding-left: 14px;
    font-size: 12px;
    color: #005DCF;
    font-weight: 500;
    border-left: 4px solid #005DCF;
  }
}
</style>
