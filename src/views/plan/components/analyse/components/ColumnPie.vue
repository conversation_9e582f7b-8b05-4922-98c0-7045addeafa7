<template>
  <div :id="`chat_${keyId}`" ref="chart" class="chart" />
</template>

<script>
const echarts = window.echarts
export default {
  name: 'Column<PERSON>hart',
  props: {
    xData: {
      type: Array,
      default: () => []
    },
    yData: {
      type: Array,
      default: () => []
    },
    keyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null
    }
  },
  watch: {
    xData: {
      immediate: true,
      deep: true,
      handler(v) {
        if (this.$refs.chart) {
          this.initChart()
        }
      }
    }
  },
  mounted() {
    this.initChart()
  },
  methods: {
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      const option = {
        grid: {
          top: 10,
          left: 40,
          bottom: 20,
          right: 10
        },
        xAxis: {
          type: 'category',
          data: this.xData,
          splitLine: {
            show: false
          },
          axisTick: {
            show: false
          }
        },
        tooltip: {
          show: true
        },
        axisLabel: {
          interval: 0,
          formatter: function(value) {
            return value.length > 6 ? value.slice(0, 6) + '...' : value
          }
        },
        yAxis: {
          type: 'value',
          max: 100,
          splitLine: {
            show: false,
            interval: 5
          },
          axisLabel: {
            formatter: '{value}%'
          }
        },
        series: [
          {
            data: this.yData,
            type: 'bar',
            showBackground: true,
            barMaxWidth: '50',
            backgroundStyle: {
              color: 'rgba(123, 206, 255, 0.1)'
            },
            itemStyle: {
              color: new echarts.graphic.LinearGradient(0, 0, 0, 1, [
                { offset: 0, color: '#8FABFF' },
                { offset: 1, color: '#4C64FE' }
              ])
            }
          }
        ]
      }

      option && this.chart.setOption(option)
    },
    async exportChart() {
      if (!this.chart) return false
      try {
        return {
          key: this.keyId,
          image: this.chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          })
        }
      } catch (error) {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: calc(100% - 54px);
  padding: 20px;
}
</style>
