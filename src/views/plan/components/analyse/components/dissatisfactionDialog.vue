<!--不满意原因弹框-->
<template>
  <el-dialog
    :title="`${isDisReason ? `不满意${type===1?'原因':'分类'}` : `满意${type===1?'原因':'分类'}`}`"
    :visible.sync="innerVisible"
    width="950px"
    center
    @close="cancel"
  >
    <div class="f-flex-acjcsb margin-b-20">
      <el-radio-group v-if="type===1" v-model="currentTab">
        <el-radio-button v-for="item in isDisReason ? tabList : tabList1" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
      <div v-if="currentTab===1"><el-button :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportChange">导出</el-button></div>
    </div>
    <div v-if="currentTab===0" class="hsl f-flex">
      <div class="hs-nei">评价次数：{{ evaluateNumber }}</div>
      <div class="hs-nei no_border-left">评价人数：{{ evaluatePeopleNumber }}</div>
    </div>
    <um-table-full
      ref="table"
      v-loading="loading"
      scroll
      :data="currentTab === 0 ? disData : tableData"
    >
      <el-table-column
        type="index"
        label="序号"
        width="70"
      />
      <el-table-column
        prop="name"
        :label="`${isDisReason ? `不满意${type===1?'原因':'分类'}` : `满意${type===1?'原因':'分类'}`}`"
        width="200"
      >
        <template slot-scope="{row}">
          <um-tool-tips v-if="type===1" :content="row.name" :row="2" />
          <um-tool-tips v-if="type===2" :content="row.categoryName" :row="2" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="type===1"
        prop="categoryName"
        label="所属分类"
        show-overflow-tooltip
        :formatter="$formatterTable"
        min-width="140"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="count"
        label="选择次数"
        min-width="90"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="personNum"
        label="选择人数"
        min-width="90"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="peopleRate"
        label="人数占比"
        min-width="140"
      >
        <span slot-scope="{ row }" class="primary">{{ row.peopleRate }}</span>
      </el-table-column>
      <el-table-column
        v-if="currentTab==0"
        prop="countRate"
        label="次数占比"
        min-width="140"
      >
        <span slot-scope="{ row }" class="primary">{{ row.countRate }}</span>
      </el-table-column>
    </um-table-full>
  </el-dialog>
</template>
<script>
import { getOtherReasonList } from '@/api/plan/common'
import exportData from '@/mixins/exportData'
import { exportOtherReasonPlanAnalysis } from '@/api/export'
export default {
  name: 'DissatisfactionDialog',
  mixins: [exportData],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 是否不满意原因
    isDisReason: {
      type: Boolean,
      default: false
    },
    disData: {
      type: Array,
      default: () => []
    },
    targetId: {
      type: String,
      default: null
    },
    searchForm: {
      type: Object,
      default: () => {}
    },
    type: { // 1:原因  2:分类
      type: Number,
      default: 1
    },
    evaluatePeopleNumber: { // 评价总人数
      type: [String, Number],
      default: 0
    },
    evaluateNumber: { // 评价总次数
      type: [String, Number],
      default: 0
    },
    targetName: {
      type: String,
      default: ''
    } // 指标名称
  },
  data() {
    return {
      exportApi: exportOtherReasonPlanAnalysis,
      currentTab: 0,
      tabList: [
        {
          label: '不满意原因统计',
          value: 0
        },
        {
          label: '其他不满意原因',
          value: 1
        }
      ],
      tabList1: [
        {
          label: '满意原因统计',
          value: 0
        },
        {
          label: '其他满意原因',
          value: 1
        }
      ],
      tableData: [],
      loading: false
    }
  },
  computed: {
    innerVisible: {
      get() {
        if (this.visible) {
          this.getOtherReasonList()
        }
        return this.visible
      },
      set(val) {
        if (!val) this.currentTab = 0
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    cancel() {
      this.innerVisible = false
    },
    formatExportParams() {
      const params = { ...this.searchForm, name: this.targetName, satisfactionFlag: +!this.isDisReason, targetId: this.targetId }
      return params
    },
    exportChange() {
      this.exportData()
    },
    // 获取其它原因
    getOtherReasonList() {
      // const load = this.$load()
      this.tableData = []
      this.loading = true
      getOtherReasonList({ ...this.searchForm, targetId: this.targetId }).then(res => {
        const { otherDissatisfactionReasonList, otherSatisfactionReasonList } = res.data
        this.tableData = (this.isDisReason ? (otherDissatisfactionReasonList || []) : (otherSatisfactionReasonList || [])).map(item => {
          return {
            name: item.reasonName,
            categoryName: item.categoryName
          }
        })
      }).finally(() => {
        this.loading = false
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.hsl {
  width: 100%;
  padding: 6px 0;
  background: #E5EEFA;
  border-radius: 4px;
  margin-bottom: 12px;
  .hs-nei {
    padding-left: 14px;
    font-size: 12px;
    color: #005DCF;
    font-weight: 500;
    border-left: 4px solid #005DCF;
  }
  .hs-nei.no_border-left{
    border-left: 0;
    margin-left: 25px;
  }
}
</style>
