<template>
  <div :id="`chat_${keyId}`" class="chart">
    <div ref="chart" class="chart-content" />
  </div>
</template>

<script>
const echarts = window.echarts
const COLORS = ['#3D80FC', '#62DE85', '#F7C739', '#F3653C', '#7262DE', '#E7CAAF', '#263EDE', '#DB2F8F', '#B63CF3', '#62D1DE', '#e6d15e', '#aae850', '#f01742', '#ccdbc4', '#fa2670', '#f3914a', '#3c16e2', '#2083e1', '#bb1dfe', '#6c3d2d']
export default {
  name: 'Circle<PERSON>ie',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    keyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      chart: null,
      COLORS
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler(v) {
        if (this.$refs.chart) {
          this.initChart()
        }
      }
    }
  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.chart && this.chart.resize()
    },
    initChart() {
      this.chart = echarts.init(this.$refs.chart)
      const option = {
        backgroundColor: 'transparent',
        grid: {
          top: 0,
          right: 0,
          bottom: 0
        },
        graphic: [{
          type: 'circle',
          left: '20',
          top: '75',
          shape: {
            r: 80
          },
          style: {
            fill: '#ECF0F3',
            shadowBlur: 20,
            shadowColor: '#D1D9E6',
            shadowOffsetX: 10,
            shadowOffsetY: 10
          }
        }, {
          type: 'circle',
          left: '30',
          top: '83',
          shape: {
            r: 71
          },
          style: {
            fill: '#fff'
          }
        }],
        legend: {
          orient: 'vertical',
          left: '190',
          top: 'middle',
          type: 'scroll',
          height: 300,
          itemGap: 16,
          itemWidth: 12,
          itemHeight: 14,
          selectedMode: true,
          tooltip: {
            show: true
          },
          data: this.chartData.map(item => ({
            name: item.itemName,
            tooltip: {
              show: true,
              formatter: item.itemName
            }
          })),
          icon: 'circle',
          formatter: (name) => {
            const item = this.chartData.find(i => i.itemName === name)
            const value = item ? item.proportion : 0
            return `{name|${(name && name.length > 16 ? name.substring(0, 16) + '...' : name) || '--'}}{value|${value}%}`
          },
          textStyle: {
            rich: {
              name: {
                color: '#333',
                width: 140,
                padding: [0, 10, 0, 0],
                overflow: 'truncate',
                ellipsis: '...'
              },
              value: {
                color: '#6C7B96',
                width: 60,
                align: 'right'
              }
            }
          }
        },
        series: [{
          type: 'pie',
          center: ['101', '50%'],
          radius: '55',
          avoidLabelOverlap: false,
          label: {
            show: false
          },
          labelLine: {
            show: false
          },
          itemStyle: {
            color: (params) => {
              return COLORS[params.dataIndex] || COLORS[params.dataIndex % COLORS.length]
            }
          },
          data: this.chartData.map(item => ({
            value: item.chooseCount,
            name: item.itemName
          }))
        }]
      }
      this.chart.setOption(option)
    },
    async exportChart() {
      if (!this.chart) return false
      try {
        return {
          key: this.keyId,
          image: this.chart.getDataURL({
            type: 'png',
            pixelRatio: 2,
            backgroundColor: '#fff'
          })
        }
      } catch (error) {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.chart {
  width: 100%;
  height: calc(100% - 54px);
  .chart-content {
    width: 100%;
    height: 100%;
  }
}
</style>
