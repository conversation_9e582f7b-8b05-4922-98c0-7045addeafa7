<template>
  <div class="mb-20">
    <div class="ques-title f-flex-acjcsb">
      <div class="ques-title-left">第{{ numberToWords(index) }}题：{{ title }}</div>
      <div class="hsl"><div class="hs-nei">回收量：{{ total }}</div></div>
    </div>
    <div class="g-container">
      <div class="g-container__l">
        <template v-if="tableData.length">
          <el-select v-model="chartType" placeholder="请选择" style="width: 113px;margin-left: 20px;margin-top: 20px">
            <el-option
              v-for="item in chartTypes"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
          <components :is="chartType" ref="chatComponent" :key-id="id" :x-data="xData" :y-data="yData" :chart-data="tableData" :total="total" />
        </template>
        <um-empty v-else />
      </div>
      <div class="g-container__r">
        <um-table-full
          ref="table"
          :data="tableData"
          height="364px"
        >
          <el-table-column
            key="itemName"
            prop="itemName"
            label="选项"
            min-width="200px"
            :formatter="$formatterTable"
          />
          <!-- 深访问卷的多选题才有 -->
          <el-table-column
            v-if="questionTypeCode === 2200082 && isDepth"
            key="itemTypeName"
            prop="itemTypeName"
            label="所属分类"
            min-width="160px"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="itemScore"
            prop="itemScore"
            label="选项分值"
            min-width="120px"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="chooseCount"
            prop="chooseCount"
            label="选择次数"
            min-width="120px"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="proportion"
            prop="proportion"
            label="占比"
            min-width="100px"
            :formatter="$formatterTable"
          >
            <span slot-scope="{ row }">{{ row.proportion }}%</span>
          </el-table-column>
        </um-table-full>
      </div>
    </div>
  </div>
</template>

<script>
import CirclePie from './CirclePie'
import ColumnPie from './ColumnPie'
import LinePie from './LinePie'
import { getStatisticalResult } from '@/api/plan/common'
import { numberToWords } from '@root/src/utils/index'
export default {
  name: 'ChartQuestion',
  components: { CirclePie, ColumnPie, LinePie },
  props: {
    title: {
      type: String,
      default: ''
    },
    id: {
      type: [String, Number],
      default: ''
    },
    questionTypeCode: {
      type: [String, Number],
      default: ''
    },
    index: {
      type: [String, Number],
      default: 0
    },
    isDepth: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      numberToWords,
      chartTypes: [
        {
          label: '饼状图',
          value: 'CirclePie'
        },
        {
          label: '柱状图',
          value: 'ColumnPie'
        },
        {
          label: '折线图',
          value: 'LinePie'
        }
      ],
      chartType: 'CirclePie',
      tableData: [],
      xData: [],
      yData: {},
      total: 0
    }
  },
  watch: {
    id: {
      immediate: true,
      handler(v) {
        if (v) {
          v && this.getStatisticalResult(v, this.questionTypeCode)
        }
      }
    }
  },
  methods: {
    getStatisticalResult(questionId, questionTypeCode) {
      getStatisticalResult({ ...this.$parent.searchForm, questionId, questionTypeCode }).then(res => {
        const arr = res.data || []
        this.tableData = arr
        this.xData = arr.map(item => item.itemName)
        this.yData = arr.map(item => item.proportion)
        this.total = arr[0] ? arr[0].answerNum : 0
      })
    },
    doLayout() {
      this.$refs.table.el.doLayout()
    },
    exportChart() {
      return this.$refs.chatComponent.exportChart()
    }
  }
}
</script>

  <style lang="scss" scoped>
  .ques-title {
    font-size: 14px;
    font-weight: 600;
    margin-bottom: 12px;
    margin-left: 5px;
    &-left {
      width: 445px;
    }
    .hsl {
      width: calc(100% - 465px);
      padding: 6px 0;
      background: #E5EEFA;
      border-radius: 4px;
      .hs-nei {
        padding-left: 14px;
        font-size: 12px;
        color: #005DCF;
        font-weight: 500;
        border-left: 4px solid #005DCF;
      }
    }
  }
  .g-container {
    display: flex;
    height: 364px;
    &__l {
        width: 445px;
        background: #F8FAFB;
        border-radius: 8px;
        margin-right: 20px;
        // padding: 20px;
    }
    &__r {
        flex: 1;
        overflow-x: auto;
    }
  }
  </style>

