<template>
  <div>
    <el-card shadow="never" class="margin-b-20">
      <div class="m-top-title">
        <div>计划名称：{{ planName||"--" }}</div>
        <div v-if="pageType==='satisfactionSurvey'" class="margin-l-20">触点：{{ pointName ||"--" }}</div>
      </div>
      <el-radio-group v-model="currentTab">
        <el-radio-button v-for="item in tabList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
      <el-form v-model="searchForm" label-width="80px" class="margin-t-20">
        <el-row type="flex" :gutter="20" style="flex-wrap: wrap">
          <el-col :span="8">
            <el-form-item label="区域/城市：">
              <UmBusOrgan v-model="searchForm.projDto" style="width: 100%;" :is-auth="pageType==='specialissues'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="项目/分期：">
              <UmBusProject v-model="searchForm.projDto" style="width: 100%;" :area-company-id="searchForm.projDto.areaCompanyId" :city-company-id="searchForm.projDto.cityCompanyId"  :is-auth="pageType==='specialissues'" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="月份：" label-width="50px">
              <el-date-picker
                v-model="searchForm.month"
                type="month"
                :editable="false"
                format="yyyy-MM"
                value-format="yyyy-MM"
                placeholder="请选择"
                :clearable="false"
                style="width: 100%;"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row type="flex" justify="end">
              <div><el-button type="primary" icon="el-icon-search" @click="search">搜索</el-button></div>
              <div class="f-mg-l10">
                <el-button v-if="$checkPermission(pageType === 'satisfactionSurvey' ? ['DYJHGL_MYDDY_TJFX_DC'] : ['DYJHGL_ZXWTDY_TJFX_DC'])&&currentTab===0" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportChangeChart">导出</el-button>
                <el-button v-if="$checkPermission(pageType === 'satisfactionSurvey' ? ['DYJHGL_MYDDY_TJFX_WJFX_DC'] : ['DYJHGL_ZXWTDY_TJFX_WJFX_DC']) && currentTab===1" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportChangeChart">导出</el-button>
              </div>
            </el-row>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div class="g-container">
      <!-- 满意度分析 -->
      <div v-show="currentTab === 0">
        <el-row type="flex" justify="space-between" class="f-mg-b10">
          <CommonTitle title="满意度概况" height="20" padding-left="0" />
          <el-radio-group v-model="searchForm.type" class="f-flex-shrink0">
            <el-radio-button v-for="item in tabStatList" :key="item.value" :label="item.value">
              {{ item.label }}
            </el-radio-button>
          </el-radio-group>
        </el-row>
        <el-row v-for="item in targetList" :key="item.targetId" :gutter="20" style="margin-bottom: 30px;">
          <el-col :span="8">
            <CommonTitle :title="item.targetName" height="20" padding-left="0" :show-icon="false" />
            <TargetChart ref="targetChart" :key-id="item.targetId" :chart-data="item.pieList" />
          </el-col>
          <el-col :span="8">
            <el-row type="flex" align="middle" style="margin-bottom: 20px;">
              <CommonTitle :title="`不满意${searchForm.type===1?'原因':'分类'}（Top5）`" height="20" padding-left="0" margin-bottom="0" :show-icon="false" />
              <span class="u-more" @click="watchMore(item.dissatisfactionReason, item.disAllCount,item.disAllPersonNum ,item.targetId,true,item.targetName)">查看更多<i class="el-icon-arrow-right" /></span>
            </el-row>
            <Quota :title="`不满意${searchForm.type===1?'原因':'分类'}`" :type="searchForm.type" :list="item.dissatisfactionReason" :total="item.disAllCount" />
          </el-col>
          <el-col :span="8">
            <el-row type="flex" align="middle" style="margin-bottom: 20px;">
              <CommonTitle :title="`满意${searchForm.type===1?'原因':'分类'}（Top5）`" height="20" padding-left="0" margin-bottom="0" :show-icon="false" />
              <span class="u-more" @click="watchMore(item.satisfactionReason, item.allCount,item.allPersonNum,item.targetId,false,item.targetName)">查看更多<i class="el-icon-arrow-right" /></span>
            </el-row>
            <Quota :title="`满意${searchForm.type===1?'原因':'分类'}`" :type="searchForm.type" :list="item.satisfactionReason" :total="item.allCount" />
          </el-col>
        </el-row>
        <el-empty v-if="!targetList.length" :image="require('@/assets/plan/zanwu.png')" />
      </div>
      <!-- 整体问卷分析 -->
      <div v-show="currentTab === 1">
        <el-row type="flex" align="middle" justify="space-between" class="margin-b-20">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">数据概况</div>
          </div>
          <span>数据更新时间：{{ allOverviewInfo.updateTime | formatterTable }}</span>
        </el-row>

        <Statistics :list="overviewInfo" :data-info="allOverviewInfo" class="mb-36" />
        <div class="g-plan-title-icon margin-b-20">
          <div class="u-txt-icon" />
          <div class="u-txt-title">统计报表</div>
        </div>
        <components
          :is="QUESTION_TYPE[item.questionTypeCode]"
          v-for="item in questionList"
          :id="item.questionId"
          ref="dynamicComponent"
          :key="item.questionId"
          :question-type-code="item.questionTypeCode"
          :index="item.xh"
          :title="item.questionName"
        />
        <el-empty v-if="!questionList.length" :image="require('@/assets/plan/zanwu.png')" />
      </div>
    </div>
    <!--满意/不满意原因弹框-->
    <DissatisfactionDialog
      :type="searchForm.type"
      :visible.sync="showDialog"
      :dis-data="disData"
      :evaluate-number="disOrAllCount"
      :evaluate-people-number="disOrAllPersonNum"
      :is-dis-reason="isDisReason"
      :search-form="searchForm"
      :target-id="targetId"
      :target-name="targetName"
    />
  </div>
</template>

<script>
import Statistics from './components/Statistics'
import Quota from './components/Quota'
import TableQuestion from './components/TableQuestion'
import TableFileList from './components/TableFileList'
import ChartQuestion from './components/ChartQuestion'
import TargetChart from './components/TargetChart'
import CommonTitle from '@/components/CommonTitle'
import { getTargetList, getTargetInfo, getDataGeneral, getQuestionList } from '@/api/plan/common'
import DissatisfactionDialog from './components/dissatisfactionDialog.vue'
import UmBusOrgan from '@/components/UmBusOrgan'
import UmBusProject from '@/components/UmBusProject'
import exportData from '@/mixins/exportData'
import { exportPlanAnalysisTargetAnalysis, exportQuestionnaireAnalysis } from '@/api/export'
const QUESTION_TYPE = {
  2200081: 'ChartQuestion', // '单选题',
  2200082: 'ChartQuestion', // '多选题',
  2200083: 'ChartQuestion', // '打分题',
  2200084: 'TableQuestion', // '填空题',
  // 2200085: 'TableQuestion' // '文本描述',
  2200086: 'TableFileList' // '附件题',
}
export default {
  name: 'PlanSatisfactionSurveyAnalyse',
  components: { Statistics, Quota, CommonTitle, TargetChart, TableQuestion, TableFileList, ChartQuestion, DissatisfactionDialog, UmBusOrgan, UmBusProject },
  mixins: [exportData],
  props: {},
  data() {
    const DATE = new Date()
    return {
      exportApi: exportPlanAnalysisTargetAnalysis,
      tabStatList: [{ label: '按原因统计', value: 1 }, { label: '按分类统计', value: 2 }],
      showDialog: false,
      targetList: [], // 指标集合
      targetInfoList: [],
      tabList: [
        {
          label: '指标分析',
          value: 0
        },
        {
          label: '问卷分析',
          value: 1
        }
      ],
      currentTab: 0,
      overviewInfo: [
        {
          label: '样框量',
          key: 'frameNum'
        },
        {
          label: '样本量',
          key: 'recycleNum'
        },
        {
          label: '回收率',
          key: 'recycleRate',
          unit: '%'
        },
        {
          label: '平均答题时长',
          key: 'avgAnswerTime'
        }
      ],
      allOverviewInfo: {},
      QUESTION_TYPE,
      questionList: [],
      searchForm: {
        type: 1, // 1,按照原因统计2,按照分类统计
        projDto: {},
        month: DATE.getFullYear() + '-' + (DATE.getMonth() + 1).toString().padStart(2, '0'),
        planUuid: null
      },
      planName: '', // 计划名称
      pointName: '', // 触点
      pageType: '', // 页面类型 根据页面名称 satisfactionSurvey 满意度调研 specialissues 专项调研
      imageList: [], // 导出echart图片
      disData: [], // 查看更多的数据
      disOrAllCount: 0, // 不Or不满意总次数=评价总次数
      disOrAllPersonNum: 0, // 不Or不满意总人数=评价总人数
      isDisReason: false, // 是否是不满意原因 true 满意原因 false 不满意原因
      targetId: null, // 点击查看更多的targetId
      targetName: '' // 指标名称
    }
  },
  watch: {
    async currentTab(v) {
      //  && !Object.keys(this.allOverviewInfo).length
      if (v === 1) {
        this.getQuestionData()
      } else { //  if (v === 0 && !this.targetList.length)
        this.init()
        this.$nextTick(() => {
          this.$refs.dynamicComponent && this.$refs.dynamicComponent.forEach(item => {
            const doLayout = item.doLayout
            typeof doLayout === 'function' && doLayout()
          })
        })
      }
    },
    'searchForm.type'(v) {
      this.search()
    }
  },
  created() {
    const { planName, pointName, uuid, pageType } = this.$route.query
    this.planName = planName
    this.pointName = pointName
    this.pageType = pageType
    this.searchForm.planUuid = uuid
    this.init()
  },
  methods: {
    search() {
      this.currentTab === 0 ? this.init() : this.getQuestionData()
    },
    async getQuestionData() {
      const load = this.$load()
      try {
        await this.getDataGeneral()
        await this.getQuestionList()
      } catch (error) {

      } finally {
        load.close()
      }
    },
    async init() {
      const load = this.$load()
      try {
        await this.getTargetList()
        const length = this.targetList.length
        const promiseArr = []
        for (let index = 0; index < length; index++) {
          promiseArr.push(this.getTargetInfo(this.targetList[index].targetId, index))
        }
        await Promise.all(promiseArr)
      } catch (error) {
        console.log(error)
      } finally {
        load.close()
      }
    },
    // 获取问卷指标集合
    getTargetList() {
      return getTargetList(this.searchForm).then(res => {
        this.targetList = res.data || []
      })
    },
    // 获取指标分析具体数据
    getTargetInfo(targetId, index) {
      return getTargetInfo({ ...this.searchForm, targetId }).then(res => {
        const { pieList, dissatisfactionReason, satisfactionReason, dissatisfactionNum, satisfactionNum, satisfactionCountNum, dissatisfactionCountNum } = res.data
        this.$set(this.targetList, index, {
          ...this.targetList[index],
          pieList, // 饼图数据
          dissatisfactionReason, // 不Or满意原因数组对象
          satisfactionReason, // 满意原因对象
          allCount: satisfactionCountNum, // 满意总次数
          disAllCount: dissatisfactionCountNum, // 不满意总次数
          allPersonNum: satisfactionNum, // 满意总人数
          disAllPersonNum: dissatisfactionNum // 不满意总人数
        })
      })
    },
    watchMore(disOrSatisfactionReason, disOrAllCount, disOrAllPersonNum, targetId, isDisReason, targetName) {
      // disOrSatisfactionReason // 不Or满意原因数组对象
      this.disOrAllCount = disOrAllCount // 不Or不满意总次数=评价总次数
      this.disOrAllPersonNum = disOrAllPersonNum // 不Or不满意总人数=评价总人数
      this.targetId = targetId // 点击查看更多的targetId
      this.isDisReason = isDisReason // 是否是不满意原因 true 满意原因 false 不满意原因
      this.targetName = targetName
      this.disData = disOrSatisfactionReason
      this.showDialog = true
    },
    // 获取所有问题列表
    getQuestionList() {
      this.questionList = []
      return getQuestionList(this.searchForm).then(res => {
        const data = res.data || []
        let xh = 0
        this.questionList = data.map(item => {
          if (item.questionTypeCode !== 2200085) {
            xh++
          }
          return {
            ...item,
            xh
          }
        })
      })
    },
    // 获取数据概况
    getDataGeneral() {
      return getDataGeneral({ ...this.searchForm, planId: this.$route.query.id }).then(res => {
        this.allOverviewInfo = res.data || {}
      })
    },
    formatExportParams() {
      const params = { ...this.searchForm, imageList: this.imageList }
      if (this.currentTab === 1) {
        params.type = 1
      }
      return params
    },
    async exportChangeChart() {
      try {
        if (this.currentTab === 0) {
          if (Array.isArray(this.$refs.targetChart) && this.$refs.targetChart.length > 0) {
            this.imageList = await Promise.all(this.$refs.targetChart.map(item => item.exportChart()))
            this.exportApi = exportPlanAnalysisTargetAnalysis
            this.exportData()
          } else {
            this.$message.warning('暂无数据')
          }
        } else {
          if (Array.isArray(this.$refs.dynamicComponent) && this.$refs.dynamicComponent.length > 0) {
            this.imageList = await Promise.all(this.$refs.dynamicComponent.filter(item => item.exportChart).map(item => item.exportChart()))
            this.exportApi = exportQuestionnaireAnalysis
            this.exportData()
          } else {
            this.$message.warning('暂无数据')
          }
        }
      } catch (e) {
        console.log(e)
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  // height: calc(100vh - 76px);
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  border-radius: 12px;
  padding: 20px;
  overflow-y: auto;
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-36 {
    margin-bottom: 36px;
  }
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  .u-more {
    display: flex;
    color: #999999;
    flex-shrink: 0;
    cursor: pointer;
    &:hover {
      color: $--color-primary;
    }
    i {
      margin-left: 4px;
    }
  }
}
.m-top-title{
  display: flex;
  align-items: center;
  margin-bottom: 20px;
  font-size: 16px;
  font-weight: 600;
  color: #333;
}
</style>

