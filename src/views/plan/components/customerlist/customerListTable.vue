<template>
  <el-dialog
    title="选择客户"
    :visible.sync="customerVisible"
    z-index="1999"
    width="1400px"
    center
    :close-on-click-modal="false"
    :destroy-on-close="true"
    append-to-body
    @close="handleClose"
  >
    <el-form ref="searchForm" :model="searchForm" label-width="90px">
      <el-row :gutter="30">
        <el-col :span="8">
          <el-form-item label="区域/城市：" prop="projDto">
            <UmBusOrgan v-model="searchForm.projDto" isAuth />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目/分期：" prop="projDto">
            <UmBusProject v-model="searchForm.projDto" :area-company-id="searchForm.projDto.areaCompanyId" :city-company-id="searchForm.projDto.cityCompanyId" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="楼栋/房间：" prop="houseIds">
            <UmBusRoom v-model="searchForm.houseIds" multiple :emit-path="false" :project-id="searchForm.projDto.projectId" :stage-id="searchForm.projDto.stageId" />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="调研业态：" prop="productTypeIds">
            <el-select-tree
              v-model="searchForm.productTypeIds"
              :options="productList"
              filterable
              multiple
              show-checkbox
              clearable
              collapse-tags
              :show-all-levels="false"
              :props="{
                disabled: 'disable',
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="操盘条线：" prop="cp">
            <el-select v-model="searchForm.cp" style="width: 100%" clearable placeholer="请选择操盘条线">
              <el-option v-for="item in operateList" :key="item.dictValue" :label="item.dictName" :value="item.dictValue" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="装修类型：" prop="decorationStandard">
            <el-select v-model="searchForm.decorationStandard" filterable clearable style="width: 100%" placeholder="请选择">
              <el-option label="毛坯" value="01" />
              <el-option label="精装" value="02" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="项目配套：" prop="supportProductTypeId">
            <el-select-tree
              v-model="searchForm.supportProductTypeId"
              :options="supportList"
              filterable
              clearable
              :show-all-levels="true"
              :props="{
                disabled: 'disable',
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item label="对象类型：" prop="customerType">
            <el-select v-model="searchForm.customerType" style="width: 100%" clearable placeholer="请选择对象类型">
              <el-option v-for="item in customerList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="ownerName" label="客户姓名">
            <el-input v-model="searchForm.ownerName" placeholder="请输入客户姓名" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="mobile" label="手机号码">
            <el-input v-model="searchForm.mobile" placeholder="请输入手机号码" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="certNumber" label="证件号码">
            <el-input v-model="searchForm.certNumber" placeholder="请输入证件号码" clearable />
          </el-form-item>
        </el-col>
        <el-col :span="8">
          <el-form-item prop="certNumber" label="实际交付日期">
            <el-date-picker
              v-model="dateRange"
              type="daterange"
              value-format="yyyy-MM-dd"
              format="yyyy-MM-dd"
              range-separator="至"
              style="width: 100%"
              start-placeholder="开始日期"
              end-placeholder="结束日期">
            </el-date-picker>
          </el-form-item>
        </el-col>
        <el-col :span="4" class="fr text-right mb-20">
          <el-button type="primary" :loading="tableLoading" icon="el-icon-search" @click="getList">搜索</el-button>
        </el-col>
      </el-row>
    </el-form>
    <div v-loading="tableLoading">
      <um-table-full
        ref="tableData"

        scroll
        style="width: 100%"
        :data="tableData"
        row-key="ownerId"
        height="398"
        @selection-change="handleSelectionChange"
      >
        <el-table-column
          type="selection"
          width="70"
          reserve-selection
          :selectable="selectable"
        />
        <el-table-column width="80" prop="xh" label="序号" />
        <el-table-column min-width="100" prop="ownerName" label="姓名" :formatter="$formatterTable" />
        <el-table-column width="80" prop="gender" label="性别" :formatter="$formatterTable">
          <span slot-scope="{row}">{{ SEX[row.gender] }}</span>
        </el-table-column>
        <el-table-column width="140" prop="mobile" label="手机号码" :formatter="$formatterTable" />
        <el-table-column width="140" label="证件信息">
          <template slot-scope="scope">
            <div>{{ scope.row.certTypeName }}</div>
            <div>{{ scope.row.certNumber }}</div>
          </template>
        </el-table-column>
        <el-table-column min-width="250" prop="houseFullName" label="房间号" :formatter="$formatterTable">
          <template slot-scope="scope">
            <div v-for="item in scope.row.houses" :key="item.houseId">
              <div class="m-house">
                <div class="m-house__name" :title="item.houseFullName">{{ item.houseFullName }}</div>
                <span class="m-house__tag">{{ item.customerTypeName }}</span>
              </div>
            </div>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        class="f-mg-t20"
        @pagination="getList('page')"
      />
    </div>

    <div class="f-tac f-mg-t20">
      <el-button type="primary" plain @click="customerVisible = false">取消</el-button>
      <el-button type="primary" @click="submit">添加</el-button>
      <el-button type="primary" @click="batchSubmit">批量添加</el-button>
    </div>
  </el-dialog>
</template>

<script>
import {
  pageCustomer
} from '@/api/plan/specialissues'
import { deepClone } from '@/utils'
import getList from '@/mixins/getList'
import UmBusRoom from '@/components/UmBusRoom'
import { getProductType, getProjectSupport, getSysDictList } from '@/api/common'
import { SYS_DICT_CODE, CUSTOMER_TYPE } from '@/enum'
export default {
  name: 'CustomerListTable',
  components: { UmBusRoom },
  mixins: [getList],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    selectCustomers: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      listApi: pageCustomer,
      SEX: {
        0: '男',
        1: '女',
        2: '未知'
      },
      dateRange: [],
      searchForm: {
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null,
          projectId: null,
          stageId: null
        },
        deliverTimeBegin: null,
        deliverTimeEnd: null,
        houseIds: [],
        productTypeIds: [],
        supportProductTypeId: null,
        cp: null,
        customerType: null,
        decorationStandard: null,
        certNumber: null,
        mobile: '',
        ownerName: '' // 客户姓名
      },
      optionYear: [],
      multipleSelection: [],
      tagList: [],
      productList: [], // 调研业态数据
      operateList: [], // 操盘条线数据
      supportList: [], // 项目配套数据
      customerList: [
        {
          label: '业主',
          value: CUSTOMER_TYPE.OWNER_OUT
        },
        {
          label: '同住人',
          value: CUSTOMER_TYPE.TOGETHER_OUT
        },
        {
          label: '租户',
          value: CUSTOMER_TYPE.TENANT_OUT
        }
      ] // 对象类型数据
    }
  },
  computed: {
    customerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(val) {
      if (val) {
        this.tagList = deepClone(this.selectCustomers)
        this.getProductType()
        this.getProjectSupport()
        this.getSysDictList()
        this.$nextTick(_ => {
          // !this.tableLoading && !this.tableData.length && this.getList()
        })
      }
    },
    dateRange(v) {
      if (!v || !v.length) {
        this.searchForm.deliverTimeBegin = null
        this.searchForm.deliverTimeEnd = null
      } else {
        [this.searchForm.deliverTimeBegin, this.searchForm.deliverTimeEnd] = v
      }
    }
  },
  methods: {
    // 判断当前行是否可选
    selectable(row) {
      return this.tagList.findIndex(item => item.ownerId === row.ownerId) === -1
    },
    // 获取调研业态
    getProductType() {
      if (this.productList.length) return
      getProductType().then(res => {
        this.productList = res.data || []
      })
    },
    // 获取项目配套
    getProjectSupport() {
      if (this.supportList.length) return
      getProjectSupport().then(res => {
        this.supportList = res.data || []
      })
    },
    // 获取操盘条线
    getSysDictList() {
      getSysDictList({ dictCode: SYS_DICT_CODE.OPERATE }).then(res => {
        this.operateList = res.data || []
      })
    },
    handleSelectionChange(selection) {
      this.multipleSelection = selection
    },
    handleClose() {
      this.$refs.searchForm.resetFields()
      this.$refs.tableData.el.clearSelection()
      this.multipleSelection = []
      this.customerVisible = false
    },
    submit() {
      const tagList = deepClone(this.multipleSelection)
      if (tagList && tagList.length) {
        this.$emit('change', tagList)
        this.customerVisible = false
      } else {
        this.$message.warning('请选择客户！')
      }
    },
    // 批量添加，数据量限制在2w以内
    batchSubmit() {
      const load = this.$load()
      pageCustomer({
        ...this.searchForm,
        page: {
          pageNum: 1,
          pageSize: 10000
        }
      }).then(res => {
        const { list } = res.data || {}
        if (!list.length) {
          this.$message.warning('没有符合条件的数据，请调整搜索条件后重试！')
          return
        }
        this.$emit('change', list)
        this.customerVisible = false
      }).finally(() => load.close())
    }
  }
}
</script>

<style scoped lang="scss">
::v-deep {
  .el-cascader__tags .el-tag {
    max-width: 40%;
  }
  .el-cascader__tags {
    flex-wrap: nowrap;
  }
  .el-cascader__search-input {
    max-width: 20%;
    margin-left: 5px;
  }
}
.m-house {
  display: flex;
  align-items: center;
  &__name {
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  &__tag {
    border-top-left-radius: 10px;
    border-bottom-right-radius: 10px;
    background-color: $--color-primary;
    color: #fff;
    font-size: 12px;
    line-height: 17px;
    margin-left: 5px;
    padding: 0 8px;
    flex-shrink: 0;
  }
}
.customer401{
  width: 32px;
  height: 14px;
  background: url("~@/assets/plan/yezhu.png") no-repeat center/100%;
  margin-left: 5px;
}
.customer402{
  width: 32px;
  height: 14px;
  background: url("~@/assets/plan/jiaren.png") no-repeat center/100%;
}
.customer403{
  width: 32px;
  height: 14px;
  background: url("~@/assets/plan/zuhu.png") no-repeat center/100%;
}
.margin-l-6{
  margin-left: 6px;
}
.el-dialog__wrapper{
  overflow: hidden;
}
</style>
