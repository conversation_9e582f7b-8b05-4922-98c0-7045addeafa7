
<template>
  <SurveyInfo ref="SurveyInfo" type="specialissues" />
</template>

<script>
import SurveyInfo from '../../components/surveyInfo'
export default {
  name: 'PlanSpecialIssuesSurveyInfo',
  components: { SurveyInfo },
  data() {
    return {
    }
  },
  mounted() {
    const { id, uuid } = this.$route.query
    if (id) {
      this.$refs.SurveyInfo.searchForm1.planId = id
      this.$refs.SurveyInfo.searchForm1.uuid = uuid
      this.$refs.SurveyInfo.getTopData()
      this.$refs.SurveyInfo.getList1()
    }
  }
}
</script>
