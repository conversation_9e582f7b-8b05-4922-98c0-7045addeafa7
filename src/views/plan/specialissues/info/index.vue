<template>
  <div class="m_plan_info">
    <el-card shadow="never">
      <div class="g_card_bg">
        <div class="g-plan-title-icon">
          <div class="u-txt-icon" />
          <div class="u-txt-title">调研计划</div>
        </div>
      </div>
      <div class="g_plan_info">
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">计划名称：</div>
            <div class="value">{{ detail.name ||'--' }}</div>
          </div>
          <div class="flex g_plan_info_rowItem margin-r-10">
            <div class="label">调研问卷：</div>
            <div class="value">{{ detail.quesTemplateName ||'--' }}</div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">填写有效期：</div>
            <div class="value">
              <span v-if="detail.quesEffectFlag === 0">不设置</span>
              <span v-else-if="detail.quesEffectFlag === 1">有效期{{ detail.quesEffectValue }} 天</span>
              <span v-else>--</span>
            </div>
          </div>
          <div class="flex g_plan_info_rowItem margin-r-10">
            <div class="label">结束时间：</div>
            <div class="value">{{ detail.endDate ||'--' }}</div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">相关业务条线：</div>
            <div class="value">{{ detail.bizLineNames ? detail.bizLineNames.join('、') : '--' }}</div>
          </div>
          <div class="flex g_plan_info_rowItem">
            <div class="label">对客问卷名称：</div>
            <div class="value">{{ detail.questionnaireName || '--' }}</div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">满意度工单责任归属：</div>
            <div class="value">{{ { 1: '客关', 16: '物业' }[detail.orderLine] || '--' }}</div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">客户名单：</div>
          </div>
        </div>
        <el-table :data="paginatedData" stripe row-key="sblBoardId">
          <el-table-column width="100" type="index" label="序号" />
          <el-table-column min-width="300" prop="ownerName" label="姓名">
            <template slot-scope="{ row }">{{ encryptName(row.ownerName) }}</template>
          </el-table-column>
          <el-table-column min-width="300" prop="mobile" label="手机号码">
            <template slot-scope="{ row }">{{ encryptMobile(row.mobile) }}</template>
          </el-table-column>
        </el-table>
        <div class="mt-20 margin-b-20">
          <pagination
            :total="tableData.length"
            :page.sync="pageNum"
            :limit.sync="pageSize"
            background
          />
        </div>
      </div>
    </el-card>
    <el-card shadow="never">
      <div class="g_card_bg">
        <div class="g-plan-title-icon">
          <div class="u-txt-icon" />
          <div class="u-txt-title">推送规则</div>
        </div>
      </div>
      <div class="g_plan_info">
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">是否推送短信：</div>
            <div class="value">
              {{ detail.sendSmsFlag ? '是' : '否' }}
            </div>
          </div>
          <div v-if="detail.sendSmsFlag" class="flex g_plan_info_rowItem">
            <div class="label">短信费用归属：</div>
            <div class="value">
              {{ detail.smsFeeName }}
            </div>
          </div>
        </div>
        <div style="margin-top: -20px;margin-bottom: 20px;padding-left: 0px;color: #999;" :span="24">备注：是否推送短信都会默认会通过小程序推送该问卷。</div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">推送规则：</div>
            <div class="value">
              <span v-if="detail.sendRuleCode === SPECIAL_PUSH_RULE.PUBLISH">仅在发布时推送一次</span>
              <span v-else-if="detail.sendRuleCode === SPECIAL_PUSH_RULE.POINT">在{{ detail.setTime }}推送一次</span>
              <span v-else>--</span>
            </div>
          </div>
        </div>
        <div class="g_plan_info_row1">
          <div class="label">推送通道：</div>
          <pushChannel ref="pushChannel" v-model="detail.channelTypes" disabled />
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">推送比例：</div>
            <div class="value">渠道推送比例{{ detail.surveyRate | formatterTable }}%</div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">失败补偿：</div>
            <div class="value">
              <span v-if="detail.retrySendFlag">对推送失败的消息，在{{ detail.retrySendDay | formatterTable }}天{{ detail.retrySendHour | formatterTable }}时{{ detail.retrySendMinute | formatterTable }}分后再推送一次</span>
              <span v-else>未开启</span>
            </div>
          </div>
        </div>
        <div class="g_plan_info_row">
          <div class="flex g_plan_info_rowItem">
            <div class="label">回收补偿：</div>
            <div class="value">
              <span v-if="detail.recycleSendFlag">当问卷发放{{ detail.recycleSendDay | formatterTable }}天{{ detail.recycleSendHour | formatterTable }}时{{ detail.recycleSendMinute | formatterTable }}分后，对未回收的问卷再推送一次</span>
              <span v-else>未开启</span>
            </div>
          </div>
        </div>
      </div>
    </el-card>
  </div>
</template>

<script>
import { getDetail, getListCustomer } from '@/api/plan/specialissues'
import { CHANNEL_TYPE, SPECIAL_PUSH_RULE } from '@/enum'
import pushChannel from '@/views/plan/components/pushChannel/index.vue'
export default {
  name: 'PlanSpecialIssuesInfo',
  components: { pushChannel },
  data() {
    return {
      SPECIAL_PUSH_RULE,
      detail: {
        channelTypes: [
          {
            title: '短信消息',
            channelTypeCode: CHANNEL_TYPE.MSG,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '公众号消息',
            channelTypeCode: CHANNEL_TYPE.PUBLIC,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '小程序订阅消息',
            channelTypeCode: CHANNEL_TYPE.MINI,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          }
        ]
      },
      pageNum: 1,
      pageSize: 10,
      tableData: []
    }
  },
  computed: {
    paginatedData() {
      const startIndex = (this.pageNum - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.tableData.slice(startIndex, endIndex)
    }
  },
  async created() {
    const { id } = this.$route.query
    if (id) {
      const load = this.$load()
      try {
        await this.getDetail(id)
        await this.getListCustomer(id)
      } catch (error) {
      } finally {
        load.close()
      }
    }
  },
  methods: {
    getDetail(uuid) {
      return getDetail({ uuid }).then(res => {
        const { recycleSendFlag, retrySendFlag, quesTelEffectFlag, channelTypes = [] } = res.data
        const obj = {
          ...res.data,
          recycleSendFlag: !!recycleSendFlag,
          retrySendFlag: !!retrySendFlag,
          quesTelEffectFlag: !!quesTelEffectFlag,
          channelTypes: this.detail.channelTypes.map(item => {
            const index = channelTypes.findIndex(_item => _item.channelTypeCode === item.channelTypeCode)
            if (index !== -1) {
              return {
                ...item,
                ...channelTypes[index],
                isChoose: true
              }
            } else {
              return {
                ...item
              }
            }
          })
        }
        this.detail = obj
      })
    },
    // 编辑时，获取选择的用户
    getListCustomer(uuid) {
      return getListCustomer({ uuid }).then(res => {
        this.tableData = res.data || []
        const arr = res.data || []
        this.tableData = arr.map(item => {
          return {
            houseId: item.houseId,
            ownerId: item.customerId,
            ownerName: item.customerName,
            mobile: item.customerMobile
          }
        })
      })
    },
    encryptName(str) {
      if (!str) return '--'
      // 姓名脱敏，两个字的也要兼容
      if(str.length === 2) return str[0] + '*'
      return str.replace(/^(.).+(.)$/, '$1*$2')
    },
    encryptMobile(str) {
      // 实现手机号脱敏
      if (!str) return '--'
      return str.replace(/(\d{3})\d{4}(\d{4})/, '$1****$2')
    }

  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/modules/plan';
</style>
