<template>
  <div class="m_plan_add">
    <el-form
      ref="planForm"
      :model="planForm"
      label-width="100px"
      label-position="left"
      @submit.native.prevent
    >
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">调研计划</div>
      </div>
      <div class="g-plan-form">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item ref="name" label="计划名称：" prop="name" :rules="[{ required: true, message: '请输入计划名称' }]">
              <el-input
                v-model="planForm.name"
                style="width: 100%"
                maxlength="50"
                placeholder="请输入计划名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesTemplateId" label="调研问卷：" prop="quesTemplateId" :rules="[{ required: true, message: '请选择调研问卷' }]">
              <el-select v-model="planForm.quesTemplateId" filterable clearable style="width: 100%">
                <el-option
                  v-for="opt in quesTemList"
                  :key="opt.quesTemplateId"
                  :label="opt.name"
                  :value="opt.quesTemplateId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesEffectFlag" label="填写有效期：" prop="quesEffectFlag" :rules="[{ required: true, message: '请选择有效期' }]">
              <el-row type="flex" align="middle" style="height: 32px;">
                <el-radio-group v-model="planForm.quesEffectFlag">
                  <el-radio :label="0">不设置</el-radio>
                  <el-radio :label="1">设置有效期</el-radio>
                </el-radio-group>
                <template v-if="planForm.quesEffectFlag === 1">
                  <el-form-item ref="quesEffectValue" label="" prop="quesEffectValue" :rules="[{ required: true, message: '请填写有效期', trigger: 'blur' }]" style="margin-bottom: 0!important; margin-left: 10px">
                    <el-input-number
                      v-model="planForm.quesEffectValue"
                      style="width: 110px;margin-right: 10px;"
                      :min="0"
                      :max="100"
                      :controls="false"
                      placeholder="请输入"
                      clearable
                    />
                  </el-form-item>
                  天
                </template>
              </el-row>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="endDate" label="结束时间：" prop="endDate" :rules="[{ required: true, message: '请选择结束时间' }]">
              <el-date-picker
                v-model="planForm.endDate"
                clearable
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择结束时间"
                style="width:100%"
                :picker-options="pickerOptions"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="bizLines" label="相关业务条线：" prop="bizLines" :rules="[{ required: true, type: 'array', message: '请选择相关业务条线' }]">
              <el-checkbox-group v-model="planForm.bizLines">
                <el-checkbox v-for="item in businessList" :key="item.dictValue" :disabled="item.disabled" :label="+item.dictValue">{{ item.dictName }}</el-checkbox>
              </el-checkbox-group>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="questionnaireName" label="对客问卷名称：" prop="questionnaireName" :rules="[{ required: true, message: '请输入对客问卷名称' }]">
              <el-input v-model="planForm.questionnaireName" maxlength="10" show-word-limit clearable />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-row>
              <el-col :span="12">
                <el-form-item ref="orderLine" label-width="140px" label="满意度工单责任归属：" prop="orderLine" :rules="[{ required: true, message: '请选择满意度工单责任归属' }]">
                  <el-select v-model="planForm.orderLine" style="width: 100%">
                    <el-option label="客关" :value="1" :disabled="!$checkPermission('DYJHGL_ZXWTDY_XZJH_GDGSKG')" />
                    <el-option label="物业" :value="16" :disabled="!$checkPermission('DYJHGL_ZXWTDY_XZJH_GDGSWY')" />
                  </el-select>
                </el-form-item>
              </el-col>
            </el-row>
          </el-col>
          <el-col :span="24">
            <el-form-item ref="customers" label="客户名单：" prop="customers" :rules="[{ required: true, type: 'array', message: '请选择客户', trigger: 'blur' }]">
              <div style="float: right" class="margin-b-20">
                <el-button type="primary" @click="delAll">批量删除</el-button>
                <el-button type="primary" @click="selectCustomerDialog = true">选择客户</el-button>
                <el-button type="primary" @click="importFile"><svg-icon icon-class="import" /> 导入</el-button>
                <el-button :loading="downLoading" type="primary" icon="el-icon-download" @click="downLoadFile">下载模版</el-button>
              </div>
              <el-table ref="tableData" :data="paginatedData" row-key="xh" stripe @selection-change="handleSelectionChange">
                <el-table-column
                  reserve-selection
                  type="selection"
                  width="50"
                />
                <el-table-column width="100" prop="xh" label="序号" />
                <el-table-column width="300" prop="ownerName" label="姓名" :formatter="$formatterTable" />
                <el-table-column width="" prop="mobile" label="手机号码" :formatter="$formatterTable" />
                <el-table-column
                  label="操作"
                  width="120"
                >
                  <template slot-scope="scope">
                    <span style="color: #F8716B;cursor: pointer" @click="delHandle(scope.$index)">删除</span>
                  </template>
                </el-table-column>
              </el-table>
              <pagination
                :total="tableTotal"
                :page.sync="pageNum"
                :limit.sync="pageSize"
                class="f-mg-t20"
              />
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="g-plan-title-icon mt45">
        <div class="u-txt-icon" />
        <div class="u-txt-title">推送规则</div>
      </div>
      <div class="g-rule-form">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label-width="126px" label="是否推送短信：" prop="sendSmsFlag" :rules="[{ required: true, message: '请选择是否推送短信', trigger: 'change' }]">
              <el-radio-group v-model="planForm.sendSmsFlag">
                <el-radio :label="1" :disabled="!hasSmsAuth">是</el-radio>
                <el-radio :label="0">否</el-radio>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col v-if="planForm.sendSmsFlag" :span="12">
            <el-form-item label-width="126px" label="短信费用归属：" prop="smsFeeId" :rules="[{ required: planForm.sendSmsFlag, message: '请选择短信费用归属', trigger: 'change' }]">
              <el-select v-model="planForm.smsFeeId" placeholder="请选择" clearable>
                <el-option v-for="item in smsOrgans" :key="item.value" :value="item.value" :label="item.label" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col style="margin-bottom: 10px;padding-left: 10px;color: #999;transform: translateY(-5px);" :span="24">备注：是否推送短信都会默认会通过小程序推送该问卷。</el-col>
          <el-col :span="24">
            <el-form-item
              prop="sendRuleCode"
              :rules="[{
                required: true, trigger: 'change', message: '请选择推送规则'
              }]"
              label="推送规则："
            >
              <el-radio-group v-model="planForm.sendRuleCode">
                <el-row type="flex" align="middle">
                  <el-radio :label="SPECIAL_PUSH_RULE.PUBLISH" style="margin-left: 10px">仅在发布时推送一次</el-radio>
                  <el-radio :label="SPECIAL_PUSH_RULE.POINT" class="u-label__hide" />
                  <el-form-item
                    prop="setTime"
                    :rules="[{
                      required: planForm.sendRuleCode === SPECIAL_PUSH_RULE.POINT, trigger: 'change', message: '请选择'
                    }]"
                    label=""
                    style="margin-bottom: 0!important;"
                  >
                    在<el-date-picker
                      v-model="planForm.setTime"
                      clearable
                      type="datetime"
                      format="yyyy-MM-dd hh:mm"
                      value-format="yyyy-MM-dd hh:mm"
                      placeholder="请选择结束时间"
                      style="margin: 0 10px"
                      :picker-options="pickerOptions"
                      popper-class="hidenNow"
                    />推送一次
                  </el-form-item>
                </el-row>
              </el-radio-group>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item
              prop="channelTypes"
              :rules="[{
                required: true, validator: validatePlanMsgContent, trigger: 'change'
              }]"
              label="推送通道："
            >
              <pushChannel ref="pushChannel" v-model="planForm.channelTypes" />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="surveyRate"
              label="推送比例："
              :rules="[{ required: true, message: '请输入推送比例', trigger: 'blur' }]"
            >
              <div class="errorTime" style="margin-left: 0">
                <el-form-item
                  ref="retryDay"
                  prop="retryDay"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.surveyRate"
                    style="width: 110px"
                    :min="0"
                    :max="100"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">%</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="retrySendFlag"
              label="失败补偿："
              :rules="[{ required: true, }]"
            >
              <el-switch
                v-model="planForm.retrySendFlag"
              />
            </el-form-item>
            <div v-if="planForm.retrySendFlag" class="errorTime">
              <div>对推送失败的消息，在</div>
              <el-form-item
                prop="retrySendDay"
                label=""
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="planForm.retrySendDay"
                  style="width: 110px"
                  :min="0"
                  :max="31"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-r-14 margin-l-10">天</span>
              <el-form-item
                prop="retrySendHour"
                label=""
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="planForm.retrySendHour"
                  style="width: 110px"
                  :min="0"
                  :max="24"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class=" margin-r-14 margin-l-10">时</span>
              <el-form-item
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
                prop="retrySendMinute"
                label=""
              >
                <el-input-number
                  v-model="planForm.retrySendMinute"
                  style="width: 110px"
                  :min="0"
                  :max="60"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">分钟后再推送一次</span>
            </div>
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="recycleSendFlag"
              label="回收补偿："
              :rules="[{ required: true, }]"
            >
              <el-switch
                v-model="planForm.recycleSendFlag"
              />
            </el-form-item>
            <div v-if="planForm.recycleSendFlag" class="errorTime">
              <div class="f-flex">
                当问卷发放
                <el-form-item
                  prop="recycleSendDay"
                  label=""
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="planForm.recycleSendDay"
                    style="width: 110px"
                    :min="0"
                    :max="31"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">天</span>
                <el-form-item
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                  prop="recycleSendHour"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.recycleSendHour"
                    style="width: 110px"
                    :min="0"
                    :max="24"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">时</span>
                <el-form-item
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                  prop="recycleSendMinute"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.recycleSendMinute"
                    style="width: 110px"
                    :min="0"
                    :max="60"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">分后，对未回收的问卷再推送一次</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="m-fixed__btm">
      <el-button type="primary" plain @click="save(0)">保存</el-button>
      <el-button type="primary" @click="save(1)">立即发布</el-button>
    </div>
    <customerListTable :visible.sync="selectCustomerDialog" :select-customers="tableData" @change="getVal" />
    <um-upload-file
      ref="upload"
      style="display: none;"
      accept="xlsx"
      base-url="/api/task/pc/spi-plan/importCustomer"
      :custom-success="customSuccess"
    />
  </div>
</template>

<script>
import { getQuesTemplateList } from '@/api/plan/satisfactionSurvey'
import { getDetail, updatePlan, addPlan, getListCustomer } from '@/api/plan/specialissues'
import pushChannel from '@/views/plan/components/pushChannel/index.vue'
import customerListTable from '@/views/plan/components/customerlist/customerListTable.vue'
import { CHANNEL_TYPE, SYS_DICT_CODE, TIME_UNIT, SPECIAL_PUSH_RULE, POINT_RULE } from '@/enum'
import { getSysDictList, getFileTemplate, getPointOption, getTreeStage, getUserBizline } from '@/api/common'
import { cloneDeep } from 'lodash'
import { flattenTree } from '@/utils'
let importLoad = null
export default {
  name: 'PlanSpecialIssuesAdd',
  components: { customerListTable, pushChannel },
  props: {},
  data() {
    return {
      SPECIAL_PUSH_RULE,
      tableTotal: 0,
      pageNum: 1,
      pageSize: 10,
      multipleSelection: [],
      selectCustomerDialog: false,
      pickerOptions: {
        disabledDate(time) {
          return time.getTime() < Date.now()
        }
      },
      planForm: {
        uuid: null,
        quesTemplateId: null, // 问卷id
        name: null, // 计划名称
        endDate: null, // 结束时间 yyyy-MM-dd
        surveyRate: undefined, // 推送比例
        quesEffectFlag: null, // 问卷有效期标识
        retrySendFlag: false, // 失败补偿标识
        questionnaireName: null, // 对客展示问卷名称
        orderLine: null, // 责任归属
        channelTypes: [ // 推送渠道
          {
            title: '短信消息',
            channelTypeCode: CHANNEL_TYPE.MSG,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '公众号消息',
            channelTypeCode: CHANNEL_TYPE.PUBLIC,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '小程序订阅消息',
            channelTypeCode: CHANNEL_TYPE.MINI,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          }
        ],
        recycleSendFlag: false, // 回收补偿标识
        retrySendDay: undefined, // 失败补偿天
        retrySendHour: undefined, // 失败补偿小时
        retrySendMinute: undefined, // 失败补偿分钟
        recycleSendDay: undefined, // 回收补偿天
        recycleSendHour: undefined, // 回收补偿小时
        recycleSendMinute: undefined, // 回收补偿分钟
        quesEffectValue: undefined, // 问卷有效期值
        quesEffectUnitCode: TIME_UNIT.DAY, // 问卷有效期单位编码
        customers: [], // 客户名单
        bizLines: [], // 业务条线
        sendRuleCode: null, // 推送规则编码
        sendSmsFlag: null, // 是否推送短信
        smsFeeId: null, // 短信归属id
        smsFeeName: null, // 短信归属名称
        setTime: null // 指定时间
      },
      quesTemList: [], // 调研问卷
      businessList: [], // 业务条线
      unitOption: [
        // {
        //   label: '分钟',
        //   value: TIME_UNIT.MINUTE
        // },
        // {
        //   label: '小时',
        //   value: TIME_UNIT.HOUR
        // },
        {
          label: '天',
          value: TIME_UNIT.DAY
        }
        // {
        //   label: '月',
        //   value: TIME_UNIT.MONTH
        // }
      ],
      person: {
        estimateUserNum: '',
        estimateRoomNum: ''
      },
      tableData: [],
      // tableData: [{ 'ownerId': '5c896d36db0d44cc9910da28a9560c22', 'ownerName': '\t王爽', 'mobile': '***********', 'certTypeName': '身份证', 'certNumber': '2202****0083', 'gender': 1, 'houses': [{ 'houseId': '1029A11000000000X1NL', 'houseFullName': '大连华发新城-二期-60栋-201', 'customerTypeName': '业主' }], 'xh': 1 }, { 'ownerId': 'de6f2092440b42c2adab093c3b935c41', 'ownerName': '\t申光善', 'mobile': '***********', 'certTypeName': '身份证', 'certNumber': '2101****5013', 'gender': 1, 'houses': [{ 'houseId': '1029A11000000001230T', 'houseFullName': '大连华发新城-二期-57栋-801', 'customerTypeName': '业主' }], 'xh': 2 }, { 'ownerId': '12d6952b2b2f4524b9d444307d03d28d', 'ownerName': '   李惠琴', 'mobile': '13318483063', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EEBA5D683D67EB65994', 'houseFullName': '珠海国际海岸花园14#地块-1分期-1栋-4805', 'customerTypeName': '同住人' }], 'xh': 3 }, { 'ownerId': '463838efea46426caf78cc8a62a9332f', 'ownerName': ' 张辉', 'mobile': '15920793027', 'certTypeName': '身份证', 'certNumber': '4201****4529', 'gender': 0, 'houses': [{ 'houseId': '1032A1100000000197WS', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-141栋-2503', 'customerTypeName': '业主' }], 'xh': 4 }, { 'ownerId': '9901d19d635f43aebbb98faffa4a3c17', 'ownerName': ' 李翠芬', 'mobile': '13508819830', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EEBB2B37C98C992B9C0', 'houseFullName': '昆明草海项目-1分期-8号楼-2406', 'customerTypeName': '同住人' }], 'xh': 5 }, { 'ownerId': 'f52ff6de6b1e4430b599134403421870', 'ownerName': ' 梁冰  ', 'mobile': '15633875001', 'certTypeName': '身份证', 'certNumber': '3710****9323', 'gender': 0, 'houses': [{ 'houseId': '1dbba93ed5555d42a9d88ecd7620d89b', 'houseFullName': '荣成华发樱花湖-一期-6栋-3101', 'customerTypeName': '业主' }], 'xh': 6 }, { 'ownerId': 'dc46c4fffd1b473b9008d6de408b427a', 'ownerName': ' 程倩', 'mobile': '62658988', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EEABEB24239CBCC1769', 'houseFullName': '珠海华发国际海岸A8-A8#地块-17栋-3311', 'customerTypeName': '同住人' }], 'xh': 7 }, { 'ownerId': '75b8f0f6f92942f0abfa0fc4ae49a544', 'ownerName': ' 苗成梁', 'mobile': '13631287296', 'certTypeName': '身份证', 'certNumber': '4128****3882', 'gender': 1, 'houses': [{ 'houseId': '1032A11000000002JVFN', 'houseFullName': '珠海华发蔚蓝堡（109-119栋）-一期-119栋-402', 'customerTypeName': '业主' }], 'xh': 8 }, { 'ownerId': '2b25f61904bf4fbe8f84eb3b9ef0e04b', 'ownerName': ' 谢军', 'mobile': '13825622195', 'certTypeName': '身份证', 'certNumber': '3601****1232', 'gender': 1, 'houses': [{ 'houseId': '1032A11000000002JV7M', 'houseFullName': '珠海华发蔚蓝堡（109-119栋）-一期-115栋-606', 'customerTypeName': '业主' }], 'xh': 9 }, { 'ownerId': 'f72464f34b204658bef2ab58676d2a28', 'ownerName': ' 雷嘉辉', 'mobile': '13355788299', 'certTypeName': '回乡证', 'certNumber': '505****8(4)', 'gender': 0, 'houses': [{ 'houseId': '1032A1100000000197XC', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-141栋-2001', 'customerTypeName': '业主' }], 'xh': 10 }, { 'ownerId': '1f3daa7f063d4e4ca945781b92e864b8', 'ownerName': '1', 'mobile': '15899398700', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EDB8DAD5E0227A2C1F4', 'houseFullName': '珠海西区华发水郡别墅及又一城-（华发又一城七期）-12栋-1001', 'customerTypeName': '同住人' }], 'xh': 11 }, { 'ownerId': 'b320020154d748149ad516c7e60417f5', 'ownerName': 'Amy', 'mobile': '14715012125', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EDB8FD01BAB6A53B6BC', 'houseFullName': '珠海国际海岸花园21#地块-1分期-1栋-3903', 'customerTypeName': '同住人' }], 'xh': 12 }, { 'ownerId': 'e53857396b254fefbd12a028ae821909', 'ownerName': 'ANITANG（林秀菁)', 'mobile': '13788951892', 'certTypeName': '其他', 'certNumber': 'A****85', 'gender': 1, 'houses': [{ 'houseId': '1032A1100000000H2BR8', 'houseFullName': '珠海华发峰景湾花园-一期-10栋-3203', 'customerTypeName': '业主' }], 'xh': 13 }, { 'ownerId': '1ed7e55431bc4c29861c4ba944785ef6', 'ownerName': 'Bill', 'mobile': '13928180042', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EDBAFA743EDD2635AAC', 'houseFullName': '珠海国际海岸花园20#地块-1分期-2栋-3404', 'customerTypeName': '同住人' }], 'xh': 14 }, { 'ownerId': 'c5a6ba93ab73499791093d5c2cac0dbc', 'ownerName': 'BOO JOO CHUAH', 'mobile': '60124087606', 'certTypeName': '其他', 'certNumber': 'A19****3415', 'gender': 1, 'houses': [{ 'houseId': '1032A11000000002MLVF', 'houseFullName': '珠海华发新城（六期：249-254栋）-六期-252栋-1803', 'customerTypeName': '业主' }], 'xh': 15 }, { 'ownerId': 'bb078aae7e15487eb9d357d2e5774a38', 'ownerName': 'CHOWKARINAJADEYOOKLING', 'mobile': '18620979693', 'certTypeName': '护照', 'certNumber': 'PE0****8233', 'gender': 0, 'houses': [{ 'houseId': 'cded60c45a59754b30598c054f906922', 'houseFullName': '广州华发中央公园-01分期-9号楼-1701', 'customerTypeName': '业主' }, { 'houseId': '005056AC57431EDA85997BCC320AAD7F', 'houseFullName': '广州华发中央公园-01分期-地下室--2734', 'customerTypeName': '业主' }], 'xh': 16 }, { 'ownerId': '4595349e687c4eba8d75f7187cae3ade', 'ownerName': 'DINGCHUAN', 'mobile': '18807182390', 'certTypeName': '护照', 'certNumber': 'PA5****1953', 'gender': 1, 'houses': [{ 'houseId': '005056AC57431EDA81A2A62EABC31333', 'houseFullName': '武汉华发中央首府-1分期-12号楼-1704', 'customerTypeName': '业主' }, { 'houseId': '005056AC57431EDCB9F5DA6F608F8D10', 'houseFullName': '武汉华发中央首府-1分期-地下室--1975', 'customerTypeName': '业主' }, { 'houseId': '005056AC57431EDCB9F5DA6F607FED10', 'houseFullName': '武汉华发中央首府-1分期-地下室--1850', 'customerTypeName': '业主' }], 'xh': 17 }, { 'ownerId': 'efe24a39708741b480ca023d5df3b939', 'ownerName': 'FU JUN（付军）', 'mobile': '13544936718', 'certTypeName': '回乡证', 'certNumber': '422****7731', 'gender': 0, 'houses': [{ 'houseId': '1032A1100000000182TM', 'houseFullName': '珠海华发世纪城（四期）-四期-162栋-2902', 'customerTypeName': '业主' }], 'xh': 18 }, { 'ownerId': 'ee0a2dd5ab414f6f96e2486819e23156', 'ownerName': 'JINJIAN', 'mobile': '13986225310', 'certTypeName': '护照', 'certNumber': 'A****92', 'gender': 0, 'houses': [{ 'houseId': '9d5f172b72845747ee78cea7134a078a', 'houseFullName': '武汉华发中城荟-二期-B9、T1、T2、T3栋T1号楼-2107', 'customerTypeName': '业主' }], 'xh': 19 }, { 'ownerId': 'aaacde5b521c49a5892ed6336e49deb9', 'ownerName': 'KIM YUNG KIL', 'mobile': '13701705994', 'certTypeName': '其他', 'certNumber': 'M17****1324', 'gender': 1, 'houses': [{ 'houseId': '1032A1100000000N1DXO', 'houseFullName': '珠海华发城建未来荟-一期-2栋-1811', 'customerTypeName': '业主' }, { 'houseId': '1032A1100000000PELTW', 'houseFullName': '珠海华发首府-二期-1栋-2804', 'customerTypeName': '业主' }], 'xh': 20 }, { 'ownerId': 'b5a43e1531444fd1b8110d4506f36a5f', 'ownerName': 'KUAN WAI TIM MAX(关伟添)', 'mobile': '13360608332', 'certTypeName': '其他', 'certNumber': 'S12****9251', 'gender': 1, 'houses': [{ 'houseId': '1032A11000000001978D', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-136栋-1503', 'customerTypeName': '业主' }, { 'houseId': '1032A110000000019654', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-133栋-2601', 'customerTypeName': '业主' }], 'xh': 21 }, { 'ownerId': '5a5edd09279b444381fc26d54b98f2d3', 'ownerName': 'kuan Wai Tim Max（关伟添）', 'mobile': '13809803683', 'certTypeName': '回乡证', 'certNumber': 'S12****925I', 'gender': 0, 'houses': [{ 'houseId': '1032A1100000000199U6', 'houseFullName': '珠海华发世纪城（一期二期三期）-三期-157栋-301', 'customerTypeName': '业主' }], 'xh': 22 }, { 'ownerId': '29f207dfc0f54bb4b0155d959d9aeddc', 'ownerName': 'LI QIONG(李琼)', 'mobile': '13068805883', 'certTypeName': '其他', 'certNumber': 'S70****2(B)', 'gender': 1, 'houses': [{ 'houseId': '1032A110000000019LBS', 'houseFullName': '珠海华发新城（一期/二期/三期/四期）-四期-134栋-2301', 'customerTypeName': '业主' }], 'xh': 23 }, { 'ownerId': 'e1caae854c3c427db3a4e03e82c3ec84', 'ownerName': 'Liu Jualing', 'mobile': '13901213824', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EDB8FCD27B8498FD040', 'houseFullName': '横琴金融岛30、32、36、39地块-36#地块-1栋-3101', 'customerTypeName': '同住人' }], 'xh': 24 }, { 'ownerId': 'a0afa83b720142369f99a2b4cf0df6e1', 'ownerName': 'LIUCHEN', 'mobile': '18771947132', 'certTypeName': '护照', 'certNumber': 'K04****821B', 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EDA819F7D0CC492E4C9', 'houseFullName': '武汉华发中央首府-1分期-7号楼-2301', 'customerTypeName': '业主' }], 'xh': 25 }, { 'ownerId': 'e9c108260db3483ba28010857db4fe6a', 'ownerName': 'MAKOWEI', 'mobile': '19000101745', 'certTypeName': '身份证', 'certNumber': '0****', 'gender': 0, 'houses': [{ 'houseId': '1032A110000000019MU3', 'houseFullName': '珠海华发新城（五期：170-188/189-248栋）-五期-175栋-1702', 'customerTypeName': '业主' }, { 'houseId': '1032A110000000019862', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-143栋-103', 'customerTypeName': '业主' }, { 'houseId': '1032A110000000019MWR', 'houseFullName': '珠海华发新城（五期：170-188/189-248栋）-五期-176栋-2501', 'customerTypeName': '业主' }], 'xh': 26 }, { 'ownerId': 'fbb5ae5dd6d246cb876b7d3e18c8ccd8', 'ownerName': 'Pyy', 'mobile': '13450780216', 'certTypeName': '身份证', 'certNumber': null, 'gender': 0, 'houses': [{ 'houseId': '005056AC57431EECA49447A57F04BFBB', 'houseFullName': '珠海华发城建四季半岛-四期25#-2栋-2904', 'customerTypeName': '同住人' }], 'xh': 27 }, { 'ownerId': '342f27b08ff6481899d0bcf04d6c5d7b', 'ownerName': 'SCHULTE(托比)', 'mobile': '15916315908', 'certTypeName': '其他', 'certNumber': '569****2616', 'gender': 1, 'houses': [{ 'houseId': '1032A1100000000197CC', 'houseFullName': '珠海华发世纪城（一期二期三期）-二期-137栋-2104', 'customerTypeName': '业主' }, { 'houseId': '1032A110000000019I31', 'houseFullName': '珠海华发新城（一期/二期/三期/四期）-三期-117栋-1201', 'customerTypeName': '业主' }], 'xh': 28 }, { 'ownerId': 'b2a0cd7af3114adfadab945cece9a884', 'ownerName': 'Silva', 'mobile': '8536280213', 'certTypeName': '其他', 'certNumber': 'XUN****0004', 'gender': 1, 'houses': [{ 'houseId': '1032A110000000019FOE', 'houseFullName': '珠海华发新城（一期/二期/三期/四期）-二期-74栋-1401', 'customerTypeName': '业主' }], 'xh': 29 }, { 'ownerId': '630388bd827f4e35bdd912efdc66023b', 'ownerName': 'SiminYu', 'mobile': '18807107747', 'certTypeName': '护照', 'certNumber': 'A****71', 'gender': 0, 'houses': [{ 'houseId': '241c29d14284244c8f4860f492d7fc6c', 'houseFullName': '武汉华发外滩首府-01分期-2#-4902', 'customerTypeName': '业主' }], 'xh': 30 }],
      downLoading: false,
      smsOrgans: [
        { label: '珠海大区', value: '1099006', authCode: ['DYJHGL_ZXWTDY_XZJH_ZHDX'] },
        { label: '股份本部', value: '1099008', authCode: ['DYJHGL_ZXWTDY_XZJH_GFDX'] },
        { label: '华南大区', value: '1099004', authCode: ['DYJHGL_ZXWTDY_XZJH_HNDX'] },
        { label: '华东大区', value: '1099001', authCode: ['DYJHGL_ZXWTDY_XZJH_HDDX'] },
        { label: '北方大区', value: '1099003', authCode: ['DYJHGL_ZXWTDY_XZJH_BFDX'] },
        { label: '物业', value: '2000000', authCode: ['DYJHGL_ZXWTDY_XZJH_WYDX'] }
      ]
    }
  },
  computed: {
    paginatedData() {
      const startIndex = (this.pageNum - 1) * this.pageSize
      const endIndex = startIndex + this.pageSize
      return this.tableData.slice(startIndex, endIndex)
    }
  },
  async created() {
    // 判断用户是否有选择推送短信权限
    if (this.$checkPermission(['DYJHGL_ZXWTDY_XZJH_FSDX'])) {
      this.hasSmsAuth = true
      this.planForm.sendSmsFlag = 1
    } else {
      this.hasSmsAuth = false
      this.planForm.sendSmsFlag = 0
    }
    this.getUserBizline()
    this.getQuesTemplateList()
    const { id } = this.$route.query
    const load = this.$load()
    try {
      // 编辑回显的时候需要处理下项目回显，因提交的选择项目数据经过特殊处理，故先加载这个接口
      await this.getTreeStage()
      if (id) {
        this.planForm.uuid = id
        await this.getDetail()
        await this.getListCustomer()
      }
    } catch (error) {

    } finally {
      load.close()
    }
  },
  mounted() {},
  methods: {
    // 获取用户的业务条线全线
    getUserBizline() {
      getUserBizline().then(res => {
        this.getSysDictList(res.data || [])
      })
    },
    getDetail() {
      return getDetail({ uuid: this.planForm.uuid }).then(res => {
        const { recycleSendFlag, retrySendFlag, quesTelEffectFlag, channelTypes = [] } = res.data
        const obj = {
          ...res.data,
          recycleSendFlag: !!recycleSendFlag,
          retrySendFlag: !!retrySendFlag,
          quesTelEffectFlag: !!quesTelEffectFlag,
          channelTypes: this.planForm.channelTypes.map(item => {
            const index = channelTypes.findIndex(_item => _item.channelTypeCode === item.channelTypeCode)
            if (index !== -1) {
              return {
                ...item,
                ...channelTypes[index],
                isChoose: true
              }
            } else {
              return {
                ...item
              }
            }
          })
        }
        this.planForm = obj
      })
    },
    getTreeStage() {
      return getTreeStage().then(res => {
        const arr = res.data || []
        const flatTree = flattenTree(arr)
        // 过滤掉短信权限，把有权限的code删除掉，留下的是没有权限的【卧推100公斤的健身大神产品提的】
        // 如果只有某个大区的权限，则别的大区就不能选
        // 如果只有某一个项目权限，则表示有这个大区权限
        const smsCodes = ['1099006', '1099004', '1099001', '1099003']
        flatTree.forEach(item => {
          try {
            smsCodes.forEach((_item, _index) => {
              if (item.codes.includes(_item) && !item.disabled) {
                smsCodes.splice(_index, 1)
                throw new Error('stop')
              }
            })
          } catch (error) {

          }
        })
        // 短信权限过滤，根据按钮权限和项目数据权限过滤
        this.smsOrgans = this.smsOrgans.filter(item => {
          return this.$checkPermission(item.authCode) && !smsCodes.includes(item.value)
        })
      })
    },
    // 编辑时，获取选择的用户
    getListCustomer() {
      return getListCustomer({ uuid: this.planForm.uuid }).then(res => {
        this.tableData = res.data || []
        const arr = res.data || []
        this.tableData = arr.map(item => {
          return {
            houseId: item.houseId,
            ownerId: item.customerId,
            ownerName: item.customerName,
            mobile: item.customerMobile
          }
        })
        console.log(this.tableData, '111')

        this.tableTotal = this.tableData.length
      })
    },
    // 获取业务条线字典值
    getSysDictList(authCode) {
      getSysDictList({ dictCode: SYS_DICT_CODE.BUSINESS }).then(res => {
        const arr = res.data || []
        arr.forEach(item => {
          item.disabled = !authCode.includes(+item.dictValue)
        })
        this.businessList = arr
      })
    },
    // 去重复，以系统用户为主导
    filterCustomer(arr) {
      // 如果选择的系统用户，存在tableData中，则直接覆盖
      const _tableData = cloneDeep(this.tableData)
      arr.forEach((item, _index) => {
        const index = _tableData.findIndex(_item => item.ownerName === _item.ownerName && item.mobile && item.mobile === _item.mobile)
        if (index !== -1) {
          _tableData[index] = item
        } else {
          _tableData.push(item)
        }
      })
      return _tableData
    },
    // 选择客户返回
    getVal(val) {
      this.tableData = this.filterCustomer(val).map((item, index) => {
        return {
          ...item,
          xh: index + 1
        }
      })
      this.tableTotal = this.tableData.length
    },
    // 客户选择
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    // 批量删除
    delAll() {
      console.log(this.multipleSelection, '==')
      if (this.multipleSelection && this.multipleSelection.length) {
        // this.multipleSelection.
        const toRemove = this.multipleSelection.map(item => item.xh)
        const filteredData = this.tableData.filter(item => !toRemove.includes(item.xh))
        this.tableData = filteredData
        this.tableTotal = this.tableData.length
        this.$refs.tableData.clearSelection()
      } else {
        this.$message.warning('请选择客户！')
      }
    },
    // 导入
    importFile() {
      this.$refs.upload.$el.querySelector('.el-upload').click()
    },
    customSuccess({ type, data }) {
      if (type === 'start') {
        importLoad = this.$load()
        return
      }
      importLoad && importLoad.close()
      this.$message.success('操作成功，共导入' + data.data.length + '个客户')
      const arr = []
      data.data.forEach(item => {
        if (this.tableData.findIndex(_item => item.name === _item.ownerName && item.mobile === _item.mobile) === -1) {
          arr.push({
            ownerName: item.name,
            mobile: item.mobile
          })
        }
      })
      this.tableData = this.tableData.concat(arr).map((item, index) => {
        return {
          ...item,
          xh: index + 1
        }
      })
      this.tableTotal = this.tableData.length
    },
    // 下载
    downLoadFile() {
      this.downLoading = true
      getFileTemplate({ code: 2000061 }).then(res => {
        window.open(res.data)
      }).finally(() => {
        this.downLoading = false
      })
    },
    // 切换table
    delHandle(index) {
      this.tableData.splice(index, 1)
    },
    validatePlanMsgContent(rule, value, callback) {
      const length = value.length
      const checkLength = value.filter(i => i.isChoose).length
      if (checkLength === 0 || length === 0) {
        const text = '请选择推送通道'
        return callback(text)
      }
      const data1 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.PUBLIC) // 公众号
      const data2 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.MINI) // 小程序
      const data3 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.MSG) // 短信
      if (data3.isChoose && !data3.firstContent) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      if (data1.isChoose && (!data1.firstContent || !data1.secondContent)) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      if (data2.isChoose && (!data2.firstContent || !data2.secondContent)) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      callback()
    },
    // 获取调研问卷
    async getQuesTemplateList() {
      try {
        const { data } = await getPointOption()
        const item = data.filter(item => item.ruleCode === POINT_RULE.SPECIAL)[0]
        item && getQuesTemplateList({ pointId: item.pointId }).then(res => {
          this.quesTemList = res.data || []
        })
      } catch (error) {

      }
    },
    save(type) {
      this.planForm.customers = this.tableData.map(item => {
        return {
          houseId: Array.isArray(item.houses) && item.houses.length ? item.houses[0].houseId : item.houseId,
          customerId: item.ownerId,
          customerName: item.ownerName,
          customerMobile: item.mobile
        }
      })
      this.$refs.planForm.validate(valid => {
        if (!valid) return
        const load = this.$load()
        const API = this.planForm.uuid ? updatePlan : addPlan
        API({
          ...this.planForm,
          retrySendFlag: +this.planForm.retrySendFlag,
          recycleSendFlag: +this.planForm.recycleSendFlag,
          quesTelEffectFlag: +this.planForm.quesTelEffectFlag,
          publishFlag: type,
          smsFeeName: this.smsOrgans.find(item => item.value === this.planForm.smsFeeId)?.label,
          channelTypes: this.planForm.channelTypes.filter(item => item.isChoose)
        }).then(res => {
          this.$alert('操作成功', '提示', {
            type: 'success',
            showClose: false
          }).then(() => {
            this.$router.back()
          })
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style>
.hidenNow .el-picker-panel__footer .el-button--text {
  display: none;
}
</style>

<style scoped lang="scss">
@import '../../../../styles/modules/plan';
.tips {
  font-size: 12px;
  color: #F8716B;
  font-weight: 400;
}
.icon {
  font-size: 12px;
  color: #F8716B;
  margin-right: 4px;
}
.btn-tips {
  font-size: 12px;
}
.errorTime{
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-left: 18px;
  ::v-deep .el-form-item__content{
    margin-left: 8px !important;
  }
}
.u-label__hide {
  margin-right: 10px!important;
  ::v-deep {
    .el-radio__label {
      display: none;
    }
  }
}
::v-deep {
  .el-picker-panel__footer .el-button--text {
    display: none;
  }
}
</style>
