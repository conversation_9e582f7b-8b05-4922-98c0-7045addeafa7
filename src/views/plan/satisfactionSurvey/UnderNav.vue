<template>
  <div class="unernav">
    <div v-for="(item, index) in tableData" :key="index" class="items">
      <div class="main-top">
        <div class="top">
          <div>
            <div class="f-flex-ac">
              <span class="title" :title="item.name" @click="$router.push('/plan/satisfactionSurvey/info?id=' + item.uuid)">{{ item.name | formatterTable }}</span>
              <div class="planNo">{{ item.planCode | formatterTable }}</div>
            </div>
            <div class="times">
              数据更新时间：{{ item.updateTime | formatterTable }}
            </div>
          </div>
          <div class="m-btns">
            <el-button v-if="$checkPermission(['DYJHGL_MYDDY_WJLL'])" type="primary" plain @click="$router.push('/plan/satisfactionSurvey/preview?id=' + item.quesTemplateId + (item.pointId ? '&pointId=' + item.pointId : ''))">问卷浏览</el-button>
            <el-button v-if="$checkPermission(['DYJHGL_MYDDY_DYXQ'])" type="primary" plain @click="$router.push('/plan/satisfactionSurvey/surveyInfo?id=' + item.planId + '&uuid=' + item.uuid+'&pageType=satisfactionSurvey'+'&planName='+item.name+'&pointName='+item.pointName)">
              调研详情
            </el-button>
            <el-button v-if="$checkPermission(['DYJHGL_MYDDY_TJFX'])" type="primary" plain @click="$router.push('/plan/satisfactionSurvey/analyse?id=' + item.planId + '&uuid=' + item.uuid+'&pageType=satisfactionSurvey'+'&planName='+item.name+'&pointName='+item.pointName)">统计分析</el-button>
            <el-dropdown v-if="$checkPermission(['DYJHGL_MYDDY_JSDY', 'DYJHGL_MYDDY_QXFB']) && statusCode === PLAN_STATUS.IN_PROGRESS" @command="handleCommand($event, item)">
              <el-button type="primary" plain class="u-rotate">
                更多<span class="um_iconfont rotate-icon">&#xe6a1;</span>
              </el-button>
              <el-dropdown-menu slot="dropdown">
                <el-dropdown-item v-if="$checkPermission(['DYJHGL_MYDDY_JSDY'])" command="1">
                  <span class="um_iconfont margin-icon">&#xe61c;</span>结束调研
                </el-dropdown-item>
                <el-dropdown-item v-if="$checkPermission(['DYJHGL_MYDDY_QXFB'])" command="2">
                  <span class="um_iconfont margin-icon">&#xe6b3;</span>取消发布
                </el-dropdown-item>
                <!-- <el-dropdown-item v-if="statusCode === PLAN_STATUS.COMPLANT" command="5">
                  <span class="um_iconfont margin-icon danger">&#xe678; 删除</span>
                </el-dropdown-item> -->
              </el-dropdown-menu>
            </el-dropdown>
          </div>
        </div>
      </div>
      <div class="nav">
        <el-descriptions>
          <el-descriptions-item label="调研范围"><span class="row_1" :title="item.planScope">{{ item.scopeNames ? item.scopeNames.join('、') : '全部' }}</span></el-descriptions-item>
          <el-descriptions-item label="调研问卷">{{ item.quesTemplateName | formatterTable }}</el-descriptions-item>
          <el-descriptions-item label="触点">{{ item.pointName | formatterTable }}</el-descriptions-item>
          <el-descriptions-item label="结束时间">{{ item.endTime | formatterTable }}</el-descriptions-item>
          <el-descriptions-item label="发布时间">{{ item.publishTime | formatterTable }}</el-descriptions-item>
          <el-descriptions-item label="发布人">{{ item.creator | formatterTable }}</el-descriptions-item>
        </el-descriptions>
      </div>
      <div class="main-content f-flex-ac f-flex-acjcsb">
        <div class="con-left f-flex-acjcsb">
          <div class="con-item">
            <div class="con-number">
              {{ item.frameNum | formatterTable }}
            </div>
            <div class="con-title">
              样框量
            </div>
          </div>
          <div class="con-item">
            <div class="con-number">
              {{ item.totalSendNum | formatterTable }}
            </div>
            <div class="con-title">
              样本量
            </div>
          </div>
          <div class="con-item">
            <div class="con-number">
              {{ item.recycleRate | formatterTable }}%
            </div>
            <div class="con-title">
              回收率
            </div>
          </div>
        </div>
        <div class="con-right f-flex">
          <div class="yangben-left f-flex-acjcsb">
            <div class="yangben1">
              <div class="number">{{ item.onlineSendTotalNum | formatterTable }}</div>
              <div class="title">网络样本</div>
            </div>
            <div class="yangben2">
              <div class="number">{{ item.callSendTotalNum | formatterTable }}</div>
              <div class="title">电话样本</div>
            </div>
          </div>
          <div class="yangben-right">
            <div class="min-item">
              <div class="imgShow f-flex-ac">
                <img class="imgs" src="~@/assets/images/duanxin.png" alt="">
                <span>短信</span>
              </div>
              <div class="f-flex-acjcsb">
                <div>
                  <div class="min-number">{{ item.smsSendSuccessNum | formatterTable }}</div>
                  <div class="min-title">推送成功</div>
                </div>
                <div>
                  <div class="min-number">{{ item.smsSendFailNum | formatterTable }}</div>
                  <div class="min-title">推送失败</div>
                </div>
              </div>
            </div>
            <div class="min-item">
              <div class="imgShow f-flex-ac">
                <img class="imgs" src="~@/assets/images/xcx.png" alt="">
                <span>小程序</span>
              </div>
              <div class="f-flex-acjcsb">
                <div>
                  <div class="min-number">{{ item.appletSendSuccessNum | formatterTable }}</div>
                  <div class="min-title">推送成功</div>
                </div>
                <div>
                  <div class="min-number">{{ item.appletSendFailNum | formatterTable }}</div>
                  <div class="min-title">推送失败</div>
                </div>
              </div>
            </div>
            <div class="min-item">
              <div class="imgShow f-flex-ac">
                <img class="imgs" src="~@/assets/images/weixin.png" alt="">
                <span>公众号</span>
              </div>
              <div class="f-flex-acjcsb">
                <div>
                  <div class="min-number">{{ item.mpSendSuccessNum | formatterTable }}</div>
                  <div class="min-title">推送成功</div>
                </div>
                <div>
                  <div class="min-number">{{ item.mpSendFailNum | formatterTable }}</div>
                  <div class="min-title">推送失败</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
    <UmEmpty v-if="!tableData.length" />
    <pagination
      :total="tableTotal1"
      :page.sync="searchForm1.page.pageNum"
      :limit.sync="searchForm1.page.pageSize"
      @pagination="getList1('page')"
    />
  </div>
</template>

<script>
import { pageList, unPublishPlan, finishPlan } from '@/api/plan/satisfactionSurvey'
import { pageReportList } from '@/api/plan/common'
import UmEmpty from '@/components/UmEmpty'
import getList1 from '@/mixins/getList1'
import { PLAN_STATUS } from '@/enum'
export default {
  name: 'UnderNav',
  components: { UmEmpty },
  mixins: [getList1],
  props: {
    params: {
      type: Object,
      default: () => { }
    },
    statusCode: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {
      listApi1: pageList,
      PLAN_STATUS,
      items: {
        headline: '交付后满意度调查'
      },
      tableData: [],
      loading: false
    }
  },
  watch: {
    params: {
      immediate: true,
      deep: true,
      handler(v) {
        if (!v) { return }
        this.searchForm1 = {
          ...v
        }
      }
    },
    loading: {
      immediate: true,
      handler(v) {
        this.$parent.tableLoading1 = v
      }
    }
  },
  methods: {
    beforeApiCallBack1() {
      this.loading = true
    },
    tableCallBack1({ list, stateCount }) {
      this.tableData = []
      this.$parent.tableCallBack1({ stateCount })
      const ids = list.map(item => item.planId)
      if (!ids.length) {
        this.loading = false
        return
      }
      this.tableData = list
      pageReportList({ planIdList: ids }).then(res => {
        const arr = res.data || []
        arr.forEach(item => {
          const index = this.tableData.findIndex(_item => _item.planId === item.planId)
          if (index !== -1) {
            this.$set(this.tableData, index, {
              ...this.tableData[index],
              ...item
            })
          }
        })
      }).finally(() => {
        this.loading = false
      })
    },
    handleCommand(command, item) {
      if (command === '1') {
        this.$confirm('是否确定结束调研?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then((_) => {
          finishPlan({ uuid: item.uuid }).then((res) => {
            this.$message.success('操作成功')
            this.getList1()
          })
        }).catch((_) => {
        })
      }
      if (command === '2') {
        this.$confirm('是否确定取消发布?', '提示', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        }).then((_) => {
          unPublishPlan({ uuid: item.uuid }).then((res) => {
            this.$message.success('操作成功')
            this.getList1()
          })
        }).catch((_) => {
        })
      }
      if (command === '5') {
        this.$parent.deletePlan(item, () => {
          this.getList1()
        })
      }
    }
  }
}
</script>

<style>
.custom-alert {
  width: 430px;
}
</style>

<style lang="scss" scoped>
.items {
  padding: 20px;
  border-radius: 8px;
  border: 1px solid #F0F3F5;
  margin-top: 20px;
  .main-top {
    height: 50px;
    border-bottom: 1px dashed #D8DCE6;
  }
  .top {
    height: 32px;
    display: flex;
    justify-content: space-between;
    padding-left: 12px;
    border-left: 4px solid #005DCF;
    .times {
      color: #999999;
      font-size: 12px;
      margin-top: 2px;
    }
    .title {
      font-size: 16px;
      font-family: PingFangSC-Medium, PingFang SC;
      font-weight: 500;
      color: $--color-primary;
      cursor: pointer;
      text-overflow: ellipsis;
      white-space: nowrap;
      overflow: hidden;
      margin-right: 8px;
    }
    .planNo {
      padding: 2px 6px;
      height: 16px;
      background: #E5EEFA;
      border-radius: 4px;
      text-align: center;
      color: #005DCF;
      line-height: 12px;
      font-size: 12px;
    }
  }
  .m-btns {
      float: right;
      display: flex;
      justify-content: space-between;
      flex-shrink: 0;

      .el-button {
        text-align: center;
        width: 100px;
      }
      .el-dropdown {
        margin-left: 20px;
      }
    }

  .nav {
    margin-top: 20px;
    // display: flex;
    // justify-content: space-between;
    // width: 765px;
    // .nav-list {
    //   width: 225px;
    // }

  }
  .main-content {
    .con-left, .con-right {
      display: flex;
      align-items: center;
      background: #F5F7FA;
      border-radius: 8px;
      margin-right: 20px;
      height: 90px;
      width: 30%;
      justify-content: space-around;
      .con-item {
        min-width: 80px;
        .con-number {
          font-size: 26px;
          font-weight: 700;
          text-align: center;
          color: #333;
          margin-bottom: 10px;
          font-family: Akrobat-Bold;
        }
        .con-title {
          font-size: 14px;
          font-weight: 400;
          color: #333;
          text-align: center;
        }
      }
      //.con-item:nth-child(2) {
      //  margin: 0 20px;
      //}
    }
    .con-right {
      // flex: 1;
      // padding: 0 40px;
      padding-right: 0;
      margin-right: 0;
      height: 90px;
      width: calc(70% - 20px);
      .yangben-left {
        padding: 20px 0;
        display: flex;
        flex: 1;
        justify-content: center;
        .yangben1, .yangben2 {
          flex: 1;
          align-items: center;
          justify-content: center;
          max-width: 150px;
          .number {
            font-size: 26px;
            font-weight: 700;
            text-align: center;
            color: #333;
            margin-bottom: 10px;
            font-family: Akrobat-Bold;
          }
          .title {
            font-size: 14px;
            font-weight: 400;
            color: #333;
            text-align: center;
          }
        }
        .yangben1 {
          border-right: 1px dashed #D8DCE6;
        }
      }
      .yangben-right {
        display: flex;
        justify-content: space-between;
        padding: 10px;
        align-items: center;
        .min-item {
          padding: 22px 14px 12px 14px;
          height: 100%;
          width: 150px;
          min-width: 144px;
          background: #FFFFFF;
          border: 1px solid rgba(229,238,250,1);
          border-radius: 8px;
          text-align: center;
          position: relative;
          .imgShow {
            position: absolute;
            left: 0;
            top: 0;
            width: 68px;
            height: 16px;
            background: #E5EEFA;
            border-radius: 8px 0px 8px 0px;
            color: #005DCF;
            padding: 0 8px;
            font-size: 12px;
            .imgs {
              width: 12px;
              height: 12px;
              margin-right: 2px;
            }
          }
          .min-number {
            font-size: 18px;
            color: #333333;
            text-align: center;
            line-height: 20px;
            font-weight: 700;
            margin-bottom: 8px;
          }
          .min-title {
            font-size: 12px;
            color: #666666;
            text-align: center;
            font-weight: 400;
          }
        }
        .min-item:nth-child(2) {
          margin: 0 8px;
        }
      }
    }
  }

}

.u-rotate:hover {
  .rotate-icon {
    transform: rotate(180deg);
  }
}

.rotate-icon {
  margin-left: 4px;
  transition: all linear .3s;
  display: inline-block;
}

.margin-icon {
  margin-right: 4px;
}

::v-deep {
  .el-descriptions-item__content {
    font-weight: 400;
    font-size: 14px;
    color: #333333
  }
  .row_1{
     display:inline-block;
    }
  .el-descriptions-item__content {
    -webkit-line-clamp: 2;
    display: -webkit-box;
    -webkit-box-orient: vertical;
    overflow: hidden;
    text-overflow: ellipsis;
  }
  .el-descriptions-item__label {
    color: #999;
    font-weight: 400;
    font-size: 14px;
  }

  .el-descriptions-item__content {
    margin-right: 20px;
  }
}
</style>

