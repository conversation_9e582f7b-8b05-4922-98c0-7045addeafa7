<template>
  <div class="m_plan_info">
    <el-form label-width="98px" class="detailForm">
      <el-card shadow="never">
        <div class="g_card_bg">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">调研计划</div>
          </div>
        </div>
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="12">
            <el-form-item label="计划名称：">
              <span>{{ detail.name || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研触点：">
              <span>{{ detail.pointName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="调研问卷：">
              <span>{{ detail.quesTemplateName || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="开始时间：">
              <span>{{ detail.startDate || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="结束时间：">
              <span>{{ detail.endDate || '--' }}</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="考核方式：">
              <span v-if="detail.examineFlag !== undefined">
                {{ detail.examineFlag === 1 ? '计入考核' : '不计入考核' }}
                {{ detail.examineFlag === 1 && `(${detail.examineNames.join('、')})` || '' }}
              </span>
              <span v-else>--</span>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="网调有效期：">
              <span>{{ detail.quesEffectValue || '--' }} {{ detail.quesEffectUnitName }}</span>
            </el-form-item>
          </el-col>
          <template v-if="detail.quesTelEffectFlag">
            <el-col :span="12">
              <el-form-item label="电访开始时间：">
                <span>调研结束前{{ detail.telRelativeEndDay | formatterTable }}天</span>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item label="电访有效期：">
                <span>{{ detail.quesTelEffectValue || '--' }} {{ detail.quesTelEffectUnitName }}</span>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="12">
            <el-form-item label="调研范围：">
              <span v-if="detail.surveyScopeCode === PLAN_RANGE.ALL">全部</span>
              <span v-else>{{ detail.scopeNames ? detail.scopeNames.join('、') : '--' }}</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
      <el-card shadow="never">
        <div class="g_card_bg">
          <div class="g-plan-title-icon">
            <div class="u-txt-icon" />
            <div class="u-txt-title">推送规则</div>
          </div>
        </div>
        <el-row :gutter="20" style="padding: 20px 20px 0 20px">
          <el-col :span="24">
            <el-form-item label="推送通道：">
              <pushChannel ref="pushChannel" v-model="detail.channelTypes" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="推送比例：">
              <span>渠道推送比例{{ detail.surveyRate | formatterTable }}%</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="失败补偿：">
              <span v-if="detail.retrySendFlag">对推送失败的消息，在{{ detail.retrySendDay | formatterTable }}天{{ detail.retrySendHour | formatterTable }}时{{ detail.retrySendMinute | formatterTable }}分后再推送一次</span>
              <span v-else>未开启</span>
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="回收补偿：">
              <span v-if="detail.recycleSendFlag">当问卷发放{{ detail.recycleSendDay | formatterTable }}天{{ detail.recycleSendHour | formatterTable }}时{{ detail.recycleSendMinute | formatterTable }}分后，对未回收的问卷再推送一次</span>
              <span v-else>未开启</span>
            </el-form-item>
          </el-col>
        </el-row>
      </el-card>
    </el-form>
  </div>
</template>

<script>
import { getDetail } from '@/api/plan/satisfactionSurvey'
import { CHANNEL_TYPE, PLAN_RANGE } from '@/enum'
import pushChannel from '@/views/plan/components/pushChannel/index.vue'
export default {
  name: 'PlanSatisfactionInfo',
  components: { pushChannel },
  data() {
    return {
      PLAN_RANGE,
      detail: {
        channelTypes: [
          {
            title: '短信消息',
            channelTypeCode: CHANNEL_TYPE.MSG,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '公众号消息',
            channelTypeCode: CHANNEL_TYPE.PUBLIC,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '小程序订阅消息',
            channelTypeCode: CHANNEL_TYPE.MINI,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          }
        ]
      }
    }
  },
  created() {
    const { id } = this.$route.query
    this.getDetail(id)
  },
  methods: {
    getDetail(uuid) {
      const load = this.$load()
      getDetail({ uuid }).then(res => {
        const { recycleSendFlag, retrySendFlag, quesTelEffectFlag, channelTypes = [] } = res.data
        const obj = {
          ...res.data,
          recycleSendFlag: !!recycleSendFlag,
          retrySendFlag: !!retrySendFlag,
          quesTelEffectFlag: !!quesTelEffectFlag,
          channelTypes: this.detail.channelTypes.map(item => {
            const index = channelTypes.findIndex(_item => _item.channelTypeCode === item.channelTypeCode)
            if (index !== -1) {
              return {
                ...item,
                ...channelTypes[index],
                isChoose: true
              }
            } else {
              return {
                ...item
              }
            }
          })
        }

        this.detail = obj
      }).finally(() => load.close())
    }

  }
}
</script>

<style scoped lang="scss">
@import '~@/styles/modules/plan';

</style>
