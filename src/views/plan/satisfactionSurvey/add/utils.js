import { flattenTree } from '@/utils'
import { HOUSE_TYPE } from '@/enum'
// 处理城市选择数据

// 逻辑如下
// 如果一个【项目】下所有【分期】都选中，那么传给后台的是项目id，分期id不传
// 如果一个【城市】下所有【项目】都选中，那么传给后台的是城市id，项目和分期id都不传
// 如果一个【区域】下所有【城市】都选中，那么传给后台的是区域id，城市、项目、分期id都不传

/**
 * 将子级全部选中的节点，子级的checked 设置为flase
 * @param {Object} node el-select-tree 的树节点数据
 */
export function getMaxRangeCheckedData(nodes) {
  if (!Array.isArray(nodes) || !nodes.length) return []
  nodes.forEach(item => {
    const { children } = item
    if (Array.isArray(children) && children.length) {
      getMaxRangeCheckedData(children)
      if (children.every(_item => _item.checked)) {
        children.forEach(_item => {
          _item.checked = false
        })
      }
    }
  })
}

/**
 * 获取过滤后的选中的数据
 * @param {Object} nodes
 * @return {Array} checkedNodes
 */
export function getCheckedNodes(nodes) {
  return flattenTree(nodes).filter(item => item.checked)
}

/**
 * 将最终处理的节点数组处理提交给后台【组织架构相关处理】
 * @param {Array} nodes
 * @returns {Array<Object>} ProjDtos
 */
export function formatProjDtos(nodes) {
  return nodes.map(item => {
    if (item.level === 1) { // 区域
      return {
        areaCompanyId: item.value,
        cityCompanyId: null,
        projectId: null,
        stageId: null
      }
    }
    if (item.level === 2) { // 城市
      return {
        areaCompanyId: item.parent?.value,
        cityCompanyId: item.value,
        projectId: null,
        stageId: null
      }
    }
    if (item.level === 3) { // 项目
      return {
        areaCompanyId: item.parent?.parent?.value,
        cityCompanyId: item.parent?.value,
        projectId: item.value,
        stageId: null
      }
    }
    if (item.level === 4) { // 分期
      return {
        areaCompanyId: item.parent?.parent?.parent?.value,
        cityCompanyId: item.parent?.parent?.value,
        projectId: item?.parent.value,
        stageId: item.value
      }
    }
  })
}

/**
 * 将最终处理的节点数组处理提交给后台【楼栋单元相关】
 * @param {Array} nodes
 * @returns {Array<Object>} ProjDtos
 */
export function formatHouse(nodes) {
  return nodes.map(item => {
    if (item.level === 1) { // 楼栋
      return {
        houseScopeCode: HOUSE_TYPE.BUILDING,
        buildingId: item.value,
        unitId: null,
        houseId: null
      }
    }
    if (item.level === 2) { // 单元
      return {
        houseScopeCode: HOUSE_TYPE.UNIT,
        buildingId: item.parent?.value,
        unitId: item.value,
        houseId: null
      }
    }
    if (item.level === 3) { // 房间
      return {
        houseScopeCode: HOUSE_TYPE.ROOM,
        buildingId: item.parent?.parent?.value,
        unitId: item.parent?.value,
        houseId: item.value
      }
    }
  })
}

/**
 * 根据当前选中节点，把字节点也给选中，用于回显示
 * @param {Array} ids  后台返回的选中id
 * @param {Array} nodes
 */
export function computedCheckedNodes(ids, nodes) {
  const flatArr = flattenTree(nodes).filter(item => {
    return ids.includes(item.code)
  })
  return flattenTree(flatArr).map(item => item.code)
}

