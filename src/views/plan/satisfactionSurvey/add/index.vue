<template>
  <div class="m_plan_add">
    <el-form
      ref="planForm"
      :model="planForm"
      label-width="126px"
      label-position="left"
      @submit.native.prevent
    >
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">调研计划</div>
      </div>
      <div class="g-plan-form">
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item ref="name" label="计划名称：" prop="name" :rules="[{ required: true, message: '请输入计划名称' }]">
              <el-input
                v-model="planForm.name"
                style="width: 100%"
                maxlength="20"
                placeholder="请输入计划名称"
                clearable
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="pointId" label="调研触点：" prop="pointId" :rules="[{ required: true, message: '请选择调研触点' }]">
              <UmBusScene v-model="planForm.pointId" :all="false" :need-special="false" placeholder="请选择调研触点" :disabled="isGenerateQues" @nodeChange="sceneNodeChange" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesTemplateId" label="调研问卷：" prop="quesTemplateId" :rules="[{ required: true, message: '请选择调研问卷' }]">
              <el-select v-model="planForm.quesTemplateId" v-loading="quesTempLoading" filterable clearable style="width: 100%" :disabled="!planForm.pointId || isGenerateQues">
                <el-option
                  v-for="opt in quesTemList"
                  :key="opt.quesTemplateId"
                  :label="opt.name"
                  :value="opt.quesTemplateId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="startDate" label="开始时间：" prop="startDate" :rules="[{ required: true, message: '请选择开始时间' }]">
              <el-date-picker
                v-model="planForm.startDate"
                :disabled="isGenerateQues"
                clearable
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择开始时间"
                style="width:100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="endDate" label="结束时间：" prop="endDate" :rules="[{ required: true, message: '请选择结束时间' }]">
              <el-date-picker
                v-model="planForm.endDate"
                :disabled="isGenerateQues"
                clearable
                type="date"
                format="yyyy-MM-dd"
                value-format="yyyy-MM-dd"
                placeholder="请选择结束时间"
                style="width:100%"
                :picker-options="{
                  disabledDate: (date) => {
                    return new Date() >= date
                  },
                }"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="examineFlag" label="考核方式：" prop="examineFlag" :rules="[{ required: true, message: '请选择考核方式' }]">
              <el-radio-group v-model="planForm.examineFlag">
                <el-row type="flex" align="middle">
                  <el-radio :label="1" style="margin-right: 10px" :disabled="showPointConfig">计入考核</el-radio>
                  <el-form-item v-if="planForm.examineFlag === 1" ref="examineCodes" prop="examineCodes" style="margin-bottom: 0!important;" :rules="[{ required: true, type: 'array', message: '请选择考核方式' }]">
                    <el-select
                      v-model="planForm.examineCodes"
                      multiple
                      collapse-tags
                      filterable
                      clearable
                      style="width: 100%;"
                    >
                      <el-option
                        v-for="opt in examineOption"
                        :key="opt.value"
                        :label="opt.label"
                        :value="opt.value"
                      />
                    </el-select>
                  </el-form-item>
                  <el-radio :label="0" style="margin-left: 10px">不计入考核</el-radio>
                </el-row>
              </el-radio-group>
              <div v-show="showPointConfig" class="u-tip" :class="{ 'mt-10': planForm.examineFlag === 1 }"><i class="el-icon-warning-outline icon" /><span class="tips">未配置 触点类型，不能计入考核</span> <el-button type="text" class="btn-tips">去配置</el-button></div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesEffectValue" label="网络问卷有效期：" prop="quesEffectValue" :rules="[{ required: true, message: '请输入网络问卷有效期', trigger: 'blur' }]">
              <div class="f-flex-acjcsb">
                <el-input-number v-model="planForm.quesEffectValue" :controls="false" :min="0" :max="30" :precision="0" placeholder="请输入" style="flex: 1;margin-right: 10px" />
                <el-select v-model="planForm.quesEffectUnitCode" filterable style="width: 100px">
                  <el-option
                    v-for="opt in unitOption"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item ref="quesTelEffectFlag" label="电话回访：" prop="quesTelEffectFlag" :rules="[{ required: true }]">
              <el-switch v-model="planForm.quesTelEffectFlag" />
            </el-form-item>
          </el-col>
          <template v-if="planForm.quesTelEffectFlag">
            <el-col :span="12">
              <el-form-item ref="telRelativeEndDay" label="电话回访开始日期：" prop="telRelativeEndDay" :rules="[{ required: true, message: '请输入电话回访开始日期', trigger: 'blur' }]">
                <div class="f-flex-acjcsb">
                  调研结束前 <el-input-number v-model="planForm.telRelativeEndDay" :controls="false" :min="1" :max="31" :precision="0" placeholder="请输入" style="flex: 1;margin: 0 10px" /> 日
                </div>
                <div class="u-tip"><i class="el-icon-warning-outline icon" /><span class="tips">该开始时间后生成回访名单及开始计算电话调研问卷有效期</span></div>
              </el-form-item>
            </el-col>
            <el-col :span="12">
              <el-form-item ref="quesTelEffectValue" label="电话问卷有效期：" prop="quesTelEffectValue" :rules="[{ required: true, message: '请输入电话问卷有效期', trigger: 'blur' }]">
                <el-row type="flex" middle="center">
                  <el-input-number v-model="planForm.quesTelEffectValue" placeholder="请输入" :controls="false" :min="1" :max="30" :precision="0" style="flex: 1;margin-right: 10px" />
                  <el-select v-model="planForm.quesTelEffectUnitCode" filterable style="width: 100px;">
                    <el-option
                      v-for="opt in unitOption"
                      :key="opt.value"
                      :label="opt.label"
                      :value="opt.value"
                    />
                  </el-select>
                </el-row>
                <div class="u-tip"><i class="el-icon-warning-outline icon" /><span class="tips">该开始时间后生成回访名单及开始计算电话调研问卷有效期</span></div>
              </el-form-item>
            </el-col>
          </template>
          <el-col :span="24">
            <el-form-item ref="surveyScopeCode" label="调研范围：" prop="surveyScopeCode" :rules="[{ required: true, message: '请选择调研范围' }]">
              <el-row type="flex" align="middle" style="height: 32px;">
                <el-radio-group v-model="planForm.surveyScopeCode">
                  <el-radio :label="PLAN_RANGE.ALL" :disabled="!hasOrganAuth">全部</el-radio>
                  <el-radio :label="PLAN_RANGE.PROJECT">指定项目</el-radio>
                </el-radio-group>
                <el-form-item v-if="planForm.surveyScopeCode === PLAN_RANGE.PROJECT" ref="projDtos" label="" prop="projDtos" style="margin-bottom: 0!important;margin-left: 10px;flex: 1" :rules="[{ required: true, type: 'array', message: '请选择指定项目' }]">
                  <el-select-tree
                    ref="elSelectTree"
                    v-model="planForm.projDtos"
                    is-leaf
                    style="width: 100%"
                    placeholder="请选择"
                    :options="organList"
                    clearable
                    filterable
                    :show-all-levels="true"
                    :check-strictly="false"
                    collapse-tags
                    :default-props="{
                      multiple: true,
                      label: 'name',
                      value: 'code',
                    }"
                  />
                </el-form-item>
              </el-row>
            </el-form-item>
          </el-col>
        </el-row>
      </div>
      <div class="g-plan-title-icon mt45">
        <div class="u-txt-icon" />
        <div class="u-txt-title">推送规则</div>
      </div>
      <div class="g-rule-form">
        <el-row :gutter="30">
          <el-col :span="24">
            <el-form-item
              prop="channelTypes"
              :rules="[{
                required: true, validator: validatePlanMsgContent, trigger: 'change'
              }]"
              label="推送通道："
            >
              <pushChannel ref="pushChannel" v-model="planForm.channelTypes" />
            </el-form-item>
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="surveyRate"
              label="推送比例："
              :rules="[{ required: true, message: '请输入推送比例', trigger: 'blur' }]"
            >
              <div class="errorTime" style="margin-left: 0">
                <el-form-item
                  ref="retryDay"
                  prop="retryDay"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.surveyRate"
                    style="width: 110px"
                    :min="0"
                    :max="100"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">%</span>
              </div>
            </el-form-item>
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="retrySendFlag"
              label="失败补偿："
              :rules="[{ required: true, }]"
            >
              <el-switch
                v-model="planForm.retrySendFlag"
              />
            </el-form-item>
            <div v-if="planForm.retrySendFlag" class="errorTime">
              <div>对推送失败的消息，在</div>
              <el-form-item
                prop="retrySendDay"
                label=""
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="planForm.retrySendDay"
                  style="width: 110px"
                  :min="0"
                  :max="31"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-r-14 margin-l-10">天</span>
              <el-form-item
                prop="retrySendHour"
                label=""
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
              >
                <el-input-number
                  v-model="planForm.retrySendHour"
                  style="width: 110px"
                  :min="0"
                  :max="24"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class=" margin-r-14 margin-l-10">时</span>
              <el-form-item
                :rules="[{ required: planForm.retrySendFlag, message: '请输入', trigger: 'blur' }]"
                prop="retrySendMinute"
                label=""
              >
                <el-input-number
                  v-model="planForm.retrySendMinute"
                  style="width: 110px"
                  :min="0"
                  :max="60"
                  :precision="0"
                  :controls="false"
                  placeholder="请输入"
                  clearable
                />
              </el-form-item>
              <span class="margin-l-10">分钟后再推送一次</span>
            </div>
            <div />
          </el-col>
          <el-col :span="24" class="flex">
            <el-form-item
              prop="recycleSendFlag"
              label="回收补偿："
              :rules="[{ required: true, }]"
            >
              <el-switch
                v-model="planForm.recycleSendFlag"
              />
            </el-form-item>
            <div v-if="planForm.recycleSendFlag" class="errorTime">
              <div class="f-flex">
                当问卷发放
                <el-form-item
                  prop="recycleSendDay"
                  label=""
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                >
                  <el-input-number
                    v-model="planForm.recycleSendDay"
                    style="width: 110px"
                    :min="0"
                    :max="31"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">天</span>
                <el-form-item
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                  prop="recycleSendHour"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.recycleSendHour"
                    style="width: 110px"
                    :min="0"
                    :max="24"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">时</span>
                <el-form-item
                  :rules="[{ required: planForm.recycleSendFlag, message: '请输入', trigger: 'blur' }]"
                  prop="recycleSendMinute"
                  label=""
                >
                  <el-input-number
                    v-model="planForm.recycleSendMinute"
                    style="width: 110px"
                    :min="0"
                    :max="60"
                    :precision="0"
                    :controls="false"
                    placeholder="请输入"
                    clearable
                  />
                </el-form-item>
                <span class="margin-l-10">分后，对未回收的问卷再推送一次</span>
              </div>
            </div>
          </el-col>
        </el-row>
      </div>
    </el-form>
    <div class="m-fixed__btm">
      <el-button type="primary" plain @click="save(0)">保存</el-button>
      <el-button type="primary" @click="save(1)">立即发布</el-button>
    </div>
  </div>
</template>

<script>
import {
  getQuesTemplateList, addPlan, updatePlan, getDetail, hasQues
} from '@/api/plan/satisfactionSurvey'
import pushChannel from '@/views/plan/components/pushChannel/index.vue'
import { TIME_UNIT, PLAN_RANGE, EXAMINE_TYPE, CHANNEL_TYPE } from '@/enum'
import { gufenAuth, getTreeStage } from '@/api/common'
import { getMaxRangeCheckedData, getCheckedNodes, formatProjDtos, computedCheckedNodes } from './utils'
import { cloneDeep } from 'lodash'
export default {
  name: 'PlanSatisfactionSurveyAdd',
  components: { pushChannel },
  data() {
    return {
      PLAN_RANGE,
      examineOption: [
        {
          label: '月度',
          value: EXAMINE_TYPE.MONTH
        },
        {
          label: '季度',
          value: EXAMINE_TYPE.QUARTER
        },
        {
          label: '半年度',
          value: EXAMINE_TYPE.HALF_YEAR
        },
        {
          label: '年度',
          value: EXAMINE_TYPE.YEAR
        }
      ],
      unitOption: [
        // {
        //   label: '分钟',
        //   value: TIME_UNIT.MINUTE
        // },
        // {
        //   label: '小时',
        //   value: TIME_UNIT.HOUR
        // },
        {
          label: '天',
          value: TIME_UNIT.DAY
        }
        // {
        //   label: '月',
        //   value: TIME_UNIT.MONTH
        // }
      ],
      planForm: {
        uuid: null,
        quesTemplateId: null, // 问卷id
        name: null, // 计划名称
        endDate: null, // 结束日期
        surveyRate: undefined, // 推送比例
        retrySendFlag: false, // 是否失败补偿
        channelTypes: [
          {
            title: '短信消息',
            channelTypeCode: CHANNEL_TYPE.MSG,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '公众号消息',
            channelTypeCode: CHANNEL_TYPE.PUBLIC,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          },
          {
            title: '小程序订阅消息',
            channelTypeCode: CHANNEL_TYPE.MINI,
            isChoose: false,
            firstContent: '',
            secondContent: '',
            thirdContent: ''
          }
        ], // 推送渠道
        recycleSendFlag: false, // 回收补偿标识
        pointId: null, // 触点id
        surveyScopeCode: null, // 调研范围类型编码
        startDate: null, // 开始日期
        telRelativeEndDay: undefined, // 电话回访开始天数
        examineFlag: null, // 是否开启考核
        quesTelEffectFlag: false, // 是否开启电话回访
        quesTelEffectUnitCode: TIME_UNIT.DAY, // 问卷电话有效期单位编码
        quesTelEffectValue: undefined, // 问卷电话有效期值
        quesEffectValue: undefined, // 问卷有效期值
        quesEffectUnitCode: TIME_UNIT.DAY, // 问卷有效期单位编码
        retrySendDay: undefined, // 失败补偿天
        retrySendHour: undefined, // 失败补偿小时
        retrySendMinute: undefined, // 失败补偿分钟
        recycleSendDay: undefined, // 回收补偿天
        recycleSendHour: undefined, // 回收补偿小时
        recycleSendMinute: undefined, // 回收补偿分钟
        examineCodes: [], // 考核类型编码
        projDtos: []// 选择范围
      },
      quesTemList: [], // 调研问卷下拉
      quesTempLoading: false,
      organList: [], // 区域城市项目分期下拉
      showPointConfig: false, // 是否展示配置触点类型 操作栏
      hasOrganAuth: false, // 是否有华发股份数据权限
      isGenerateQues: false // 该计划是否生成了问卷
    }
  },
  async created() {
    const { id } = this.$route.query

    const load = this.$load()
    try {
      // 编辑回显的时候需要处理下项目回显，因提交的选择项目数据经过特殊处理，故先加载这个接口
      await this.getTreeStage()
      await this.gufenAuth()
      if (id) {
        this.planForm.uuid = id
        this.getDetail()
        this.hasQues()
      }
    } catch (error) {

    } finally {
      load.close()
    }
  },
  methods: {
    getDetail() {
      const load = this.$load()
      getDetail({ uuid: this.planForm.uuid }).then(res => {
        const { recycleSendFlag, retrySendFlag, quesTelEffectFlag, scopeIds, channelTypes = [] } = res.data
        const obj = {
          ...res.data,
          recycleSendFlag: !!recycleSendFlag,
          retrySendFlag: !!retrySendFlag,
          quesTelEffectFlag: !!quesTelEffectFlag,
          pointId: '' + res.data.pointId,
          quesTelEffectUnitCode: TIME_UNIT.DAY,
          projDtos: scopeIds ? computedCheckedNodes(scopeIds, this.organList) : [],
          channelTypes: this.planForm.channelTypes.map(item => {
            const index = channelTypes.findIndex(_item => _item.channelTypeCode === item.channelTypeCode)
            if (index !== -1) {
              return {
                ...item,
                ...channelTypes[index],
                isChoose: true
              }
            } else {
              return {
                ...item
              }
            }
          })
        }

        this.planForm = obj
        this.getQuesTemplateList()
      }).finally(() => load.close())
    },
    /**
     * 取消发布之后再编辑 如果已经产生问卷
     * 触点不能改
     * 问卷不能改
     * 时间范围所属年份不能改
     */
    hasQues() {
      hasQues({ uuid: this.planForm.uuid }).then(res => {
        this.isGenerateQues = res.data
      })
    },
    // 获取项目数据
    getTreeStage() {
      return getTreeStage().then(res => {
        this.organList = res.data || []
      })
    },
    // 获取是否有华发股份数据权限，没有权限【调研范围】不能选择全部
    gufenAuth() {
      return gufenAuth().then(res => {
        this.hasOrganAuth = res.data
      })
    },
    // 调研触点change事件
    sceneNodeChange(data) {
      this.quesTemList = []
      this.planForm.quesTemplateId = null
      if (!data.length) {
        this.showPointConfig = false
        return
      }
      this.getQuesTemplateList()
      // 没有触点类型id，表示没有配置触点类型，不能计入考核
      this.showPointConfig = !data[0].data.pointTypeId
    },
    validatePlanMsgContent(rule, value, callback) {
      const length = value.length
      const checkLength = value.filter(i => i.isChoose).length
      if (checkLength === 0 || length === 0) {
        const text = '请选择推送通道'
        return callback(text)
      }
      const data1 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.PUBLIC) // 公众号
      const data2 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.MINI) // 小程序
      const data3 = value.find(i => i.channelTypeCode === CHANNEL_TYPE.MSG) // 短信
      if (data3.isChoose && !data3.firstContent) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      if (data1.isChoose && (!data1.firstContent || !data1.secondContent)) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      if (data2.isChoose && (!data2.firstContent || !data2.secondContent)) {
        // eslint-disable-next-line standard/no-callback-literal
        return callback('推送通道内容不能为空')
      }
      callback()
    },
    // 获取调研问卷
    getQuesTemplateList() {
      this.quesTempLoading = true
      getQuesTemplateList({ pointId: this.planForm.pointId }).then(res => {
        this.quesTemList = res.data || []
      }).finally(() => { this.quesTempLoading = false })
    },
    save(type) {
      this.$refs.planForm.validate(valid => {
        if (!valid) return
        if (new Date(this.planForm.startDate) > new Date(this.planForm.endDate)) {
          this.$message.warning('开始时间不得大于结束时间，请重新选择！')
          return
        }
        const arr = cloneDeep(this.$refs.elSelectTree?.$children[0].getNodes()) || []
        getMaxRangeCheckedData(arr) // 递归处理数据
        const load = this.$load()
        const API = this.planForm.uuid ? updatePlan : addPlan
        API({
          ...this.planForm,
          retrySendFlag: +this.planForm.retrySendFlag,
          recycleSendFlag: +this.planForm.recycleSendFlag,
          quesTelEffectFlag: +this.planForm.quesTelEffectFlag,
          publishFlag: type,
          projDtos: formatProjDtos(getCheckedNodes(arr)),
          channelTypes: this.planForm.channelTypes.filter(item => item.isChoose)
        }).then(res => {
          this.$alert(res.msg || '操作成功', '提示', {
            type: 'success',
            showClose: false
          }).then(() => {
            this.$router.back()
          })
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style scoped lang="scss">
@import '../../../../styles/modules/plan';
.u-tip {
  line-height: 24px;
  font-size: 12px;
  font-weight: 400;
  &.mt-10 {
    margin-top: 10px;
  }
}
.icon {
  font-size: 12px;
  color: #F8716B;
  margin-right: 4px;
}
.btn-tips {
  font-size: 12px;
}
.errorTime{
  display: flex;
  height: 32px;
  line-height: 32px;
  margin-left: 18px;
  ::v-deep .el-form-item__content{
    margin-left: 8px !important;
  }
}
</style>
