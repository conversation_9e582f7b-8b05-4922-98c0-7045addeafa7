
<template>
  <Watch ref="Preview" />
</template>

<script>
import Watch from '../../components/watch'
export default {
  name: 'PlanSatisfactionSurveyWatch',
  components: { Watch },
  data() {
    return {
    }
  },
  mounted() {
    // const { id } = this.$route.query
    // if (id) {
    //   this.$refs.SurveyInfo.searchForm1.uuid = id
    //   this.$refs.SurveyInfo.getTopData()
    //   this.$refs.SurveyInfo.getList1()
    // }
  }
}
</script>

