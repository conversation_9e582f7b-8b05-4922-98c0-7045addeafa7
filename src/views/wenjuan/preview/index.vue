<template>
  <div class="um-sq g-container">
    <div class="um-sq_header">
      <div class="m-left">
        <div class="u-back" @click="goBack">
          <i class="um_iconfont">&#xe64b;</i>
        </div>
        <div class="u-title">{{ title || $route.query.title || '问卷调查' }}</div>
      </div>
      <div class="m-center fix-width">
        <div class="m-button_tab">
          <div class="u-tab" :class="{ on: platform === 'mobile' }" @click="platform = 'mobile'"><svg-icon icon-class="mobile" />手机预览</div>
          <div class="u-tab" :class="{ on: platform === 'pc' }" @click="platform = 'pc'"><svg-icon icon-class="pc" />电脑预览</div>
        </div>
      </div>
      <div class="m-right flex-end">
        <el-button type="primary" @click="goBack">退出预览</el-button>
      </div>
    </div>
    <div class="um-sq_main card hide-scrollbar" :class="[platform]">
      <div class="u-tips">
        <i class="um_iconfont">&#xe6a0;</i>
        <span>当前为预览页面，答案不被记录，可以验证跳转逻辑。</span>
      </div>

      <!-- <div class="g-preview" :class="[platform, computedConfig.config.pageMode && 'onPage' ]"> -->
      <div class="g-content">
        <div class="g-preview" :class="[platform]">
          <div class="g-preview__head">
            <div class="m-status">
              <span>{{ currentTime }}</span>
              <div class="um_iconfont"><svg-icon icon-class="mobile-xh" /><i>&#xe61e;</i><i>&#xe61d;</i></div>
            </div>
            <div class="m-title">问卷调查</div>
          </div>
          <iframe class="g-preview__webview" :src="previewUrl" />
        </div>
        <div v-show="platform === 'mobile'" class="g-preview__qrcode">
          <div class="qrcode"><canvas ref="canvas" class="canvas" /></div>
          <span>手机扫描预览</span>
        </div>
      </div>
    </div>
  </div>
</template>
<script>
import { htmlToText } from '@/components/UmSQ/src/utils'
const QRCode = require('qrcode')
let timer = null
export default {
  name: 'WenjuanManagePreview',
  props: {
    previewUuid: {
      type: [String, Number],
      default: null
    },
    title: {
      type: String,
      default: '问卷调查'
    },
    typeCode: {
      type: Number,
      default: 1 // 1 调研问卷 2 深访问卷
    }
  },
  data() {
    return {
      tab: 0,
      htmlToText,
      platform: 'mobile',
      previewUrl: '',
      currentTime: null
    }
  },
  watch: {
    previewUuid: {
      immediate: true,
      handler(v) {
        let url = process.env.NODE_ENV !== 'localdev' ? location.origin + '/client/' : 'http://localhost:9528/'
        if (+this.$route.query.typeCode === 2 || this.typeCode === 2) {
          url += 'depth/'
        }
        this.previewUrl = url + '?uuid=' + (this.previewUuid || this.$route.query.id) + '&mode=1' + (this.previewUuid ? '&isEditPreview=1' : '') + (this.$route.query.pointId ? '&pointId=' + this.$route.query.pointId : '') // mode 1 预览
      }
    }
  },
  mounted() {
    this.generateQrcode()
    this.getCurrentDate()
    timer = setInterval(() => {
      this.getCurrentDate()
    }, 1000)
  },
  beforeDestroy() {
    clearInterval(timer)
  },
  methods: {
    getCurrentDate() {
      const date = new Date()
      this.currentTime = date.getHours() + ':' + date.getMinutes()
    },
    goBack() {
      this.previewUuid ? this.$emit('goConfig') : this.$router.back()
    },
    generateQrcode() {
      QRCode.toCanvas(this.$refs.canvas, this.previewUrl, function(error) {
        if (error) console.error(error)
        console.log('success!')
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  margin-top: -20px;
  margin-left: -20px;
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  &.isEditor {
    margin-top: 0px;
    margin-left: 0px;
    width: 100%;
    height: 100%;
  }
}
</style>
