<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form>
        <el-row :gutter="20">
          <el-col :span="8">
            <el-form-item label="问卷编号：" label-width="80px" class="mb-0">
              <el-input v-model="searchForm1.templateCode" placeholder="请输入问卷编号" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="问卷名称：" label-width="80px" class="mb-0">
              <el-input v-model="searchForm1.name" placeholder="请输入问卷名称" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="适用场景：" label-width="80px" class="mb-0">
              <UmBusScene v-model="searchForm1.pointId" need-special need-deep placeholder="请选择适用场景" />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="更新时间：" label-width="80px" class="mb-0">
              <el-date-picker
                v-model="timeRange"
                style="width: 100%"
                type="datetimerange"
                value-format="yyyy-MM-dd HH:mm"
                format="yyyy-MM-dd HH:mm"
                range-separator="至"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8">
            <el-form-item label="问卷类型：" label-width="80px" class="mb-0">
              <el-select v-model="searchForm1.typeCode" placeholder="请选择问卷类型" clearable style="width: 100%">
                <el-option label="调研问卷" :value="1" />
                <el-option label="深访问卷" :value="2" />
              </el-select>
            </el-form-item>
          </el-col>
          <el-form-item label="" class="fr mb-0 mr-10">
            <el-button :loading="tableLoading1" type="primary" icon="el-icon-search" @click="getList1">搜索</el-button>
          </el-form-item>
        </el-row>
      </el-form>
    </el-card>

    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row type="flex" justify="space-between" class="mb-20">
        <CommonTabs :tabs="tabs" @changeTab="changeTab" />
        <el-button
          v-if="$checkPermission(['DYWJGL_DYWJGL_XZWJ'])"
          type="primary"
          icon="el-icon-circle-plus-outline"
          @click="addDialogVisible = true"
        >
          新增问卷
        </el-button>
      </el-row>
      <um-table-full
        :data="tableData1"
        scroll
      >
        <el-table-column
          prop="xh"
          label="序号"
          width="60"
        />
        <el-table-column
          prop="templateCode"
          label="问卷编号"
          width="200px"
        />
        <el-table-column
          prop="name"
          label="问卷名称"
          min-width="300px"
          show-overflow-tooltip
        />
        <el-table-column
          prop="typeCode"
          label="问卷类型"
          min-width="80px"
        >
          <template slot-scope="{ row }">{{ row.typeCode === 2 ? '深访问卷' : '调研问卷' }}</template>
        </el-table-column>
        <el-table-column
          prop="pointNameSte"
          label="适用场景"
          min-width="300px"
          show-overflow-tooltip
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="createUser"
          label="创建人"
          width="160px"
        />
        <el-table-column
          prop="updateTime"
          label="更新时间"
          width="200px"
        />
        <el-table-column
          width="270px"
          label="操作"
          fixed="right"
        >
          <template slot-scope="{ row }">
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_SYCJ']) && searchForm1.statusCode === QUES_TEMPLATE_STATUS.DRAFT && row.typeCode !== 2" type="text" @click="openPoint(row)">适用场景</el-button>
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_BJ']) && searchForm1.statusCode === QUES_TEMPLATE_STATUS.DRAFT" type="text" @click="edit(row)">编辑</el-button>
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_YL'])" type="text" @click="preview(row.quesTemplateId, false, row.typeCode)">预览</el-button>
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_FZ'])" type="text" @click="copyQuesTemplate(row.quesTemplateId, row.name)">复制</el-button>
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_SC']) && searchForm1.statusCode === QUES_TEMPLATE_STATUS.DRAFT" type="text" @click="del(row.quesTemplateId, row.name)"><span class="danger">删除</span></el-button>
            <el-button v-if="$checkPermission(['DYWJGL_DYWJGL_ZF']) && searchForm1.statusCode === QUES_TEMPLATE_STATUS.PUBLISH" type="text" @click="draft(row.quesTemplateId, row.name)"><span class="danger">作废</span></el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList1('page')"
      />
    </div>
    <EditScene :visible.sync="dialogVisible" :point-ids="pointInfo.pointIdList" @success="saveTemplatePoint" />
    <el-dialog title="选择问卷类型" :visible.sync="addDialogVisible" width="250px">
      <el-radio-group v-model="typeCode">
        <el-radio :label="1">调研问卷</el-radio>
        <el-radio :label="2">深访问卷</el-radio>
      </el-radio-group>
      <template #footer>
        <el-button @click="addDialogVisible = false">取消</el-button>
        <el-button type="primary" @click="addConfirm">确定</el-button>
      </template>
    </el-dialog>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs'
import { getTemplatePage, deleteQuesTemplate, voidedQuesTemplate, copyQuesTemplate, saveTemplatePoint } from '@/api/wenjuan'
import { QUES_TEMPLATE_STATUS } from '@/enum'
import EditScene from './components/EditScene'
import getPointList from '@/mixins/getPointList'
export default {
  name: 'WenjuanManage',
  components: { CommonTabs, EditScene },
  mixins: [getList1, getPointList],
  props: {},
  data() {
    return {
      listApi1: getTemplatePage,
      QUES_TEMPLATE_STATUS,
      searchForm1: {
        templateCode: null,
        name: null,
        pointId: null,
        updateStartTime: null,
        updateEndTime: null,
        typeCode: null,
        statusCode: QUES_TEMPLATE_STATUS.DRAFT
      },
      timeRange: null,
      dialogVisible: false,
      pointInfo: {
        quesTemplateId: null,
        pointIdList: []
      }, // 适用场景相关
      tabs: [
        {
          label: '草稿',
          value: QUES_TEMPLATE_STATUS.DRAFT,
          count: 0,
          key: 'draft'
        },
        {
          label: '已发布',
          value: QUES_TEMPLATE_STATUS.PUBLISH,
          count: 0,
          key: 'publish'
        },
        {
          label: '已作废',
          value: QUES_TEMPLATE_STATUS.DUTY,
          count: 0,
          key: 'cancellation'
        }
      ],
      addDialogVisible: false,
      typeCode: 1
    }
  },
  watch: {
    timeRange(v) {
      if (v) {
        this.searchForm1.updateStartTime = v[0]
        this.searchForm1.updateEndTime = v[1]
      } else {
        this.searchForm1.updateStartTime = null
        this.searchForm1.updateEndTime = null
      }
    }
  },
  activated() {
    this.getList1()
  },
  created() {
  },
  methods: {
    tableCallBack1() {
      this.tabs.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    addConfirm() {
      this.$router.push(this.typeCode === 1 ? '/wenjuan/add?typeCode=1' : '/wenjuan/sfAdd?typeCode=2')
      this.$nextTick(() => {
        this.addDialogVisible = false
        this.typeCode = 1
      })
    },
    // 删除
    del(id, name) {
      this.$confirm('是否确定删除问卷：' + name, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            deleteQuesTemplate({ quesTemplateId: id }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    // 作废
    draft(id, name) {
      this.$confirm('是否确定作废问卷：' + name, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            voidedQuesTemplate({ quesTemplateId: id }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    // 编辑
    edit({ quesTemplateId, pointIdList, typeCode }) {
      typeCode === 1 ? this.$router.push('/wenjuan/edit?typeCode=1&id=' + quesTemplateId + (pointIdList ? '&hasPoint=1' : '')) : this.$router.push('/wenjuan/sfEdit?typeCode=2&id=' + quesTemplateId)
    },
    copyQuesTemplate(id, name) {
      this.$confirm('是否确定复制问卷：' + name, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            copyQuesTemplate({ quesTemplateId: id }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    preview(id, isTemplate, typeCode) {
      this.$router.push(`/wenjuan/preview?${isTemplate ? 'templateId' : 'id'}=${id}&typeCode=${typeCode}`)
    },
    openPoint(data) {
      this.pointInfo.pointIdList = data.pointIdList || []
      this.pointInfo.quesTemplateId = data.quesTemplateId
      this.dialogVisible = true
    },
    saveTemplatePoint(pointIdList) {
      const load = this.$load()
      saveTemplatePoint({ ...this.pointInfo, pointIdList }).then(res => {
        this.$message.success(res.msg)
        this.dialogVisible = false
        this.getList1()
      }).finally(() => load.close())
    }
  }
}
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}
.mb-0 {
  margin-bottom: 0;
}
.mr-10 {
  margin-right: 10px;
}
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  &__btm {
    flex: 1;
    ::v-deep {
      .el-card__body {
        height: 100%;
        display: flex;
        flex-direction: column;
        .el-table {
          flex: 1;
        }
      }
    }
  }
}

</style>
