<template>
  <el-dialog
    title="适用场景"
    :visible.sync="dialogVisible"
    width="600px"
    :close-on-click-modal="false"
  >
    <el-checkbox-group v-model="pointIdList">
      <um-table-full class="um-table__noheader" :data="pointOptions">
        <el-table-column prop="label" label="" width="120" />
        <el-table-column label="">
          <template slot-scope="{row}">
            <el-checkbox v-for="item in row.children" :key="item.label" :label="item.value">{{ item.label }}</el-checkbox>
          </template>
        </el-table-column>
      </um-table-full>
    </el-checkbox-group>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="saveTemplatePoint">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import getPointList from '@/mixins/getPointList'
export default {
  name: 'EditScene',
  mixins: [getPointList],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    pointIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      dialogVisible: false,
      pointIdList: [],
      needSpecial: true
    }
  },
  watch: {
    async visible(v) {
      try {
        if (v && !this.pointOptions.length) {
          await this.getPointOption(false)
        } else {
          // this.pointIdList = []
        }
        this.dialogVisible = v
      } catch (error) {

      }
    },
    dialogVisible(v) {
      this.$emit('update:visible', v)
    },
    pointIds: {
      deep: true,
      handler(v) {
        this.pointIdList = [...v]
      }
    }
  },
  mounted() {

  },
  methods: {
    saveTemplatePoint() {
      if (!this.pointIdList.length) {
        this.$message.warning('请选择适用场景！')
        return
      }
      this.$emit('success', this.pointIdList)
      this.dialogVisible = false
    }
  }
}
</script>

<style lang="scss" scoped>
.um-table__noheader ::v-deep.el-table__header-wrapper {
  display: none;
}
</style>
