<template>
  <div class="m-full">
    <UmSQ :id="id" ref="UmSQ" :type-code="typeCode" />
  </div>
</template>

<script>
import { mapMutations, mapGetters, mapActions } from 'vuex'
export default {
  name: 'WenjuanManageAdd',
  async beforeRouteLeave(to, from, next) {
    if (this.$refs.UmSQ.beforeUnload() && !this.$refs.UmSQ.isOperateSucces) {
      try {
        await this.$confirm('系统可能不会保存您所做的更改!', '您确定要离开么', {
          confirmButtonText: '确定',
          cancelButtonText: '取消',
          type: 'warning'
        })
        if (this.isAsyncCloseView) {
          this['tagsView/delVisitedView'](from)
          this['tagsView/delCachedView'](from)
          this['globalData/SET_IS_ASYNC_CLOSE_VIEW'](false)
        }
        next()
      } catch (error) {
        this['globalData/SET_COMPUTED_ACTIVE_ROUTE'](true)
        this['globalData/SET_IS_ASYNC_CLOSE_VIEW'](false)
        next(false)
      }
    } else {
      if (this.isAsyncCloseView) {
        this['tagsView/delVisitedView'](from)
        this['tagsView/delCachedView'](from)
        this['globalData/SET_IS_ASYNC_CLOSE_VIEW'](false)
      }
      next()
    }
  },
  props: {},
  data() {
    return {
      id: null,
      typeCode: 1 // 1: 调研问卷 2: 深访问卷
    }
  },
  computed: {
    ...mapGetters(['isAsyncCloseView'])
  },
  created() {
    const { id, typeCode } = this.$route.query
    this.typeCode = +typeCode
    id && (this.id = id)
  },
  mounted() {
  },
  methods: {
    ...mapMutations(['globalData/SET_COMPUTED_ACTIVE_ROUTE', 'globalData/SET_IS_ASYNC_CLOSE_VIEW']),
    ...mapActions(['tagsView/delVisitedView', 'tagsView/delCachedView'])
  }
}
</script>

<style lang="scss" scoped>
.m-full{
  width: calc(100% + 20px);
  height: calc(100% + 20px);
  position: relative;
  margin-top: -20px;
  margin-left: -20px;
}
</style>
