<template>
  <div class="g-container">
    <el-row type="flex" justify="space-between">
      <el-form style="flex: 1">
        <el-form-item label="客户信息：">
          {{ customerInfo.customerName || '--' }}（{{ customerInfo.customerAllMobile || '--' }}）
        </el-form-item>
        <el-form-item label="房产信息：">
          <div v-if="customerInfo.houseInfo" class="m-tag">{{ customerInfo.houseInfo }}</div>
          <span v-else>--</span>
        </el-form-item>
      </el-form>
      <template v-if="!isSuccess && +recycleWayCode === DATA_SOURCES.PHONE">
        <el-button v-if="!isEdit" style="flex-shrink: 0;height: 32px;" type="primary" @click="edit">编辑</el-button>
        <el-button v-else style="flex-shrink: 0;height: 32px;" type="primary" @click="submit">提交</el-button>
      </template>
    </el-row>
    <iframe id="iframe" class="m-preview" :src="url" />
  </div>
</template>

<script>
import { updateQuestion } from '@/api/telephoneInterviews'
import { DATA_SOURCES } from '@/enum'
let _iframe = null
export default {
  name: 'TelephoneInterviewsVoicePreview',
  data() {
    return {
      customerInfo: {},
      DATA_SOURCES,
      url: null,
      isEdit: false,
      isSuccess: false,
      recycleWayCode: null,
      questionType: 1 // 1 调研问卷 2 深访问卷
    }
  },
  created() {
    const { id, info, recycleWayCode, questionType } = this.$route.query
    this.questionType = +questionType || 1
    this.recycleWayCode = recycleWayCode
    let url = process.env.NODE_ENV !== 'localdev' ? location.origin + '/client/question/detail' : 'http://localhost:9528/question/detail'
    if (this.questionType === 2) {
      url = process.env.NODE_ENV !== 'localdev' ? location.origin + '/client/depth/detail' : 'http://localhost:9528/depth/detail'
    }
    this.url = url + '?uuid=' + id + '&telFlag=1&showInformation=0'
    try {
      this.customerInfo = JSON.parse(info)
    } catch (error) {

    }
    window.addEventListener('message', this.getIframeMessage)
  },
  mounted() {
    _iframe = document.getElementById('iframe')
  },
  beforeDestroy() {
    window.removeEventListener('message', this.getIframeMessage)
  },
  methods: {
    getIframeMessage(e) {
      console.warn(e)
      if (e.data.uuid) {
        const load = this.$load()
        updateQuestion(e.data).then(() => {
          _iframe.contentWindow.postMessage('success', '*')
          this.isSuccess = true
        }).finally(() => load.close())
      }
    },
    submit() {
      _iframe.contentWindow.postMessage('submit', '*')
    },
    edit() {
      this.$confirm('确定对当前问卷进行编辑么', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        this.isEdit = true
        const url = process.env.NODE_ENV !== 'localdev' ? location.origin + '/client' : 'http://localhost:9528'
        this.url = url + '?uuid=' + this.$route.query.id + '&telFlag=1'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
    background-color: #fff;
    height: 100%;
    width: 660px;
    margin:0 auto;
    border-radius: 12px;
    box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
    padding: 20px 50px;
    display: flex;
    flex-direction: column;
    ::v-deep {
        .el-form-item__label {
            color: #999!important;
        }
    }
    .m-tag {
        background: #F5F7FA;
        line-height: 28px;
        color: #333;
        font-size: 12px;
        padding: 0 12px;
        display: inline-block;
    }
    .m-preview {
        width: 560px;
        flex: 1;
        border: 1px solid rgba(216,220,230,1);
        border-radius: 8px;
        padding: 20px;
    }
}
</style>
