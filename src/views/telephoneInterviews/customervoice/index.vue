<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="组织：">
          <UmBusOrgan v-model="searchForm.projDto" is-stage />
        </el-form-item>
        <el-form-item label="楼栋：">
          <UmBusHouse v-model="houseDto" :proj-dto="searchForm.projDto" value-key="buildingId" />
        </el-form-item>
        <template v-if="$checkPermission(['DHHF_KHYS_CKFC'])">
          <el-form-item label="单元：">
            <UmBusHouse v-model="houseDto" :proj-dto="searchForm.projDto" value-key="unitId" />
          </el-form-item>
          <el-form-item label="房号：">
            <UmBusHouse v-model="houseDto" :proj-dto="searchForm.projDto" value-key="houseId" />
          </el-form-item>
        </template>
        <el-form-item label="原声类型：" prop="type">
          <el-select v-model="searchForm.type" style="width: 100%" placeholder="请选择" @change="typeChange">
            <el-option label="满意度调研" :value="1" />
            <el-option :disabled="!$checkPermission(['DHHF_KHYS_SFDYSX'])" label="深访调研" :value="2" />
            <el-option :disabled="!$checkPermission(['DHHF_KHYS_ZXDYSX'])" label="专项调研" :value="3" />
          </el-select>
        </el-form-item>
        <el-form-item label="调研计划：" prop="planIds">
          <el-row type="flex">
            <el-select v-model="planYear" style="width: 80px; padding: 0" @change="planYearChange">
              <el-option v-for="item in planYears" :key="item" :value="item">{{ item }}</el-option>
            </el-select>
            <el-select v-model="searchForm.planIds" multiple collapse-tags filterable style="flex: 1" placeholder="请选择">
              <el-option
                v-for="opt in planOptions"
                :key="opt.id"
                :label="opt.name"
                :value="opt.id"
              />
            </el-select>
          </el-row>
        </el-form-item>
        <el-form-item v-if="searchForm.type === 1" label="是否考核：" prop="examineCode">
          <el-select v-model="searchForm.examineCode" clearable style="width: 100%" placeholder="请选择">
            <el-option label="考核" :value="EXAMINE_STATUS.YES" />
            <el-option label="不考核" :value="EXAMINE_STATUS.NO" />
          </el-select>
        </el-form-item>
        <el-form-item :label="searchForm.type === 3 ? '推送月份：' : '考核月份：'" prop="surveyMonths">
          <el-date-picker
            v-model="searchForm.surveyMonths"
            value-format="yyyy-MM"
            type="months"
            placeholder="请选择月份"
            style="width: 100%"
            :picker-options="{
              disabledDate: disabledDate
            }"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item v-if="searchForm.type === 1" label="数据来源：" prop="recycleWayCode">
          <el-select v-model="searchForm.recycleWayCode" clearable style="width: 100%" placeholder="请选择">
            <el-option label="线上评价" :value="DATA_SOURCES.ONLINE" />
            <el-option label="电话回访" :value="DATA_SOURCES.PHONE" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="searchForm.type === 1" label="是否好评：" prop="goodFlag">
          <el-select v-model="searchForm.goodFlag" clearable style="width: 100%" placeholder="请选择">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item v-if="$checkPermission(['DHHF_KHYS_CKFC'])" label="房产编码：" prop="houseIdLike">
          <el-input v-model="searchForm.houseIdLike" placeholder="请输入" clearable />
        </el-form-item>
        <el-form-item label="有无附件：" prop="fileAnswerFlag">
          <el-select v-model="searchForm.fileAnswerFlag" clearable style="width: 100%" placeholder="请选择">
            <el-option label="有" :value="1" />
            <el-option label="无" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="问卷ID：" prop="uuid">
          <el-input v-model="searchForm.uuid" placeholder="请输入" clearable />
        </el-form-item>
      </template>
      <template #suffix>
        <el-button type="primary" :loading="tableLoading" icon="el-icon-search" style="margin-bottom: 20px;" @click="getList()">搜索</el-button>
        <el-button v-if="$checkPermission(['DHHF_KHYS_DC'])" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button>
      </template>
    </UmSearchLayout>

    <div v-loading="tableLoading" class="g-table_full margin-t-20">
      <um-table-full ref="table" :data="tableData" class="margin-t-20" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column key="surveyMonth" label="考核月份" prop="surveyMonth" width="120" />
        <el-table-column key="uuid" prop="uuid" label="问卷ID" width="200" />
        <el-table-column key="houseId" label="房产编码" prop="houseId" width="200">
          <template slot-scope="scope">
            {{ $checkPermission(['DHHF_KHYS_CKFC']) ? scope.row.houseId : '***' }}
          </template>
        </el-table-column>
        <el-table-column key="areaCompanyName" prop="areaCompanyName" label="所属大区" min-width="120" />
        <el-table-column key="cityCompanyName" prop="cityCompanyName" label="城市公司" min-width="120" />
        <el-table-column key="projectName" prop="projectName" label="项目" width="160" />
        <el-table-column key="stageName" prop="stageName" label="分期名称" width="120" />
        <el-table-column key="buildingName" prop="buildingName" label="楼栋" width="140" />
        <el-table-column key="unitName" prop="unitName" label="单元" width="140">
          <template slot-scope="scope">
            {{ $checkPermission(['DHHF_KHYS_CKFC']) ? scope.row.unitName : '***' }}
          </template>
        </el-table-column>
        <el-table-column key="doorNo" prop="doorNo" label="房间" width="140">
          <template slot-scope="scope">
            {{ $checkPermission(['DHHF_KHYS_CKFC']) ? scope.row.doorNo : '***' }}
          </template>
        </el-table-column>
        <el-table-column key="customerName" prop="customerName" label="姓名" width="120">
          <template slot-scope="scope">
            {{ $checkPermission(['DHHF_KHYS_CKXM']) ? scope.row.customerName : '***' }}
          </template>
        </el-table-column>
        <el-table-column key="customerMobile" prop="customerMobile" label="电话" width="120">
          <template slot-scope="scope">
            {{ $checkPermission(['DHHF_KHYS_CKDH']) ? scope.row.customerMobile : '***' }}
          </template>
        </el-table-column>
        <el-table-column key="planName" prop="planName" label="调研计划" width="200" />
        <el-table-column key="pointName" prop="pointName" label="触点/节点" width="160" />
        <el-table-column key="workOrderNo" prop="workOrderNo" label="工单编号" width="200">
          <template slot-scope="scope">
            <el-button v-if="scope.row.workOrderNo" type="text" @click="goOrderDetail(scope.row)">{{ scope.row.workOrderNo }}</el-button>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column key="satOrderNo" prop="satOrderNo" label="满意度工单" width="200">
          <template slot-scope="scope">
            <el-button v-if="scope.row.satOrderNo" type="text" @click="goMydOrderDetail(scope.row)">{{ scope.row.satOrderNo }}</el-button>
            <span v-else>--</span>
          </template>
        </el-table-column>
        <el-table-column v-if="afterSearchParams.type !== 3" key="recycleWayName" prop="recycleWayName" label="数据来源" width="120" />
        <el-table-column v-if="afterSearchParams.type !== 3" key="examineName" prop="examineName" label="是否考核" width="80" />
        <el-table-column v-if="afterSearchParams.type !== 3" key="goodFlag" prop="goodFlag" label="是否好评" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.goodFlag === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
        <el-table-column fixed="right" label="操作" width="100">
          <template slot-scope="scope">
            <el-button v-if="$checkPermission(['DHHF_KHYS_CKWJ'])" type="text" @click="preview(scope.row)">查看问卷</el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        @pagination="searchForm.planIds.length && getList('page')"
      />
    </div>
  </div>
</template>

<script>
import { customPageList, getMydYear, getDepthYear, getSpecYear, getMydPlan, getDepthPlan, getSpecPlan } from '@/api/telephoneInterviews'
import getList from '@/mixins/getList'
import { EXAMINE_STATUS, DECORATION_STATUS, DATA_SOURCES } from '@/enum'
import { exportQuestionnaire } from '@/api/export'
import exportData from '@/mixins/exportData'
const curDate = new Date()
export default {
  name: 'TelephoneInterviewsVoice',
  mixins: [getList, exportData],
  props: {},
  data() {
    return {
      exportApi: exportQuestionnaire,
      option: [], // 调研计划列表
      depthOption: [], // 深访计划列表
      listApi: customPageList,
      EXAMINE_STATUS,
      DECORATION_STATUS,
      DATA_SOURCES,
      planYear: null, // 选择的调研计划年份
      searchForm: {
        type: 1,
        surveyMonths: [curDate.getFullYear() + '-' + (curDate.getMonth() + 1).toString().padStart(2, '0')],
        pointId: null,
        uuid: null,
        examineCode: null,
        recycleWayCode: null,
        goodFlag: null,
        houseId: null,
        fileAnswerFlag: null,
        planIds: [],
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null,
          projectId: null,
          stageId: null
        }
      },
      afterSearchParams: {}, // 保存搜索后的条件
      houseDto: {
        buildingId: null,
        unitId: null,
        houseId: null
      },
      mydYears: [],
      specYears: [],
      depthYears: [],
      planOptions: []
    }
  },
  computed: {
    planYears() {
      const obj = {
        1: 'mydYears',
        2: 'depthYears',
        3: 'specYears'
      }
      return this[obj[this.searchForm.type]]
    }
  },
  watch: {
    async 'searchForm.type'(v) {
      this.planYear = this.planYears[0]
      await this.getPlans()
      if(this.planOptions[0]?.id) {
        this.searchForm.planIds = [this.planOptions[0].id]
      }
    }
  },
  async activated() {
    try {
      if (this.searchForm.planIds.length) {
        this.getList()
      }
    } catch (error) {

    }
  },
  created() {
    this.getAllYears()
  },
  methods: {
    formatExportParams() {
      return {
        ...this.searchForm,
        planTypeCode: {
          1: '2200231',
          2: '2200232',
          3: '2200233'
        }[this.searchForm.type]
      }
    },
    getAllYears() {
      getDepthYear().then(res => {
        this.depthYears = res.data || []
      })
      getMydYear().then(async res => {
        this.mydYears = res.data || []
        this.planYear = this.mydYears[0]
        await this.getPlans()
        if (this.planOptions[0]?.id) {
          this.searchForm.planIds = [this.planOptions[0].id]
          this.getList()
        }
      })
      getSpecYear().then(res => {
        this.specYears = res.data || []
      })
    },
    afterApiCallBack() {
      this.afterSearchParams = { ...this.searchForm }
    },
    getPlans() {
      if (!this.planYear) return
      const API = {
        1: getMydPlan,
        2: getDepthPlan,
        3: getSpecPlan
      }[this.searchForm.type]
      return API({ year: this.planYear }).then(res => {
        this.planOptions = res.data || []
      })
    },
    // 只能选择当前年，不能跨年选择
    disabledDate(e) {
      return new Date(e) < new Date(this.planYear + '-01') || new Date(e) > new Date(this.planYear + '-12')
    },
    // 深访、满意度切换
    typeChange() {
      this.searchForm.planIds = []
      this.planYear = null
      this.planOptions = []
    },
    planYearChange() {
      this.planOptions = []
      this.searchForm.planIds = []
      this.searchForm.surveyMonths = []
      this.getPlans()
    },
    beforeApiCallBack(params) {
      if (!params.planIds.length) {
        this.$message.warning('调研计划不能为空')
        return 'ABORT'
      }
      if (!params.surveyMonths.length) {
        this.$message.warning(`${this.searchForm.type === 3 ? '推送' : '考核'}月份不能为空`)
        return 'ABORT'
      }
      params.buildingId = this.houseDto.buildingId
      params.unitId = this.houseDto.unitId
      params.houseId = this.houseDto.houseId
      if (params.houseIdLike) {
        params.houseId = params.houseIdLike
        delete params.houseIdLike
      }
      if (this.searchForm.type === 2) {
        delete params.examineCode
        delete params.recycleWayCode
        delete params.goodFlag
      }
      return params
    },
    organSuccess({ areaCompanyId, cityCompanyId }) {
      this.searchForm.projDto.areaCompanyId = areaCompanyId
      this.searchForm.projDto.cityCompanyId = cityCompanyId
    },
    projectSuccess({ projectId, stageId }) {
      this.searchForm.projDto.projectId = projectId
      this.searchForm.projDto.stageId = stageId
    },
    // 查看问卷
    preview(data) {
      const info = JSON.stringify({
        customerName: data.customerName,
        customerAllMobile: data.customerMobile,
        houseInfo: data.projectName + '-' + data.stageName + '-' + data.buildingName + '-' + data.unitName + '-' + data.doorNo
      })
      this.$router.push({
        path: '/TelephoneInterviews/customervoice/preview',
        query: {
          id: data.uuid,
          recycleWayCode: data.recycleWayCode,
          questionType: this.afterSearchParams.type,
          info: info
        }
      })
    },
    // 跳转工单
    goOrderDetail({ workOrderNo, businessId }) {
      if (workOrderNo.startsWith('ZX')) { // 咨询工单
        window.open(process.env.VUE_APP_CUSTOM_URL + '/order-statistics/consult-order-detail?id=' + businessId)
      } else if (workOrderNo.startsWith('TS')) { // 投诉工单
        window.open(process.env.VUE_APP_CUSTOM_URL + '/order-statistics/complaint-order-detail?id=' + businessId)
      } else { // 维修工单
        window.open(process.env.VUE_APP_CUSTOM_URL + '/order-statistics/repair-order-detail?id=' + businessId)
      }
    },
    // 跳转满意度工单
    goMydOrderDetail({ satOrderId }) {
      window.open(process.env.VUE_APP_CUSTOM_URL + '/order-statistics/satisfied-order-detail?id=' + satOrderId)
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
::v-deep {
  .el-select__tags {
    display:flex;
    flex-wrap:nowrap;
  }
  .el-select__tags-text {
    max-width: 80px;  //设置最大宽度 超出显示...
    display: inline-block;
    overflow: hidden;
    vertical-align:bottom;
    text-overflow:ellipsis;
  }
}

</style>
