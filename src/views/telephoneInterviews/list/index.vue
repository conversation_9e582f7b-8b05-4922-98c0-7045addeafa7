<template>
  <div class="g-container">
    <UmSearchLayout label-width="100px">
      <template #default>
        <el-form-item label="区域/城市：">
          <UmBusOrgan v-model="searchForm.projDto" />
        </el-form-item>
        <el-form-item label="项目/分期：">
          <UmBusProject v-model="searchForm.projDto" :area-company-id="searchForm.projDto.areaCompanyId" :city-company-id="searchForm.projDto.cityCompanyId" />
        </el-form-item>
        <el-form-item label="房间：">
          <UmBusRoom v-model="searchForm.houseId" :emit-path="false" check-strictly :project-id="searchForm.projDto.projectId" :stage-id="searchForm.projDto.stageId" />
        </el-form-item>
        <el-form-item label="触点/节点：" prop="pointId">
          <UmBusScene v-model="searchForm.pointId" placeholder="请选择" />
        </el-form-item>
        <el-form-item label="是否考核：" prop="examineFlag">
          <el-select v-model="searchForm.examineFlag" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="考核" :value="1" />
            <el-option label="不考核" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="调研月份：" prop="surveyMonth">
          <el-date-picker v-model="searchForm.surveyMonth" value-format="yyyy-MM" :picker-options="pickerOptions" type="month" placeholder="选择月" style="width: 100%" :clearable="false" />
        </el-form-item>
        <el-form-item label="装修类型：" prop="decorateTypeCode">
          <el-select v-model="searchForm.decorateTypeCode" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="毛坯" :value="DECORATION_STATUS.SIMPLE" />
            <el-option label="精装" :value="DECORATION_STATUS.FINE" />
          </el-select>
        </el-form-item>
        <el-form-item label="是否华发物业：" prop="wyFlag">
          <el-select v-model="searchForm.wyFlag" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="是" :value="1" />
            <el-option label="否" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="有无会所：" prop="clubFlag">
          <el-select v-model="searchForm.clubFlag" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="有" :value="1" />
            <el-option label="无" :value="0" />
          </el-select>
        </el-form-item>
        <el-form-item label="房产编码：" prop="houseIdLike">
          <el-input v-model="searchForm.houseIdLike" placeholder="请输入" clearable />
        </el-form-item>
      </template>
      <template #suffix>
        <el-button v-if="$checkPermission(['DHHF_DHHF_DC'])" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button>
        <el-button type="primary" :loading="tableLoading" icon="el-icon-search" style="margin-bottom: 20px;" @click="getList">搜索</el-button>
      </template>
    </UmSearchLayout>

    <div v-loading="tableLoading" class="g-table_full margin-t-20">
      <um-table-full :data="tableData" class="margin-t-20" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column label="调研月份" prop="surveyMonth" width="120" />
        <el-table-column label="房产编码" prop="houseId" width="200" />
        <el-table-column prop="pointName" label="触点/节点" width="160" />
        <el-table-column prop="areaCompanyName" label="所属大区" width="140" />
        <el-table-column prop="cityCompanyName" label="城市公司" width="140" />
        <el-table-column prop="projectName" label="项目" width="160" />
        <el-table-column prop="stageName" label="分期名称" width="120" />
        <el-table-column prop="buildingName" label="楼栋" width="120" />
        <el-table-column prop="unitName" label="单元" width="120" />
        <el-table-column prop="doorNo" label="房间号" width="100" />
        <el-table-column prop="customerName" label="业主姓名" width="120" />
        <el-table-column prop="customerMobile" label="联系方式" width="160" />
        <el-table-column prop="effectTime" label="问卷有效期" width="200" />
        <el-table-column prop="examineFlag" label="是否考核" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.examineFlag === 1 ? '考核' : '不考核' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="decorationStandardName" label="装修类型" width="80" />
        <el-table-column prop="clubFlag" label="有无会所" width="80">
          <template slot-scope="scope">
            <span>{{ scope.row.clubFlag === 1 ? '有' : '无' }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="wyFlag" label="是否物业操盘" width="120">
          <template slot-scope="scope">
            <span>{{ scope.row.wyFlag === 1 ? '是' : '否' }}</span>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        @pagination="getList('page')"
      />
    </div>
  </div>
</template>

<script>
import { pageList } from '@/api/telephoneInterviews'
import getList from '@/mixins/getList'
import { EXAMINE_STATUS, DECORATION_STATUS } from '@/enum'
import { exportSurveyTask } from '@/api/export'
import exportData from '@/mixins/exportData'
const curDate = new Date()
const currentMonth = (curDate.getMonth() + 1).toString().padStart(2, '0')
const currentYear = curDate.getFullYear()
export default {
  name: 'TelephoneInterviewsList',
  mixins: [getList, exportData],
  props: {},
  data() {
    return {
      exportApi: exportSurveyTask,
      listApi: pageList,
      EXAMINE_STATUS,
      DECORATION_STATUS,
      pickerOptions: {
        disabledDate(time) {
          const currentDate = `${currentYear}-${currentMonth}` // 当前月份
          const previousDate = `${+currentMonth === 1 ? (currentYear - 1) + '-12' : currentYear + '-' + (currentMonth - 1)}` // 上个月份
          const selectedMonth = time.getFullYear() + '-' + (time.getMonth() + 1) // 选择的月
          // 禁用当前月份之后和上个月份之前的日期
          return new Date(selectedMonth) > new Date(currentDate) || new Date(selectedMonth) < new Date(previousDate)
        }
      },
      searchForm: {
        surveyMonth: currentYear + '-' + currentMonth,
        pointId: null,
        examineFlag: null,
        decorateTypeCode: null,
        wyFlag: null,
        clubFlag: null,
        houseId: null,
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null,
          projectId: null,
          stageId: null
        }
      }
    }
  },
  created() { },
  activated() {
    this.getList() // 缓存过来的刷新页面
  },
  methods: {
    beforeApiCallBack(params) {
      if (!params.surveyMonth) {
        this.$message.warning('调研月份不能为空')
        return 'ABORT'
      }
      return params
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}

</style>
