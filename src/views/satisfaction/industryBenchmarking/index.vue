<!--行业对标-->
<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form ref="searchForm" :model="searchForm1" label-width="50px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="year" label="年度">
              <el-date-picker
                v-model="searchForm1.year"
                type="year"
                value-format="yyyy"
                placeholder="请选择年度"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6" class="fr text-right">
            <el-button type="primary" :loading="tableLoading1" icon="el-icon-search" @click="getList1">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row :gutter="30" class="margin-b-20">
        <el-col :span="12">
          <CommonTabs :tabs="tabList" @changeTab="changeTab" />
        </el-col>
        <el-button
          v-if="$checkPermission(['MYDFX_HYDB_XZ', 'JCPZ_HYDB_XZ'])"
          style="float: right"
          icon="el-icon-circle-plus-outline"
          type="primary"
          class="margin-r-20"
          @click="formInline.ruleId = null; dialogVisible=true"
        >
          新增
        </el-button>
      </el-row>
      <um-table-full
        :data="tableData1"
        scroll
      >
        <el-table-column
          label="序号"
          width="80"
          type="index"
        />
        <el-table-column
          label="年度"
          prop="year"
        >
          <template slot-scope="{ row }">
            <el-button :disabled="row.statusCode === 1" type="text" @click="editYearRule(row)">{{ row.year }}</el-button>
          </template>
        </el-table-column>
        <el-table-column
          prop="createUser"
          label="创建人"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
        />
        <el-table-column
          label="操作"
          width="200"
        >
          <template slot-scope="{ row }">
            <template v-if="row.statusCode === 0">
              <el-button v-if="$checkPermission(['MYDFX_HYDB_BJ', 'JCPZ_HYDB_BJ'])" type="text" @click="$router.push(`/satisfaction/industryBenchmarking/edit?id=${row.ruleId}&year=${row.year}`)">编辑</el-button>
              <el-button v-if="$checkPermission(['MYDFX_HYDB_FB', 'JCPZ_HYDB_FB'])" type="text" @click="publishYearRule(row)">发布</el-button>
            </template>
            <template v-else>
              <el-button v-if="$checkPermission(['MYDFX_HYDB_CH', 'JCPZ_HYDB_CH'])" type="text" @click="publishYearRule(row)">撤回</el-button>
              <el-button type="text" @click="$router.push(`/satisfaction/industryBenchmarking/detail?id=${row.ruleId}&year=${row.year}&isDetail=1`)">详情</el-button>
            </template>
            <el-button v-if="$checkPermission(['MYDFX_HYDB_SC', 'JCPZ_HYDB_SC'])" type="text" @click="deleteYearRule(row)"><span class="danger">删除</span></el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        style="margin-top: 20px"
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        @pagination="getList1('page')"
      />
    </div>
    <el-dialog :visible.sync="dialogVisible" custom-class="flex-body" :title="`${formInline.ruleId ? '编辑' : '新建'}`" width="318px" @close="$refs.formInline.resetFields()">
      <el-form ref="formInline" :inline="true" :model="formInline" class="demo-form-inline" label-width="50px">
        <el-form-item ref="year" label="年份" prop="year" :rules="[{required: true, message: '请选择年份', trigger: 'change' }]">
          <el-date-picker
            v-model="formInline.year"
            type="year"
            style="width: 100%"
            value-format="yyyy"
            format="yyyy"
            placeholder="选择年"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="selectYear">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getScoreMapPageList, addYearRule, publishYearRule, deleteYearRule, editYearRule } from '@/api/satisfaction/industryBenchmarking'
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs/index.vue'
export default {
  name: 'SatisfactionIndustryBenchmarking',
  components: { CommonTabs },
  mixins: [getList1],
  data() {
    return {
      listApi1: getScoreMapPageList,
      searchForm1: {
        year: null,
        statusCode: 0
      },
      dialogVisible: false,
      tabList: [
        {
          label: '待发布',
          value: 0,
          count: 0,
          key: 'state0'
        },
        {
          label: '已发布',
          value: 1,
          count: 0,
          key: 'state1'
        }
      ],
      formInline: {
        ruleId: null,
        year: ''
      }
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    tableCallBack1() {
      this.tabList.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    // 选择年份
    selectYear() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          const load = this.$load()
          const API = this.formInline.ruleId ? editYearRule : addYearRule
          const params = { ...this.formInline }
          if (this.formInline.year instanceof Date) {
            params.year = new Date(params.year).getFullYear()
          }
          API(params).then(res => {
            this.$message.success(res.msg)
            this.dialogVisible = false
            this.getList1()
          }).finally(() => load.close())
        }
      })
    },
    editYearRule(data) {
      this.dialogVisible = true
      this.$nextTick(() => {
        this.formInline.ruleId = data.ruleId
        this.formInline.year = new Date(data.year, 1, 1)
      })
    },
    // 发布撤回
    publishYearRule(data) {
      this.$confirm(`是否确定${data.statusCode ? '撤回' : '发布'}行业对标：${data.year}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        publishYearRule({ ruleId: data.ruleId, statusCode: +!data.statusCode }).then(res => {
          this.$message.success(res.msg)
          this.getList1()
        }).finally(() => load.close())
      })
    },
    // 删除
    deleteYearRule(data) {
      this.$confirm(`是否确定删除行业对标：${data.year}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteYearRule({ id: data.ruleId }).then(res => {
          this.$message.success(res.msg)
          this.getList1()
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .u-dialog__tip {
    flex: 1;
    line-height: 32px;
    background: #FEF0F0;
    border-radius: 4px;
    color: $--color-danger;
    margin-right: 20px;
    padding-left: 16px;
    position: relative;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #F8716B;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
}
</style>
