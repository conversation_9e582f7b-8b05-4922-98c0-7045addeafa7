<template>
  <div style="height: 100%">
    <CommonTitle :title="`${$route.query.year}行业分位值设置`" show-bg margin-bottom="0" />
    <div class="flex-box">
      <div class="flex-box-left">
        <div class="header f-flex-acjcsb">
          <div class="header-left" :class="searchForm.typeCode === TARGET_POINT.TARGET ? 'click' : ''" @click="changeTab(TARGET_POINT.TARGET)">指标</div>
          <div class="header-right" :class="searchForm.typeCode === TARGET_POINT.POINT ? 'click' : ''" @click="changeTab(TARGET_POINT.POINT)">触点/节点</div>
        </div>
        <el-scrollbar wrap-class="scrollbar-wrapper" class="left-scroll">
          <UmConfigTree
            v-show="searchForm.typeCode === TARGET_POINT.TARGET"
            :tree-data="treeData"
            :default-props="{
              label: 'targetName',
              value: 'tgtTargetId',
              children: 'targetChildren'
            }"
            :current-id="currentNode.tgtTargetId"
            @node-click="nodeClick"
          />
          <UmConfigTree
            v-show="searchForm.typeCode === TARGET_POINT.POINT"
            :tree-data="pointOptions"
            :current-id="currentPointNode.value"
            @node-click="nodeClick"
          />
        </el-scrollbar>
      </div>
      <div v-loading="tableLoading" class="flex-box-right">
        <div class="u-right__title">
          {{ searchForm.typeCode === TARGET_POINT.POINT ? currentPointNode.label : currentNode.targetFullName }}
        </div>
        <el-form v-if="TARGET_POINT.POINT === searchForm.typeCode && currentPointNode.children && currentPointNode.children.length">
          <el-row :gutter="20">
            <el-col :span="8">
              <el-form-item label="组织：" label-width="90px" class="mb-0">
                <el-select-tree
                  ref="elSelectTree"
                  v-model="searchForm1.chooseOrganId"
                  :options="options"
                  :filterable="true"
                  separator="-"
                  :check-strictly="true"
                  :show-all-levels="true"
                  :clearable="true"
                  :props="{
                    emitPath: true,
                    label: 'name',
                    value: 'code'
                  }"
                  placeholder="请选择"
                  style="width: 100%;"
                  @change="(value, node) => {treeChange(value, node, 'searchForm1')}"
                />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="行业分位：" label-width="90px" class="mb-0">
                <el-input v-model="searchForm1.standardScore" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="满意度：" label-width="90px" class="mb-0">
                <el-input v-model="searchForm1.satisfaction" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <el-col :span="8">
              <el-form-item label="转换分：" label-width="90px" class="mb-0">
                <el-input v-model="searchForm1.switchScore" placeholder="请输入" clearable />
              </el-form-item>
            </el-col>
            <!-- <el-col :span="8">
              <el-form-item label="概览线状态：" label-width="90px" class="mb-0">
                <el-select v-model="searchForm1.generalFlag">
                  <el-option label="启用" :value="1" />
                  <el-option label="禁用" :value="0" />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-form-item label="" class="fr mb-0 mr-10">
              <el-button v-if="!isDetail" type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
              <el-button v-if="!isDetail" type="primary" icon="el-icon-circle-plus-outline" @click="editForm.itemId = null;editFormVisible = true">新增</el-button>
            </el-form-item>
          </el-row>
        </el-form>
        <div v-else class="text-right margin-b-20">
          <el-button v-if="!isDetail" type="primary" icon="el-icon-circle-plus-outline" @click="editForm.itemId = null;editFormVisible = true">新增</el-button>
        </div>
        <um-table-full
          ref="table"
          :data="tableData"
          scroll
        >
          <el-table-column type="index" label="序号" width="100" fixed="left" align="center" />
          <el-table-column key="standardScore" label="行业分位" prop="standardScore" align="center" />
          <el-table-column key="satisfaction" label="满意度" prop="satisfaction" align="center">
            <template slot-scope="{row}">{{ row.satisfaction }}%</template>
          </el-table-column>
          <template v-if="TARGET_POINT.POINT === searchForm.typeCode && currentPointNode.children && currentPointNode.children.length">
            <el-table-column key="organName" label="适用范围" width="200" :formatter="$formatterTable" prop="organName" align="center" />
            <!-- <el-table-column key="generalFlag" label="概览线" :formatter="$formatterTable" prop="generalFlag" align="center">
              <template slot-scope="{ row }">{{ row.generalFlag ? '启用' : '禁用' }}</template>
            </el-table-column> -->
            <el-table-column key="switchScore" label="转换分" :formatter="$formatterTable" prop="switchScore" align="center" />
          </template>
          <el-table-column v-if="!isDetail" align="left" width="140" label="操作" fixed="right">
            <template slot-scope="scope">
              <div>
                <el-button
                  type="text"
                  @click="editCate(scope.row)"
                >
                  修改
                </el-button>
                <el-button
                  type="text"
                  @click="delCate(scope.row)"
                >
                  <span class="danger">删除</span>
                </el-button>
              </div>
            </template>
          </el-table-column>
        </um-table-full>
      </div>
    </div>
    <el-dialog
      :title="editForm.itemId ? '编辑' : '新增'"
      :visible.sync="editFormVisible"
      width="400px"
      z-index="1999"
      append-to-body
      :close-on-click-modal="false"
      @close="$refs.editForm.resetFields();"
    >
      <el-form ref="editForm" :model="editForm" label-width="85px" label-suffix="：">
        <el-form-item ref="standardScore" label="分位值" prop="standardScore" :rules="[{ required: true, message: '请输入分位值', trigger: ['blur']}]">
          <el-input-number v-model="editForm.standardScore" placeholder="请输入" style="width: 100%" :min="0" :max="100" :controls="false" />
        </el-form-item>
        <el-form-item ref="satisfaction" label="满意度" prop="satisfaction" :rules="[{ required: true, message: '请输入满意度', trigger: ['blur']}]">
          <el-input-number v-model="editForm.satisfaction" placeholder="请输入" style="width: calc(100%-20px)" :min="0" :max="100" :precision="2" :controls="false" /> %
        </el-form-item>
        <template v-if="TARGET_POINT.POINT === searchForm.typeCode && currentPointNode.children && currentPointNode.children.length">
          <el-form-item ref="satisfaction" label="适用范围" prop="chooseOrganId" :rules="[{ required: true, message: '请选择适用范围', trigger: ['blur']}]">
            <el-select-tree
              ref="elSelectTree"
              v-model="editForm.chooseOrganId"
              :options="options"
              :filterable="true"
              separator="-"
              :check-strictly="true"
              :show-all-levels="true"
              :clearable="true"
              :props="{
                emitPath: true,
                label: 'name',
                value: 'code'
              }"
              placeholder="请选择"
              style="width: 100%;"
              @change="(value, node) => {treeChange(value, node, 'editForm')}"
            />
          </el-form-item>
          <!-- <el-form-item ref="generalFlag" label="概览线状态" prop="generalFlag">
            <el-switch v-model="editForm.generalFlag" />
          </el-form-item> -->
          <!-- 触点选中父节点展示 -->
          <el-form-item ref="switchScore" label="转换分" prop="switchScore">
            <el-input-number v-model="editForm.switchScore" placeholder="请输入" style="width: 100%" :min="0" :max="100" :controls="false" />
          </el-form-item>
        </template>
      </el-form>
      <div slot="footer" class="dialog-footer">
        <el-button type="primary" plain @click="editFormVisible=false">取消</el-button>
        <el-button type="primary" @click="submitHandle">确定</el-button>
      </div>
    </el-dialog>
  </div>
</template>

<script>
import getPointList from '@/mixins/getPointList'
import CommonTitle from '@/components/CommonTitle'
import { getTargetTree } from '@/api/base/indicator/index'
import { getItemList, addItem, editItem, deleteItem } from '@/api/satisfaction/industryBenchmarking'
import { TARGET_POINT } from '@/enum'
import { getTwoLevelOrgan } from '@/api/common'

export default {
  name: 'SatisfactionIndustryBenchmarkingEdit',
  components: { CommonTitle },
  mixins: [getPointList],
  props: {},
  data() {
    return {
      tableLoading: false,
      tableData: [],
      treeData: [],
      TARGET_POINT,
      currentNode: {}, //  当前选中的指标
      currentPointNode: {}, // 当前选中的触点/节点
      options: [],
      ids: [],
      searchForm: {
        ruleId: null,
        typeCode: TARGET_POINT.TARGET,
        pointTypeCode: 2000071, // 2000071 地产 现阶段 在触点分类下新增 默认是地产 2000072 物业 【暂时没有，后续要加】
        businessId: null
      },
      searchForm1: {
        chooseOrganId: [],
        standardScore: null,
        switchScore: null,
        satisfaction: null,
        organId: null,
        organType: null,
        organName: null,
        generalFlag: null
      },
      editFormVisible: false,
      editForm: {
        chooseOrganId: [],
        itemId: null,
        standardScore: undefined,
        satisfaction: undefined,
        switchScore: undefined,
        organId: null,
        generalFlag: false,
        organType: null, // 2300041：总部 2300042：区域 2300043：城市
        organName: null
      },
      isDetail: false
    }
  },
  mounted() {},
  async created() {
    this.searchForm.ruleId = this.$route.query.id
    this.isDetail = !!this.$route.query.isDetail
    try {
      await this.getTargetTree()
      this.getTwoLevelOrgan()
      this.currentNode = this.treeData[0]
      this.searchForm.businessId = this.currentNode.tgtTargetId
      this.getList()
    } catch (error) {

    }
  },
  methods: {
    //
    getList() {
      this.tableLoading = true
      switch (this.searchForm.typeCode) {
        case TARGET_POINT.TARGET:
          this.searchForm.businessId = this.currentNode.tgtTargetId
          break
        case TARGET_POINT.POINT:
          this.searchForm.businessId = this.currentPointNode.value
          break
      }
      let params = { ...this.searchForm, businessId: this.searchForm.businessId === 'other' ? '0' : this.searchForm.businessId }
      if (TARGET_POINT.POINT === this.searchForm.typeCode && this.currentPointNode.children && this.currentPointNode.children.length) {
        params = {
          ...params,
          ...this.searchForm1
        }
      }
      getItemList(params).then(res => {
        this.tableData = res.data || []
        this.$nextTick(() => {
          this.$refs.table.el.doLayout()
        })
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 获取指标树
    getTargetTree() {
      const load = this.$load()
      return getTargetTree({ disableFlag: null }).then(res => {
        this.treeData = res.data || []
      }).finally(() => load.close())
    },
    afterGetOptions() {
      this.currentPointNode = this.pointOptions[0]
    },
    async changeTab(val) {
      this.searchForm.typeCode = val
      switch (val) {
        case TARGET_POINT.TARGET: !this.treeData.length && (await this.getTargetTree()); break
        case TARGET_POINT.POINT: !this.pointOptions.length && (await this.getPointOption(true)); break
      }
      this.getList()
    },
    // 数节点点击
    nodeClick(data, res) {
      switch (this.searchForm.typeCode) {
        case TARGET_POINT.TARGET:
          this.currentNode = {
            ...data,
            targetFullName: res.join('-')
          }
          break
        case TARGET_POINT.POINT:
          this.currentPointNode = {
            ...data,
            label: res.join('-')
          }
          break
      }
      this.getList()
    },
    editCate(data) {
      this.editFormVisible = true
      this.$nextTick(() => {
        this.editForm = {
          itemId: data.itemId,
          standardScore: data.standardScore,
          satisfaction: data.satisfaction,
          switchScore: data.switchScore,
          chooseOrganId: [data.organId],
          organId: data.organId,
          generalFlag: !!data.generalFlag,
          organType: data.organType,
          organName: data.organName
        }
      })
    },
    submitHandle() {
      this.$refs.editForm.validate(valid => {
        if (!valid) return
        const load = this.$load()
        const API = this.editForm.itemId ? editItem : addItem
        const params = { ...this.editForm, ruleId: this.searchForm.ruleId, pointTypeCode: 2000071, generalFlag: +this.editForm.generalFlag }
        // 如果是编辑
        if (!this.editForm.itemId) {
          params.typeCode = this.searchForm.typeCode
          params.businessId = this.searchForm.businessId === 'other' ? '0' : this.searchForm.businessId
          params.targetTypeCode = this.currentNode.levelCode
        }
        API(params).then(res => {
          this.$message.success(res.msg)
          this.editFormVisible = false
          this.getList()
        }).finally(() => load.close())
      })
    },
    delCate(data) {
      this.$confirm(`是否确定删除该行业分位：${data.standardScore}？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        deleteItem({ id: data.itemId }).then(res => {
          this.$message.success(res.msg)
          this.getList()
        }).finally(() => load.close())
      })
    },
    getTwoLevelOrgan() {
      getTwoLevelOrgan().then(res => {
        console.log(res)
        const options = [{
          name: '华发股份',
          code: -1,
          children: res.data || []
        }]
        this.options = options
      })
    },
    treeChange(value, node, key) {
      if (!value.length) {
        this[key].organId = null
        this[key].organType = null
        this[key].organName = null
        return
      }
      const length = value.length
      switch (length) {
        case 1:
          this[key].organId = value[0]
          this[key].organType = 2300041
          break
        case 2:
          this[key].organId = value[1]
          this[key].organType = 2300042
          break
        case 3:
          this[key].organId = value[2]
          this[key].organType = 2300043
          break
      }
      this[key].organName = node.pathLabels.join('-')
    }
  }
}
</script>

<style lang="scss" scoped>
.flex-box {
  display: flex;
  justify-content: space-between;
  height: calc(100% - 56px);
  .header {
    height: 56px;
    display: flex;
    align-items: center;
    background: #F5F7FA;
    &-left, &-right {
      flex: 1;
      text-align: center;
      line-height: 56px;
      font-size: 16px;
      color: #666666;
      letter-spacing: 0;
      font-weight: 400;
      cursor: pointer;
    }
    & > div.click {
      background: #fff;
      font-size: 16px;
      color: #005DCF;
      font-weight: 500;
    }
  }
  &-left {
    width: 300px;
    background: #FFFFFF;
    box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
    border-radius: 0px 0px 0px 12px;
    height: 100%;
    overflow-y: auto;
    z-index: 1;
    display: flex;
    flex-direction:column;
    .left-scroll {
      flex: 1;
    }
  }
  &-right {
    flex: 1;
    background: #FFFFFF;
    border-radius: 0px 8px 8px 0px;
    height: 100%;
    overflow-x: hidden;
  }
}
.flex-box-right {
  position: relative;
  display: flex;
  flex-direction: column;
  padding: 20px;
  padding-top: 0;
  .u-right__title {
    line-height: 56px;
    height: 56px;
    font-size: 16px;
    font-weight: 400;
  }
}
</style>
