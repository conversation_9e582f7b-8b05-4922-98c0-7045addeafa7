<template>
  <div class="m-block">
    <div class="m-block__head cus-table">
      <div class="cus-table__l">{{ title }}</div>
      <div class="cus-table__r">获得次数</div>
    </div>
    <div v-for="(item, index) in list.slice(0, 5)" :key="item.name" class="m-block__body cus-table" @click="goDetail(item)">
      <div class="cus-table__l row_1">
        <svg-icon v-if="index <= 2" :icon-class="`rank-${index + 1}`" />
        <span v-else class="cus-table__l--index">{{ index + 1 }}</span>
        <span v-if="type===1" :title="item.name">{{ item.name || '--' }}</span>
        <span v-if="type===2" :title="item.categoryName">{{ item.categoryName || '--' }}</span>
      </div>
      <div class="cus-table__r progress">
        <div :ref="'progress_' + index" class="progress-box">
          <div class="progress-box__point" />
          <div
            class="progress-box__bar"
            :style="{
              width: width
            }"
          />
        </div>
        <span class="progress-value">{{ item.count }}</span>
      </div>
    </div>
    <um-empty v-if="!list.length" />
  </div>
</template>

<script>
export default {
  name: 'Quota',
  props: {
    title: {
      type: String,
      default: '指标'
    },
    total: {
      type: [String, Number],
      default: 0
    },
    list: {
      type: Array,
      default: () => []
    },
    pointId: {
      type: [String, Number],
      default: null
    },
    type: { // 1:原因  2:分类
      type: Number,
      default: 1
    }
  },
  data() {
    return {
      width: 0
    }
  },
  watch: {
    list: {
      immediate: true,
      handler(v) {
        this.$nextTick(() => {
          this.resize()
        })
      }
    }
  },
  mounted() {
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.list.slice(0, 5).forEach((item, index) => {
        const progress = this.$refs['progress_' + index][0]
        const progressBar = progress.querySelector('.progress-box__bar')
        const progressPoint = progress.querySelector('.progress-box__point')
        const percent = item.count / this.total
        progressBar.style.width = percent * progress.offsetWidth + 'px'
        progressPoint.style.transform = 'translateX(' + (percent * progress.offsetWidth - progressPoint.offsetWidth / 2) + 'px)'
      })
    },
    goDetail(data) {
      this.$emit('goDetail', data, this.pointId, this.title === `不满意${this.type === 1 ? '原因' : '分类'}`)
    }
  }
}
</script>

<style lang="scss" scoped>
.m-block {
  background: #F5F7FA;
  border-radius: 8px;
  padding: 20px 16px;
  height: 240px;
  &__head {
    color: #333;
    font-size: 14px;
    font-weight: 400;
    margin-bottom: 18px;
  }
  &__body {
    margin-bottom: 20px;
    &:last-child {
      margin-bottom: 0;
    }
    .cus-table__l {
      color: #6C7B96;
      .svg-icon {
        font-size: 14px;
        margin-right: 4px;
      }
    }
    .progress {
      display: flex;
      align-items: center;
      cursor: pointer;
      &-box {
        flex: 1;
        height: 10px;
        border-radius: 5px;
        background-color: #E4E8F6;
        position: relative;
        &__bar {
          position: absolute;
          height: 100%;
          transition: all ease-in-out 1s;
          background-image: linear-gradient(to right, #A9C4FB, $--color-primary);
          border-radius: 8px;
          top: 0;
          z-index: 1;
        }
        &__point {
          transition: all ease-in-out 1s;
          z-index: 2;
          position: relative;
          width: 18px;
          height: 18px;
          margin-top: -4px;
          border: 5px solid #fff;
          border-radius: 50%;
          background-color: $--color-primary;
          box-shadow: 1px 2px 3px #D1D9E6;
        }
      }
      &-value {
        width: 38px;
        text-align: right;
        font-weight: bold;
        color: #ABB5C3;
        font-family: Akrobat-Bold, Akrobat;
      }
    }
  }
  .cus-table {
    display: flex;
    &__l {
      width: 110px;
      &--index {
        display: inline-block;
        width: 14px;
        margin-right: 4px;
        text-align: center;
      }
    }
    &__r {
      flex: 1;
    }
  }
}
</style>

