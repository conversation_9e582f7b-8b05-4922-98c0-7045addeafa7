<template>
  <div>
    <UmSearchLayout label-width="70px">
      <template #default>
        <el-form-item prop="areaCompanyId" label="区域：">
          <UmBusCommon v-model="projDto" value-key="areaCompanyId" is-auth :clearable="hasGuFenAuth" />
        </el-form-item>
        <el-form-item prop="cityCompanyId" label="城市：">
          <UmBusCommon v-model="projDto" value-key="cityCompanyId" :area-company-id="projDto.areaCompanyId" is-auth :clearable="hasGuFenAuth" />
        </el-form-item>
        <el-form-item prop="projectId" label="项目：">
          <UmBusCommon v-model="projDto" value-key="projectId" :area-company-id="projDto.areaCompanyId" :city-company-id="projDto.cityCompanyId" is-auth :clearable="hasGuFenAuth" />
        </el-form-item>
        <el-form-item prop="productTypeId" label="业态：">
          <el-select-tree
            ref="elSelectTree"
            v-model="searchForm.productTypeId"
            :options="productTypeList"
            :filterable="true"
            separator="-"
            :show-all-levels="true"
            :clearable="true"
            :props="{
              label: 'name',
              value: 'code'
            }"
            style="width: 100%;"
          />
        </el-form-item>
        <el-form-item prop="decorateTypeCode" label="装修类型：">
          <el-select v-model="searchForm.decorateTypeCode" filterable clearable style="width: 100%">
            <el-option
              v-for="opt in decorationList"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item prop="examineFlag" label="考核：">
          <el-select v-model="searchForm.assessTypeCode" filterable clearable style="width: 100%">
            <el-option
              v-for="opt in examineFlagList"
              :key="opt.value"
              :label="opt.label"
              :value="opt.value"
            />
          </el-select>
        </el-form-item>
        <el-form-item label="年度：" prop="statisticsYear">
          <el-date-picker
            v-model="searchForm.statisticsYear"
            :value="new Date()"
            type="year"
            placeholder="选择年"
            value-format="yyyy-MM-dd"
            format="yyyy"
            style="width: 100%"
            :clearable="false"
            :picker-options="{
              disabledDate: (date) => {
                // 2023 开始有数据的
                return new Date(2023, 0, 1) > date
              },
            }"
            @change="useBasic.yearChange(searchForm, $event)"
          />
        </el-form-item>
        <el-form-item prop="statisticsMonth" label="月份：">
          <el-date-picker
            v-model="searchForm.statisticsMonth"
            style="width: 100%;"
            type="monthrange"
            range-separator="至"
            start-placeholder="开始月份"
            end-placeholder="结束月份"
            value-format="yyyy-MM"
            format="MM月"
            :picker-options="{
              disabledDate: (date) => {
                const year = new Date(searchForm.statisticsYear).getFullYear()
                return new Date(year, 0, 1) > date || new Date(year + 1, -1, 1) < date
              },
            }"
          />
        </el-form-item>
      </template>
      <template #suffix>
        <div>
          <el-button :loading="loading" type="primary" icon="el-icon-search" @click="search">搜索</el-button>
          <el-button :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportChangeChart">导出</el-button>
        </div>
      </template>
    </UmSearchLayout>
    <div v-loading="loading" class="g-container">
      <el-row type="flex" justify="space-between">
        <el-radio-group v-model="currentTab">
          <el-radio-button v-for="item in tabList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
        <el-radio-group v-model="searchForm.type" class="f-flex-shrink0">
          <el-radio-button v-for="item in tabStatList" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
        </el-radio-group>
      </el-row>
      <CommonTitle title="总体满意度" padding-left="0" margin-bottom="0" />
      <!-- 满意度分析 -->
      <el-row v-for="item in list" :key="item[currentTab + 'Id']" :gutter="20" style="margin-bottom: 30px;">
        <el-col :span="8">
          <CommonTitle :title="item[currentTab + 'Name']" height="20" padding-left="0" :show-icon="false">
            <span style="color: #62DE85;">满意度: {{ item.satRate || '0%' }}</span>
          </CommonTitle>
          <TargetChart ref="targetChart" :key-id="currentTab==='point'?item.pointId:item.targetId" :chart-data="item.pieList" />
        </el-col>
        <el-col :span="8">
          <el-row type="flex" align="middle" style="margin-bottom: 20px;">
            <CommonTitle :title="`不满意${searchForm.type===1?'原因':'分类'}（Top5）`" height="20" padding-left="0" margin-bottom="0" :show-icon="false" />
            <span class="u-more" @click="watchMore(item[currentTab + 'Name'], item.pieList, item.dissatisfactionReason, item.disPercentTotal, true, item.targetId, item.pointId,item.disAllPersonNum)">查看更多<i class="el-icon-arrow-right" /></span>
          </el-row>
          <Quota :title="`不满意${searchForm.type===1?'原因':'分类'}`" :list="item.dissatisfactionReason" :type="searchForm.type" :point-id="item.pointId" :total="item.disPercentTotal" @goDetail="goDetail" />
        </el-col>
        <el-col :span="8">
          <el-row type="flex" align="middle" style="margin-bottom: 20px;">
            <CommonTitle :title="`满意${searchForm.type===1?'原因':'分类'}（Top5）`" height="20" padding-left="0" margin-bottom="0" :show-icon="false" />
            <span class="u-more" @click="watchMore(item[currentTab + 'Name'], item.pieList, item.satisfactionReason, item.percentTotal, false, item.targetId, item.pointId,item.allPersonNum)">查看更多<i class="el-icon-arrow-right" /></span>
          </el-row>
          <Quota :title="`满意${searchForm.type===1?'原因':'分类'}`" :list="item.satisfactionReason" :type="searchForm.type" :point-id="item.pointId" :total="item.percentTotal" @goDetail="goDetail" />
        </el-col>
      </el-row>
      <el-empty v-if="!list.length" :image="require('@/assets/plan/zanwu.png')" />
    </div>
    <!--满意/不满意原因弹框-->
    <DissatisfactionDialog
      :type="searchForm.type"
      :visible.sync="showDialog"
      :dis-data="disData"
      :evaluate-number="disOrAllCount"
      :evaluate-people-number="disOrAllPersonNum"
      :is-dis-reason="isDisReason"
      :search-form="cacheSearchParams"
      :current-tab="currentTab"
      :target-id="targetId"
      :point-id="pointId"
      :type-name="typeName"
    />
  </div>
</template>

<script>
import Quota from './components/Quota'
import TargetChart from './components/TargetChart'
import CommonTitle from '@/components/CommonTitle'
import { getPointList, getPointInfo, getTargetList, getTargetInfo } from '@/api/satisfaction/mydyl/analyse'
import DissatisfactionDialog from './components/dissatisfactionDialog.vue'
import { getProductType } from '@/api/common'
import { DECORATION_STATUS, EXAMINE_TYPE } from '@/enum'
import { UseBasicData } from '../hooks'
import cloneDeep from 'lodash/cloneDeep'
import exportData from '@/mixins/exportData'
import { exportTargetAnalysis, exportPointAnalysis } from '@/api/export'
import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'SatisfactionMydylAnalyse',
  components: { Quota, CommonTitle, TargetChart, DissatisfactionDialog },
  mixins: [exportData],
  data() {
    return {
      exportApi: exportTargetAnalysis,
      useBasic: new UseBasicData(),
      showDialog: false,
      targetList: [], // 指标集合
      pointList: [], // 触点集合
      decorationList: [ // 装修类型
        {
          label: '毛坯',
          value: DECORATION_STATUS.SIMPLE
        },
        {
          label: '精装',
          value: DECORATION_STATUS.FINE
        }
      ],
      tabList: [
        {
          label: '触点分析',
          value: 'point'
        },
        {
          label: '指标分析',
          value: 'target'
        }
      ],
      currentTab: 'point',
      productTypeList: [], // 业态数据
      questionList: [],
      // 统计口径
      examineFlagList: [
        { label: '年度考核', value: EXAMINE_TYPE.YEAR },
        { label: '半年度考核', value: EXAMINE_TYPE.HALF_YEAR },
        { label: '季度考核', value: EXAMINE_TYPE.QUARTER },
        { label: '月度考核', value: EXAMINE_TYPE.MONTH },
        { label: '仅监控，不考核', value: EXAMINE_TYPE.NO }
      ],
      projDto: {},
      searchForm: {
        decorateTypeCode: null,
        assessTypeCode: EXAMINE_TYPE.YEAR,
        statisticsYear: new Date(),
        statisticsMonth: [], // 月份多传，连续月份
        productTypeId: null,
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null,
        type: 1 // 1,按照原因统计2,按照分类统计
      },
      tabStatList: [{ label: '按原因统计', value: 1 }, { label: '按分类统计', value: 2 }],
      cacheSearchParams: {}, // 缓存搜索过的条件
      loading: false,
      isDisReason: false, // 是否是不满意原因 true 满意原因 false 不满意原因
      targetId: null, // 点击查看更多的时候，传递给子组件
      pointId: null, // 点击查看更多的时候，传递给子组件
      typeName: null, // 点击查看更多的时候，传递给子组件
      imageList: [], // 导出echart图片
      disOrAllCount: 0, // 不Or不满意总次数=评价总次数
      disOrAllPersonNum: 0, // 不Or不满意总人数=评价总人数
      disData: [] // 查看更多的数据
    }
  },
  computed: {
    ...mapGetters(['hasGuFenAuth']),
    list() {
      return this.currentTab === 'target' ? this.targetList : this.pointList
    }
  },
  watch: {
    async currentTab(v) {
      if (v === 'target' && !this.targetList.length) {
        this.search()
      }
    },
    'searchForm.type'(v) {
      this.search()
    }
  },
  async created() {
    this['user/getGuFenAuth']()
    try {
      const params = JSON.parse(this.$route.query.searchParams)
      this.projDto.areaCompanyId = params.areaCompanyId
      this.projDto.cityCompanyId = params.cityCompanyId
      this.projDto.projectId = params.projectId
      this.searchForm.decorateTypeCode = params.decorateTypeCode
      this.searchForm.assessTypeCode = params.assessTypeCode
      this.searchForm.statisticsYear = new Date(params.statisticsYear, 1)
      this.searchForm.statisticsMonth = params.statisticsMonth // 月份多传，连续月份
      this.searchForm.productTypeId = params.productTypeId
    } catch (error) {

    }
    this.getProductType()
    this.search()
  },
  mounted() {
  },
  methods: {
    ...mapActions(['user/getGuFenAuth']),
    formatExportParams() {
      this.exportApi = this.currentTab === 'point' ? exportPointAnalysis : exportTargetAnalysis
      const params = { ...this.searchForm, imageList: this.imageList }
      return this.useBasic.formatParams(params, this.projDto)
    },
    async search() {
      this.loading = true
      this.cacheSearchParams = cloneDeep(this.useBasic.formatParams(this.searchForm, this.projDto))
      try {
        const key = this.currentTab === 'point' ? 'pointList' : 'targetList'
        await this.getList(key)
        const length = this[key].length
        const promiseArr = []
        for (let index = 0; index < length; index++) {
          promiseArr.push(this.getInfo(this[key][index][this.currentTab + 'Id'], index, key))
        }
        await Promise.all(promiseArr)
      } catch (error) {
        console.log(error)
      } finally {
        this.loading = false
      }
    },
    // 获取业态
    getProductType() {
      return getProductType().then(res => {
        this.productTypeList = res.data || []
      })
    },
    // 获取触点集合
    getList(key) {
      const api = key === 'pointList' ? getPointList : getTargetList
      return api(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this[key] = res.data || []
      })
    },
    // 获取触点具体数据
    getInfo(id, index, key) {
      const api = key === 'pointList' ? getPointInfo : getTargetInfo
      return api({ ...this.useBasic.formatParams(this.searchForm, this.projDto), [key === 'pointList' ? 'pointId' : 'targetId']: id }).then(res => {
        const { pieList, dissatisfactionReason, satisfactionReason, dissatisfactionNum, satisfactionNum, dissatisfactionCountNum, satisfactionCountNum } = res.data
        this.$set(this[key], index, {
          ...this[key][index],
          pieList,
          dissatisfactionReason,
          satisfactionReason,
          percentTotal: satisfactionCountNum, // 满意总次数
          disPercentTotal: dissatisfactionCountNum, // 不满意总次数
          allPersonNum: satisfactionNum, // 满意总人数
          disAllPersonNum: dissatisfactionNum, // 不满意总人数
          [key === 'pointList' ? 'pointId' : 'targetId']: id
        })
      })
    },
    watchMore(typeName, pieList, data, disOrAllCount, isDisReason, targetId, pointId, disOrAllPersonNum) {
      this.typeName = typeName
      this.targetId = targetId
      this.pointId = pointId
      this.isDisReason = isDisReason
      this.disOrAllPersonNum = disOrAllPersonNum // 不Or不满意总人数=评价总人数
      this.disOrAllCount = disOrAllCount
      this.disData = data
      this.showDialog = true
    },
    // isDis 是否是不满意原因
    goDetail(data, pointId, isDis = false) {
      const params = this.cacheSearchParams
      const statisticsMonth = params.statisticsMonth.split(',').filter(item => item)
      this.$router.push('/satisfaction/mydyl/questionDetail?searchParams=' + JSON.stringify({
        ...params,
        type: this.searchForm.type,
        reasonId: data.reasonId,
        pointId,
        isDis: +isDis,
        exportSourceTypeCode: this.currentTab === 'point' ? 1 : 2, // 导出来源code 总体满意度_触点分析:1 总体满意度_指标分析:2
        statisticsMonth: statisticsMonth.length ? [params.statisticsYear + '-' + statisticsMonth[0], params.statisticsYear + '-' + statisticsMonth[statisticsMonth.length - 1]] : []
      }))
    },
    async exportChangeChart() {
      if (Array.isArray(this.$refs.targetChart) && this.$refs.targetChart.length > 0) {
        try {
          this.imageList = await Promise.all(this.$refs.targetChart.map(item => item.exportChart()))
          console.log(this.imageList, 'this.imageList')
          this.exportData()
        } catch (e) {
          console.log(e)
        }
      } else {
        console.log('数据为空')
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  // height: calc(100vh - 76px);
  background: #FFFFFF;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  border-radius: 12px;
  padding: 20px;
  overflow-y: auto;
  margin-top: 20px;
  .mb-20 {
    margin-bottom: 20px;
  }
  .mb-36 {
    margin-bottom: 36px;
  }
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  .u-more {
    display: flex;
    color: #999999;
    flex-shrink: 0;
    cursor: pointer;
    &:hover {
      color: $--color-primary;
    }
    i {
      margin-left: 4px;
    }
  }
}
</style>

