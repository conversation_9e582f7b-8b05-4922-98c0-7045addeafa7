<!--不满意原因弹框-->
<template>
  <el-dialog
    :title="`${isDisReason ? `不满意${type===1?'原因':'分类'}` : `满意${type===1?'原因':'分类'}`}`"
    :visible.sync="innerVisible"
    width="950px"
    center
    @close="cancel"
  >
    <div class="flex flexBetween">
      <el-radio-group v-if="type===1" v-model="currentTab" class="margin-b-20">
        <el-radio-button v-for="item in isDisReason ? tabList : tabList1" :key="item.value" :label="item.value">{{ item.label }}</el-radio-button>
      </el-radio-group>
      <div v-if="currentTab==1"><el-button :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button></div>
    </div>
    <div v-if="currentTab===0" class="hsl">
      <div class="hs-nei">
        <span>评价次数：{{ evaluateNumber }}</span>
        <span style="margin-left: 10px;">评价人数：{{ evaluatePeopleNumber }}</span>
      </div>
    </div>
    <um-table-full
      ref="table"
      v-loading="loading"
      scroll
      :data="currentTab === 0 ? disData : tableData"
    >
      <el-table-column
        type="index"
        label="序号"
        width="70"
      />
      <el-table-column
        prop="name"
        :label="`${isDisReason ? `不满意${type===1?'原因':'分类'}` : `满意${type===1?'原因':'分类'}`}`"
        width="200"
      >
        <template slot-scope="{row}">
          <um-tool-tips v-if="type===1" :content="row.name" :row="2" />
          <um-tool-tips v-if="type===2" :content="row.categoryName" :row="2" />
        </template>
      </el-table-column>
      <el-table-column
        v-if="type===1"
        prop="categoryName"
        label="所属分类"
        show-overflow-tooltip
        :formatter="$formatterTable"
        min-width="140"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="count"
        label="选择次数"
        min-width="90"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="personNum"
        label="选择人数"
        min-width="90"
      />
      <el-table-column
        v-if="currentTab==0"
        prop="peopleRate"
        label="人数占比"
        min-width="140"
      >
        <span slot-scope="{ row }" class="primary">{{ row.peopleRate }}</span>
      </el-table-column>
      <el-table-column
        v-if="currentTab==0"
        prop="countRate"
        label="次数占比"
        min-width="140"
      >
        <span slot-scope="{ row }" class="primary">{{ row.countRate }}</span>
      </el-table-column>
    </um-table-full>
  </el-dialog>
</template>
<script>
import { getOtherReasonList } from '@/api/satisfaction/mydyl/targetAnalyse'
import exportData from '@/mixins/exportData'
import { exportTargetPointOtherReason } from '@/api/export'
export default {
  name: 'DissatisfactionDialog',
  mixins: [exportData],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    // 是否不满意原因
    isDisReason: {
      type: Boolean,
      default: false
    },
    disData: {
      type: Array,
      default: () => []
    },
    searchForm: {
      type: Object,
      default: () => {}
    },
    pointId: {
      type: String,
      default: null
    },
    typeName: {
      type: String,
      default: ''
    },
    type: { // 1:原因  2:分类
      type: Number,
      default: 1
    },
    evaluatePeopleNumber: { // 评价总人数
      type: [String, Number],
      default: 0
    },
    evaluateNumber: { // 评价总次数
      type: [String, Number],
      default: 0
    }
  },
  data() {
    return {
      exportApi: exportTargetPointOtherReason,
      currentTab: 0,
      loading: false,
      tabList: [
        {
          label: '不满意原因统计',
          value: 0
        },
        {
          label: '其他不满意原因',
          value: 1
        }
      ],
      tabList1: [
        {
          label: '满意原因统计',
          value: 0
        },
        {
          label: '其它满意原因',
          value: 1
        }
      ],
      tableData: []
    }
  },
  computed: {
    innerVisible: {
      get() {
        if (this.visible) {
          this.getOtherReasonList()
        }
        return this.visible
      },
      set(val) {
        if (!val) this.currentTab = 0
        this.$emit('update:visible', val)
      }
    }
  },
  methods: {
    cancel() {
      this.innerVisible = false
    },
    // 获取其它原因
    getOtherReasonList() {
      this.tableData = []
      this.loading = true
      getOtherReasonList({ ...this.searchForm, pointId: this.pointId }).then(res => {
        const { otherDissatisfactionReasonList, otherSatisfactionReasonList } = res.data
        this.tableData = (this.isDisReason ? (otherDissatisfactionReasonList || []) : (otherSatisfactionReasonList || [])).map(item => {
          return {
            name: item.reasonName,
            categoryName: item.categoryName
          }
        })
      }).finally(() => {
        this.loading = false
      })
    },
    formatExportParams() {
      const params = { ...this.searchForm, pointId: this.pointId, name: this.typeName, satisfactionFlag: this.isDisReason ? 0 : 1 }
      return params
    }
  }
}
</script>
<style lang="scss" scoped>
.hsl {
  width: 100%;
  padding: 6px 0;
  background: #E5EEFA;
  border-radius: 4px;
  margin-bottom: 12px;
  .hs-nei {
    padding-left: 14px;
    font-size: 12px;
    color: #005DCF;
    font-weight: 500;
    border-left: 4px solid #005DCF;
  }
}
</style>
