<template>
  <div class="chart">
    <div ref="chart" class="chart-content" style="width: 400px;"/>
  </div>
</template>

<script>
const echarts = window.echarts
const COLORS = ['#3D80FC', '#62DE85', '#F7C739', '#F3653C', '#7262DE', '#E7CAAF', '#263EDE', '#DB2F8F', '#B63CF3', '#62D1DE', '#e6d15e', '#aae850', '#f01742', '#ccdbc4', '#fa2670', '#f3914a', '#3c16e2', '#2083e1', '#bb1dfe', '#6c3d2d']
export default {
  name: 'CirclePie',
  props: {
    chartData: {
      type: Array,
      default: () => []
    },
    total: {
      type: Number,
      default: 0
    },
    keyId: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      COLORS,
      chart: null
    }
  },
  watch: {
    chartData: {
      immediate: true,
      deep: true,
      handler(v) {
        if (this.$refs.chart) {
          this.initChart()
        }
      }
    }
  },
  created() {

  },
  mounted() {
    this.initChart()
    window.addEventListener('resize', this.resize)
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.chart && this.chart.resize()
    },
    initChart() {
      if (this.chart) {
        this.chart.dispose() // 销毁旧的 chart 实例
        this.chart = null
      }
      this.chart = echarts.init(this.$refs.chart)
      const option = {
        legend: {
          bottom: 'left',
          selected: {
            '未回收': false
          }
        },
        grid: {
          bottom: '5%',
          left: 0,
          right: 0
        },
        series: [
          {
            type: 'pie',
            radius: '42%',
            color: COLORS,
            data: this.chartData.map((item, index) => {
              return {
                value: item.count,
                name: item.name,
                label: {
                  formatter: [
                    '{b|{b}}{d|{d}%}',
                    '{hr|}',
                    '{countHead|数量}{c|{c}}'
                  ].join('\n'),
                  backgroundColor: '#fff',
                  borderColor: COLORS[index],
                  borderWidth: 0.5,
                  borderRadius: 2,
                  rich: {
                    b: {
                      width: 30,
                      height: 16,
                      color: '#fff',
                      align: 'center',
                      backgroundColor: COLORS[index],
                      fontSize: 10,
                      borderRadius: [2, 0, 0, 0]
                    },
                    countHead: {
                      width: 30,
                      color: '#fff',
                      align: 'center',
                      fontSize: 10,
                      backgroundColor: COLORS[index],
                      borderRadius: [0, 0, 0, 2],
                      height: 16
                    },
                    hr: {
                      borderColor: COLORS[index],
                      width: '100%',
                      borderWidth: 0.5,
                      height: 0
                    },
                    d: {
                      width: 36,
                      align: 'right',
                      color: COLORS[index],
                      fontSize: 10,
                      height: 16,
                      padding: [0, 2, 0, 0]
                    },
                    c: {
                      width: 36,
                      align: 'right',
                      color: COLORS[index],
                      fontSize: 10,
                      height: 16,
                      padding: [0, 2, 0, 0]
                    }
                  }
                }
              }
            })
          }
        ]
      }
      this.chart && this.chart.setOption(option)
    },
    async exportChart() {
      if (this.chart) {
        try {
          // 获取 Base64 图片数据
          const base64Image = this.chart.getDataURL({
            type: 'png',
            pixelRatio: 3,
            backgroundColor: '#fff'
          })
          return { key: this.keyId, image: base64Image }
        } catch (error) {
          return false
        }
      } else {
        return false
      }
    }
  }
}
</script>

<style lang="scss" scoped>
  .chart {
      width: 100%;
      height: 240px;
      padding-bottom: 20px;
      border-radius: 8px;
      display: flex;
      align-items: center;
      justify-content: center;
      background: #F5F7FA;
      &-content {
          width: 100%;
          height: 220px;
      }
  }
</style>

