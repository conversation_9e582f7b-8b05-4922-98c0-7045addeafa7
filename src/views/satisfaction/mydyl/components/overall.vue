<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form ref="searchForm" :model="searchForm" label-width="70px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="areaCompanyId" label="区域">
              <UmBusCommon v-model="projDto" value-key="areaCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="cityCompanyId" label="城市">
              <UmBusCommon v-model="projDto" value-key="cityCompanyId" :area-company-id="projDto.areaCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="projectId" label="项目">
              <UmBusCommon v-model="projDto" value-key="projectId" :area-company-id="projDto.areaCompanyId" :city-company-id="projDto.cityCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="productTypeId" label="业态">
              <!-- <el-select-tree
                ref="elSelectTree"
                v-model="searchForm.productTypeId"
                :options="useBasic.productTypeList"
                :filterable="true"
                separator="-"
                :show-all-levels="true"
                :clearable="true"
                :props="{
                  label: 'name',
                  value: 'code'
                }"
                style="width: 100%;"
              /> -->
              <el-select v-model="searchForm.productTypeId" clearable filterable style="width: 100%;">
                <el-option
                  v-for="opt in useBasic.productTypeList"
                  :key="opt.code"
                  :label="opt.name"
                  :value="opt.code"
                />
              </el-select>
            </el-form-item>
          </el-col>

          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="decorateTypeCode" label="装修类型">
              <el-select v-model="searchForm.decorateTypeCode" filterable clearable style="width: 100%">
                <el-option
                  v-for="opt in decorationList"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item label="年度" prop="statisticsYear">
              <el-date-picker
                v-model="searchForm.statisticsYear"
                :value="new Date()"
                type="year"
                placeholder="选择年"
                value-format="yyyy-MM-dd"
                format="yyyy"
                style="width: 100%"
                :editable="false"
                :clearable="false"
                :picker-options="{
                  disabledDate: (date) => {
                    // 2023 开始有数据的, 当前年份之后的年也不能选择
                    return new Date(2023, 0, 1) > date || new Date() < date
                  },
                }"
                @change="useBasic.yearChange(searchForm,$event)"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="statisticsMonth" label="月份">
              <el-date-picker
                v-model="searchForm.statisticsMonth"
                style="width: 100%;"
                type="monthrange"
                range-separator="至"
                start-placeholder="开始月份"
                end-placeholder="结束月份"
                value-format="yyyy-MM"
                format="MM月"
                :clearable="false"
                :editable="false"
                :picker-options="pickerOptions"
                @blur="pickStartMonth = null"
              />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="assessTypeCode" label="统计口径">
              <el-select v-model="searchForm.assessTypeCode" filterable clearable style="width: 100%">
                <el-option
                  v-for="opt in examineFlagList"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <!-- <el-col v-if="hasWeightPermission" :span="8" :md="8" :xl="6">
            <el-form-item prop="weightCode" label="职能权重">
              <el-select v-model="searchForm.weightCode" filterable style="width: 100%">
                <el-option
                  v-for="opt in weightCodeList"
                  :key="opt.value"
                  :label="opt.label"
                  :value="opt.value"
                />
              </el-select>
            </el-form-item>
          </el-col> -->
          <el-col :span="8" :md="8" :xl="6" class="fr text-right" style="margin-bottom: 20px;">
            <el-button :loading="loading" type="primary" icon="el-icon-search" @click="getData">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <el-card v-loading="loading">
      <div class="f-flex-jcsb" style="overflow-x: auto;">
        <div class="overall-left margin-r-20">
          <div class="overallSat f-flex">
            <div class="overallSat-item" style="padding: 0">
              <div class="overallSat-value margin-b-10"><NumberFormat :num="overallSat.sat" /></div>
              <div class="overallSat-title margin-b-10">总体满意度</div>
              <div class="overallSat-title">
                <span>较{{ cacheSearchParams.statisticsYear - 1 }} </span>
                <template v-if="isUp(overallSat.satYoy) !== 'none'">
                  <img v-if="isUp(overallSat.satYoy)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                  <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
                </template>
                <span> {{ absNumber(overallSat.satYoy) }}%</span>
              </div>
            </div>
            <div class="overallSat-item" style="padding: 0">
              <div class="name"><NumberFormat :num="overallSat.satGoal" /></div>
              <div class="overallSat-title">满意度目标</div>
            </div>
            <div v-if="overallSat.satGoal !== null && overallSat.satGoal !== undefined" class="overallSat-item" style="padding: 0">
              <div class="name">
                <template v-if="isUp(diffScore) !== 'none'">
                  <img v-if="isUp(diffScore)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                  <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
                </template>
                <NumberFormat :num="diffScore" />
              </div>
              <div class="overallSat-title">较目标值</div>
            </div>
            <div v-for="(item, index) in scoreMapList.slice(0, 1)" :key="index" class="overallSat-item f-flex-ajc flex-col" style="min-width: 150px;">
              <div class="name">{{ item.score }}/<NumberFormat :num="item.sat" /></div>
              <div class="value">所处分位/满意度</div>
            </div>
          </div>
        </div>
        <div v-if="pointTypeSatList.length" class="overall-right">
          <div class="overallSat f-flex-acjcsb">
            <div v-for="(item, index) in pointTypeSatList" :key="index" class="overallSat-item f-flex-ajc flex-col">
              <div class="value margin-b-8 name m-t-0"><NumberFormat :num="item.sat" /></div>
              <div class="value margin-b-10 ">{{ item.pointTypeName }}</div>
              <div class="value">
                <span>环比</span>
                <template v-if="isUp(item.satMom) !== 'none'">
                  <img v-if="isUp(item.satMom)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                  <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
                </template>
                <span> {{ absNumber(item.satMom) }}%</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </el-card>
    <el-card v-loading="loading">
      <el-row type="flex" align="middle" class="margin-b-20">
        <CommonHeader title="总体满意度" padding-left="0" margin-bottom="0" height="20" />
        <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 0" icon="el-icon-upload2" type="primary" @click="beforeExportData(0)">导出</el-button>
      </el-row>
      <RadarEchart :data="allEchartData" :tips="allTipsList" need-click @click="barJumpAnalyse" />
    </el-card>
    <el-card v-loading="loading">
      <el-row type="flex" align="middle" class="margin-b-20">
        <CommonHeader padding-left="0" margin-bottom="0" height="20">
          <template slot="title">
            <div class="head-title">触点满意度</div>
            <el-tooltip class="item" effect="dark" content="Top Left 提示文字" placement="right">
              <div slot="content">
                <div v-for="(value, key) in tips" :key="key" class="u-chartTip">
                  <span>{{ key }}:</span>
                  <span>{{ value }}</span>
                </div>
              </div>
              <i class="el-icon-question" style="cursor: pointer;color: #8B8B7A" />
            </el-tooltip>
          </template>
        </CommonHeader>
        <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 1" icon="el-icon-upload2" type="primary" @click="beforeExportData(1)">导出</el-button>
      </el-row>
      <RadarEchart :data="pointEchartData" :tips="pointTipsList" need-click @click="barJumpPointAnalyse" />
    </el-card>
    <el-card v-loading="loading">
      <el-row type="flex" align="middle" class="margin-b-20">
        <CommonHeader title="指标" padding-left="0" margin-bottom="0" height="20">
          <template slot="title">
            <div class="head-title mr20">指标</div>
            <el-radio-group v-model="sortType">
              <el-radio :label="0">按满意度排序</el-radio>
              <el-radio :label="1">按指标排序</el-radio>
            </el-radio-group>
          </template>
        </CommonHeader>
        <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 2" icon="el-icon-upload2" type="primary" @click="beforeExportData(2)">导出</el-button>
      </el-row>
      <RadarEchart :data="targetEchartData" :tips="targetTipsList" need-click @click="barJumpTargetAnalyse" />
    </el-card>
  </div>
</template>

<script>
import RadarEchart from './RadarEchart.vue'
import CommonHeader from '@/components/CommonTitle/index.vue'
import { getGeneralOverView, getSatOverview, getPointSatOverview, getTargetSatOverview } from '@/api/satisfaction/mydyl'
import { UseBasicData, tips, getDefaultMonthRange } from '../hooks'
import { DECORATION_STATUS, EXAMINE_TYPE } from '@/enum'
import BigJs from 'big.js'
import NumberFormat from './NumberFormat.vue'
import { cloneDeep } from 'lodash'
import { flattenTree } from '@/utils'
import exportData from '@/mixins/exportData'
import { exportSatOverviewData, exportPointSatOverviewData, exportTargetSatOverviewData } from '@/api/export'
import { getTargetTree } from '@/api/base/indicator'
import { mapActions, mapGetters } from 'vuex'
const shorts = [
  { label: '一季度', sMonth: 0, eMonth: 2 },
  { label: '二季度', sMonth: 3, eMonth: 5 },
  { label: '三季度', sMonth: 6, eMonth: 8 },
  { label: '四季度', sMonth: 9, eMonth: 11 },
  { label: '上半年', sMonth: 0, eMonth: 5 },
  { label: '下半年', sMonth: 6, eMonth: 11 }
]
export default {
  name: 'SatisfactionMydylOverall',
  components: { CommonHeader, RadarEchart, NumberFormat },
  mixins: [exportData],
  props: {
    hasWeightPermission: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      useBasic: new UseBasicData(),
      tips,
      pickerOptions: {
        disabledDate: (date) => {
          const searchDate = new Date(this.searchForm.statisticsYear)
          const year = searchDate.getFullYear()
          const month = new Date().getMonth() + 1
          // 如果面板打开，并且选择了月份
          // 如果是1月份或者12月份, 或者没有选择任何月份
          if (this.pickStartMonth === 1 || this.pickStartMonth === 12 || !this.pickStartMonth) {
            if (year === new Date().getFullYear()) { // 当前年份，则只能选择1月份到当前月份
              return new Date(year, 0, 1) > date || new Date() < date
            } else { // 不是当前年份，则只能选择本年1月份到12月份
              return new Date(year, 0, 1) > date || new Date(year + 1, -1, 1) < date
            }
          } else if ([4, 7, 10].includes(this.pickStartMonth)) { // 如果是4,7,10月份，则可以选择前一个月份和后两个
            if (year === new Date().getFullYear()) { // 当前年份，当前季度开始月份的前一个月，和后两个月，不能超过当前月份
              if (this.pickStartMonth === 7) { // 7月份可以选择到下半年
                return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, month - 1, 1) < date
              }
              let endMonth = this.pickStartMonth + 2 > month ? this.pickStartMonth + 1 : this.pickStartMonth + 2
              endMonth = endMonth > month ? endMonth - 1 : endMonth
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, endMonth - 1, 1) < date
            } else { // 不是当前年份，当前季度开始月份的前一个月，和后两个月
              if (this.pickStartMonth === 7) { // 7月份可以选择到下半年
                return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, 11, 1) < date
              }
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth + 1, 1) < date
            }
          } else if (this.pickStartMonth !== 12) { // 如果没选择到12月份，则只能选择前后跨度一个月
            if (year === new Date().getFullYear()) { // 当前年份，不能超过当前月份
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth < month ? this.pickStartMonth : this.pickStartMonth - 1, 1) < date
            } else { // 不是当前年份，当前月份前后月份
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth, 1) < date
            }
          }
        },
        onPick: ({ maxDate, minDate }) => {
          this.pickStartMonth = new Date(minDate).getMonth() + 1
        },
        shortcuts: []
      },
      pickStartMonth: null, // 选择开始的月份
      searchForm: {
        areaCompanyId: '',
        cityCompanyId: '',
        projectId: '',
        productTypeId: '',
        decorateTypeCode: null, // 装修标准
        statisticsYear: new Date(),
        statisticsMonth: getDefaultMonthRange(), // 月份多传，连续月份
        assessTypeCode: null, // 考核类型（统计口径）
        weightCode: 1 // 职能加权：0-不加权，1-加权（默认）
      },
      projDto: {},
      allEchartData: {}, // 总体满意度
      allTipsList: [], // 总体满意度底部展示信息
      pointEchartData: {}, // 触点满意度
      pointTipsList: [], // 触点满意度底部展示信息
      targetEchartData: {}, // 指标满意度
      targetTipsList: [], // 指标满意度底部展示信息
      weightCodeList: [ // 职能权重
        { label: '不加权', value: 0 },
        { label: '加权', value: 1 }
      ],
      decorationList: [ // 装修类型
        {
          label: '毛坯',
          value: DECORATION_STATUS.SIMPLE
        },
        {
          label: '精装',
          value: DECORATION_STATUS.FINE
        }
      ],
      // 统计口径
      examineFlagList: [
        { label: '年度考核', value: EXAMINE_TYPE.YEAR },
        { label: '半年度考核', value: EXAMINE_TYPE.HALF_YEAR },
        { label: '季度考核', value: EXAMINE_TYPE.QUARTER },
        { label: '月度考核', value: EXAMINE_TYPE.MONTH },
        { label: '仅监控，不考核', value: EXAMINE_TYPE.NO }
      ],
      overallSat: {},
      scoreMapList: [],
      pointTypeSatList: [],
      cacheSearchParams: {
        statisticsYear: new Date().getFullYear()
      }, // 缓存每次点击搜索后的条件
      allStatisticsList: [], // 总体满意度图表数据
      pointDataList: [], // 触点满意度图表数据
      updateTime: null,
      exportIndex: null,
      sortType: 0,
      loading: false,
      targetDataList: [], // 指标图表数据
      sortTargetDataList: [], // 排序后的指标图表数据
      targetTreeIds: []// 所有的指标数据
    }
  },
  computed: {
    ...mapGetters(['authOrganTree', 'hasGuFenAuth']),
    diffScore() {
      return parseFloat(new BigJs(this.overallSat.sat || 0).minus(new BigJs(this.overallSat.satGoal || 0)))
    }
  },
  watch: {
    async sortType(v) {
      if (v === 0) {
        this.sortChartData()
      }
      if (v === 1) {
        this.getTargetTree()
      }
    },
    'searchForm.statisticsYear': {
      immediate: true,
      handler(v) {
        let currentMonth = null
        if (new Date().getFullYear() === new Date(this.searchForm.statisticsYear).getFullYear()) {
          currentMonth = new Date().getMonth()
        } else {
          currentMonth = 11
        }
        this.pickerOptions.shortcuts = shorts.filter(item => currentMonth >= item.sMonth).map(item => {
          return {
            text: item.label,
            onClick: (picker) => {
              const currentDate = new Date()
              const generateShorts = (sMonth, eMonth, picker) => {
                const year = new Date(this.searchForm.statisticsYear).getFullYear()
                if (year === currentDate.getFullYear()) { // 如果是当前年，则结束月份不能大于当前月份
                  picker.$emit('pick', [new Date(year, sMonth, 1), new Date(year, currentDate.getMonth() < eMonth ? currentDate.getMonth() : eMonth, 1)])
                } else {
                  picker.$emit('pick', [new Date(year, sMonth, 1), new Date(year, eMonth, 1)])
                }
              }
              generateShorts(item.sMonth, item.eMonth, picker)
            }
          }
        })
      }
    }
  },
  activated() {
    this.$parent.setUpdateTime(this.updateTime)
  },
  created() {
    this['user/getGuFenAuth']()
    this['user/getAuthOrganTree']()
    this.useBasic.getProductType()
    this.getData()
  },
  methods: {
    ...mapActions(['user/getAuthOrganTree', 'user/getGuFenAuth']),
    // 取绝对值
    absNumber(num) {
      if (num === null || num === undefined) {
        return '--'
      }
      return Math.abs(num)
    },
    beforeExportData(index) {
      this.exportIndex = index
      switch (index) {
        case 0: this.exportApi = exportSatOverviewData; break
        case 1: this.exportApi = exportPointSatOverviewData; break
        case 2: this.exportApi = exportTargetSatOverviewData; break
      }
      this.exportData()
    },
    formatExportParams() {
      return this.useBasic.formatParams(this.searchForm, this.projDto)
    },
    // 统一获取数据
    getData() {
      // const load = this.$load()
      this.loading = true
      this.cacheSearchParams = cloneDeep(this.useBasic.formatParams(this.searchForm, this.projDto))
      Promise.all([this.getGeneralOverView(), this.getSatOverview(), this.getPointSatOverview(), this.getTargetSatOverview()]).then(res => {
      }).finally(() => (this.loading = false))
    },
    // 获取总体概览头部数据
    getGeneralOverView() {
      return getGeneralOverView(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        const { overallSat, pointTypeSatList, updateTime } = res.data || {}
        this.overallSat = overallSat || {}
        this.scoreMapList = overallSat?.scoreMapList || []
        this.pointTypeSatList = pointTypeSatList || []
        this.updateTime = updateTime
        this.$parent.setUpdateTime(updateTime)
      })
    },
    // 判断用户是否有查看数据权限，跟上面带权限的组织树比较
    checkAuth() {
      if (this.hasGuFenAuth) return true // 有股份权限，可以不走下面校验
      const { projectId, cityCompanyId, areaCompanyId } = this.cacheSearchParams
      if (!areaCompanyId && !cityCompanyId && !projectId) {
        this.$message.warning('请选择区域搜索条件搜索后重新尝试！')
        return false
      }
      if (projectId) {
        if (this.authOrganTree.findIndex(item => item.code === projectId) === -1) {
          this.$message.warning('您没有该项目权限，请尝试切换搜索条件搜索后重新尝试！')
          return false
        }
        return true
      } else if (cityCompanyId) {
        if (this.authOrganTree.findIndex(item => item.code === cityCompanyId) === -1) {
          this.$message.warning('您没有该城市权限，请尝试切换搜索条件搜索后重新尝试！')
          return false
        }
        return true
      } else if (areaCompanyId) {
        if (this.authOrganTree.findIndex(item => item.code === areaCompanyId) === -1) {
          this.$message.warning('您没有该区域权限，请尝试切换搜索条件搜索后重新尝试！')
          return false
        }

        return true
      }
      return true
    },
    // 获取总体满意度
    getSatOverview() {
      return getSatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this.allStatisticsList = res.data.dataList || []
        const scoreMapList = res.data.scoreMapList || []
        // 如果指定颜色不够，添加随机颜色
        if (this.useBasic.markLineColors.length < scoreMapList.length) {
          this.useBasic.pushMarkLineColors()
        }
        const tips = scoreMapList.map((item, index) => {
          return {
            title: item.score + '分位',
            color: this.useBasic.markLineColors[index],
            type: 'line'
          }
        })
        this.allTipsList = [
          {
            title: '总体满意度',
            color: '#005DCF',
            type: 'block' // block  line  dot
          }, ...tips, {
            title: '满意度目标',
            color: '#36C35A',
            type: 'line'
          }
        ]
        // 格式化分位线
        let allStatisticsMarkLine = scoreMapList.map((item, index) => {
          return {
            name: item.score,
            yAxis: parseInt(item.sat),
            label: {
              show: true,
              position: 'end',
              color: this.useBasic.markLineColors[index],
              formatter: function(params) {
                return `${params.value}%`
              }
            },
            lineStyle: {
              color: this.useBasic.markLineColors[index]
            }
          }
        })
        // 满意度目标线
        allStatisticsMarkLine = [...allStatisticsMarkLine, {
          name: '满意度目标',
          yAxis: res.data.satGoal || '',
          label: {
            show: true,
            position: 'end',
            color: '#36C35A',
            formatter: function(params) {
              return `${params.value}%`
            }
          },
          lineStyle: {
            color: '#36C35A'
          }
        }]
        this.allEchartData = {
          color: ['#005DCF'],
          tooltip: {
            className: 'echarts-tooltip',
            trigger: 'axis',
            formatter: (params) => {
              const { name, seriesName, value, dataIndex } = params[0]
              return `
                <div>${name}</div>
                <div><span>${seriesName}：${this.$formatterTable(value)}%</span></div>
                <div><span>总样框：${this.$formatterTable(this.allStatisticsList[dataIndex].unWeightFrameNum)}</span></div>
                <div><span>加权样框：${this.$formatterTable(this.allStatisticsList[dataIndex].totalSendNum)}</span></div>
                <div><span>样本量：${this.$formatterTable(this.allStatisticsList[dataIndex].recycleNum)}</span></div>
              `
            }
          },
          grid: {
            left: '20px',
            right: '60px',
            bottom: '5%',
            top: '10%',
            containLabel: true
          },
          xAxis: {
            triggerEvent: true,
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            data: this.allStatisticsList.map(item => item.month)
          },
          yAxis: [
            {
              type: 'value',
              // name: '%',
              min: 0,
              max: 100,
              splitLine: {
                lineStyle: {
                  type: 'dashed'// 虚线
                }
              },
              axisLine: {
                show: true
              },
              axisLabel: {
                formatter: '{value}'
              }
            }
          ],
          series: [
            {
              name: '总体满意度',
              type: 'bar',
              label: {
                show: true,
                position: 'top',
                formatter({ value }) {
                  return value + '%'
                }
              },
              data: this.allStatisticsList.map(item => item.sat),
              markLine: {
                symbol: ['none', 'none'],
                lineStyle: {
                  type: 'solid'
                },
                data: allStatisticsMarkLine
              },
              barMaxWidth: this.useBasic.barWidth
            }
          ]
        }
      })
    },
    // 获取触点满意度概览图表
    getPointSatOverview() {
      return getPointSatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this.pointDataList = res.data || []
        this.pointTipsList = [
          {
            title: '满意度',
            color: '#005DCF',
            type: 'block' // block  line  dot
          }, {
            title: '满意度目标',
            color: '#36C35A',
            type: 'dot'
          }
        ]
        this.pointEchartData = {
          color: ['#005DCF', '#33C5E2', '#36C35A'],
          tooltip: {
            className: 'echarts-tooltip',
            trigger: 'axis',
            formatter: (params) => {
              const { name, seriesName, value, dataIndex } = params[0]
              return `
                <div>${name}</div>
                <div><span>${seriesName}：${this.$formatterTable(value)}%</span></div>
                <div><span>满意度目标：${this.$formatterTable(this.pointDataList[dataIndex].satGoal)}%</span></div>
                <div><span>总样框：${this.$formatterTable(this.pointDataList[dataIndex].unWeightFrameNum)}</span></div>
                <div><span>加权样框：${this.$formatterTable(this.pointDataList[dataIndex].totalSendNum)}</span></div>
                <div><span>样本量：${this.$formatterTable(this.pointDataList[dataIndex].recycleNum)}</span></div>
              `
            }
          },
          grid: {
            left: '20px',
            right: '20px',
            bottom: '5%',
            top: '10%',
            containLabel: true
          },
          xAxis: {
            triggerEvent: true,
            type: 'category',
            axisTick: {
              alignWithLabel: true
            },
            axisLabel: {
              interval: 0,
              formatter: function(params) {
                if (params.length < 6) {
                  return params
                } else if (params.length < 11) {
                  return params.substr(0, 5) + '\n' + params.substr(5)
                } else {
                  return params.substr(0, 5) + '\n' + params.substr(5, 4) + '...'
                }
              }
            },
            data: this.pointDataList.map(item => item.pointName)
          },
          yAxis: [
            {
              type: 'value',
              // name: '（分）',
              min: 0,
              max: 100,
              axisLine: {
                show: true
              },
              axisLabel: {
                formatter: '{value}'
              }
            }
          ],
          series: [
            {
              name: '满意度',
              type: 'bar',
              label: {
                show: true,
                position: 'top',
                formatter({ value }) {
                  return value + '%'
                }
              },
              data: this.pointDataList.map(item => item.sat),
              barMaxWidth: this.useBasic.barWidth
            },
            {
              symbolSize: 8,
              symbol: 'circle',
              data: this.pointDataList.map(item => item.satGoal),
              type: 'scatter',
              name: '满意度目标',
              itemStyle: {
                normal: {
                  color: '#36C35A',
                  borderColor: '#fff',
                  borderWidth: 2,
                  shadowColor: '#fff',
                  shadowBlur: 10
                }
              }
            }
          ]
        }
      })
    },
    // 获取指标满意度概览图表
    getTargetSatOverview() {
      getTargetSatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        const arr = res.data || []
        this.targetDataList = arr
        this.sortTargetDataList = arr
        this.sortChartData()
      })
    },
    // 获取所有指标，并且按照顺序展开
    getTargetTree() {
      const load = this.$load()
      getTargetTree({ disableFlag: true }).then(res => {
        const arr = flattenTree(res.data || [], 'targetChildren').map(item => item.tgtTargetId)
        this.targetTreeIds = arr
        this.sortChartData(this.targetTreeIds)
      }).finally(() => load.close())
    },
    // 根据满意度或者指标排序
    sortChartData(targetTreeIds = []) {
      const arr = []
      if (targetTreeIds.length) {
        targetTreeIds.forEach(item => {
          const index = this.targetDataList.findIndex(_item => _item.targetId === item)
          if (index !== -1) {
            arr.push(this.targetDataList[index])
          }
        })
      } else {
        arr.push(...this.targetDataList)
      }
      this.sortTargetDataList = arr
      this.targetTipsList = [
        {
          title: '满意度',
          color: '#005DCF',
          type: 'block' // block  line  dot
        }
      ]
      this.targetEchartData = {
        color: ['#005DCF', '#33C5E2', '#FF9645'],
        tooltip: {
          className: 'echarts-tooltip',
          trigger: 'axis',
          formatter: (params) => {
            const { name, seriesName, value, dataIndex } = params[0]
            return `
                <div>${name}</div>
                <div><span>${seriesName}：${this.$formatterTable(value)}%</span></div>
                <div><span>总样框：${this.$formatterTable(arr[dataIndex].unWeightFrameNum)}</span></div>
                <div><span>加权样框：${this.$formatterTable(arr[dataIndex].totalSendNum)}</span></div>
                <div><span>样本量：${this.$formatterTable(arr[dataIndex].recycleNum)}</span></div>
              `
          }
        },
        grid: {
          left: '20px',
          right: '20px',
          bottom: '5%',
          top: '10%',
          containLabel: true
        },
        xAxis: {
          type: 'category',
          axisTick: {
            alignWithLabel: true
          },
          axisLabel: {
            interval: 0,
            formatter: function(params) {
              if (params.length < 6) {
                return params
              } else if (params.length < 11) {
                return params.substr(0, 5) + '\n' + params.substr(5)
              } else {
                return params.substr(0, 5) + '\n' + params.substr(5, 4) + '...'
              }
            }
          },
          data: arr.map(item => item.targetName)
        },
        yAxis: [
          {
            type: 'value',
            // name: '（分）',
            min: 0,
            max: 100,
            axisLine: {
              show: true
            },
            axisLabel: {
              formatter: '{value}'
            }
          }
        ],
        series: [
          {
            name: '满意度',
            type: 'bar',
            label: {
              show: true,
              position: 'top',
              formatter({ value }) {
                return value + '%'
              }
            },
            data: arr.map(item => item.sat),
            barMaxWidth: this.useBasic.barWidth
          }
        ]
      }
    },
    // 判断是增长还是降低
    isUp(value) {
      if (value === undefined || value === null) {
        return 'none'
      }
      return value > 0
    },
    barJumpAnalyse(index) {
      if (!this.checkAuth()) return
      const [statisticsYear, statisticsMonth] = this.allStatisticsList[index].month.split('-')
      this.$router.push('/satisfaction/mydyl/analyse?searchParams=' + JSON.stringify({
        ...this.cacheSearchParams,
        statisticsYear,
        statisticsMonth: [statisticsYear + '-' + statisticsMonth, statisticsYear + '-' + statisticsMonth]
      }))
    },
    barJumpPointAnalyse(index) {
      if (!this.checkAuth()) return
      const item = this.pointDataList[index]
      const params = this.cacheSearchParams
      const statisticsMonth = params.statisticsMonth.split(',').filter(item => item)
      this.$router.push('/satisfaction/mydyl/pointAnalyse?searchParams=' + JSON.stringify({
        ...params,
        pointId: item.pointId,
        pointName: item.pointName,
        statisticsMonth: statisticsMonth.length ? [params.statisticsYear + '-' + statisticsMonth[0], params.statisticsYear + '-' + statisticsMonth[statisticsMonth.length - 1]] : []
      }))
    },
    barJumpTargetAnalyse(index) {
      if (!this.checkAuth()) return
      const item = this.sortTargetDataList[index]
      console.log(item)
      const params = this.cacheSearchParams
      const statisticsMonth = params.statisticsMonth.split(',').filter(item => item)
      this.$router.push('/satisfaction/mydyl/targetAnalyse?searchParams=' + JSON.stringify({
        ...params,
        targetId: item.targetId,
        targetName: item.targetName,
        statisticsMonth: statisticsMonth.length ? [params.statisticsYear + '-' + statisticsMonth[0], params.statisticsYear + '-' + statisticsMonth[statisticsMonth.length - 1]] : []
      }))
    }
  }
}
</script>

<style lang="scss" scoped>
.overall-left, .overall-right {
  padding: 20px 0;
  box-sizing: border-box;
  min-height: 116px;
  background-image: linear-gradient(130deg, #2262FF 0%, #2073FB 100%);
  background: #F5F7FA;
  border-radius: 12px;
  flex: 1;
  .overallSat {
    height: 100%;
    justify-content: space-around;
  }
  .overallSat-item {
    height: 100%;
    flex: 1;
    position: relative;
    border-right: 1px dashed rgba(216,220,230,1);
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    .overallSat-value {
      min-width: 90px;
      font-size: 22px;
      color: #005DCF;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
    .overallSat-title, .value {
      display: flex;
      justify-content: center;
      align-items: center;
      min-width: 130px;
      font-size: 14px;
      color: #666666;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
    }
    .name {
      min-width: 90px;
      font-size: 22px;
      color: #333333;
      letter-spacing: 0;
      text-align: center;
      font-weight: 400;
      text-align: center;
      margin-bottom: 13px;
    }
    .icon {
      width: 18px;
      height: 21px;
      margin: 0 4px;
    }
    .m-t-0{
      margin-top: 0;
    }
  }
  .overallSat-item:nth-last-child(1) {
    border-right: none;
  }
}
.overall-right {
  min-width: 650px;
}
.u-chartTip {
  line-height: 1.8;
  display: flex;
  > span:first-child {
    width: 60px;
    font-weight: 500;
  }
}
</style>
