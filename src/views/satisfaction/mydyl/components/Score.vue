
import UmSearchLayout from '@/components/UmSearchLayout';
<template>
  <div class="g-body">
    <div style="flex-shrink: 0;">
      <UmSearchLayout label-width="80px">
        <template #default>
          <el-form-item label="类型：">
            <el-select v-model="typeCode" style="width: 100%;" @change="getList()">
              <el-option v-for="item in typeList" :key="item.value" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="业主阶段：">
            <el-select v-model="searchForm.pointTypeId" clearable placeholder="请选择" style="width: 100%;">
              <el-option v-for="item in useBasic.personLevel" :key="item.value" :label="item.dictName" :value="item.cfgDictId" />
            </el-select>
          </el-form-item>
          <el-form-item label="年度：">
            <el-select v-model="searchForm.statisticsYear" style="width: 100%;" @change="searchForm.statisticsQuarter = null;searchForm.statisticsMonth = null">
              <el-option v-for="(item, index) in yearList" :key="index" :label="item.label" :value="item.value" />
            </el-select>
          </el-form-item>
          <el-form-item label="季度：">
            <el-select v-model="searchForm.statisticsQuarter" clearable style="width: 100%;" :disabled="!searchForm.statisticsYear" @change="searchForm.statisticsMonth = null">
              <el-option v-for="item in quarterList" :key="item" :label="item" :value="+item" />
            </el-select>
          </el-form-item>
          <el-form-item label="月份：">
            <el-select v-model="searchForm.statisticsMonth" clearable style="width: 100%;" :disabled="!searchForm.statisticsQuarter">
              <el-option v-for="item in monthList" :key="item" :label="item + '月'" :value="item" />
            </el-select>
          </el-form-item>
          <el-form-item v-if="hasWeightPermission" label="职能权重：">
            <el-select v-model="searchForm.weightCode" style="width: 100%;">
              <el-option label="加权" :value="1" />
              <el-option label="不加权" :value="0" />
            </el-select>
          </el-form-item>
        </template>
        <template #suffix>
          <el-button v-if="$checkPermission(['MYDFX_MYDGL_JXKH_DC'])" type="primary" icon="el-icon-upload2" class="margin-b-20" :loading="exportLoading" @click="exportData">导出</el-button>
          <el-button type="primary" icon="el-icon-search" class="margin-b-20" :loading="tableLoading" @click="getList">搜索</el-button>
        </template>
      </UmSearchLayout>
    </div>
    <div v-loading="tableLoading" class="g-body__btm">
      <um-table-full :data="tableData" scroll :span-method="objectSpanMethod" :row-class-name="rowClass">
        <el-table-column label="排名" width="100" prop="rank" />
        <el-table-column v-if="typeCode === TYPE_CODE.AREA || typeCode === TYPE_CODE.CITY || typeCode === TYPE_CODE.PROJECT" key="areaCompanyName" min-width="200" label="区域名称">
          <el-row slot-scope="{ row }" type="flex" align="middle">
            {{ row.areaCompanyName }}
            <template v-if="typeCode === TYPE_CODE.AREA">
              <span style="margin: 0 5px;">较上月</span>
              <template v-if="isUp(row.rankMom) !== 'none'">
                <img v-if="isUp(row.rankMom)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
              </template>
              {{ absNumber(row.rankMom) }}名
            </template>
          </el-row>
        </el-table-column>
        <el-table-column v-if="typeCode === TYPE_CODE.CITY || typeCode === TYPE_CODE.PROJECT" key="cityCompanyName" min-width="200" label="公司">
          <el-row slot-scope="{ row }" type="flex" align="middle">
            {{ row.cityCompanyName }}
            <template v-if="typeCode === TYPE_CODE.CITY">
              <span style="margin: 0 5px;">较上月</span>
              <template v-if="isUp(row.rankMom) !== 'none'">
                <img v-if="isUp(row.rankMom)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
              </template>
              {{ absNumber(row.rankMom) }}名
            </template>
          </el-row>
        </el-table-column>
        <el-table-column v-if="typeCode === TYPE_CODE.PROJECT" key="projectName" min-width="240" label="项目">
          <el-row slot-scope="{ row }" type="flex" align="middle">
            <div style="flex: 1" class="row_2" :title="row.projectName">{{ row.projectName }}</div><span style="margin: 0 5px;">较上月</span>
            <template v-if="isUp(row.rankMom) !== 'none'">
              <img v-if="isUp(row.rankMom)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
              <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
            </template>
            {{ absNumber(row.rankMom) }}名
          </el-row>
        </el-table-column>
        <el-table-column label="满意度" min-width="200">
          <el-row slot-scope="{ row }" type="flex" align="middle">
            {{ row.sat | formatterTable }}%
            <template v-if="!row.needCombine">
              <span style="margin: 0 5px;">较上月</span>
              <template v-if="isUp(row.rankMom) !== 'none'">
                <img v-if="isUp(row.satRankMom)" class="icon" src="~@/assets/images/mydyl/black-shangsheng.png" alt="">
                <img v-else class="icon" src="~@/assets/images/mydyl/black-xiajiang.png" alt="">
              </template>
              {{ absNumber(row.satRankMom) }}%
            </template>
          </el-row>
        </el-table-column>
        <el-table-column label="满意度目标" min-width="200">
          <el-row slot-scope="{ row }" type="flex" align="middle">
            {{ row.satTarget | formatterTable }}%<span v-if="row.satTarget !== null" class="u-tag" :class="row.sat >= row.satTarget ? 'success' : 'error'">{{ row.sat >= row.satTarget ? '目标达成' : '目标未达成' }}</span>
          </el-row>
        </el-table-column>
        <el-table-column label="绩效分" width="100" prop="performanceScore" />
      </um-table-full>
    </div>
  </div>
</template>

<script>
import { getAreaCompanyScore, getCityCompanyScore, getProjectScore } from '@/api/satisfaction/mydyl'
import { CUSTOMER_LEVEL_LIST } from '@/enum'
import { UseBasicData } from '../hooks'
import exportData from '@/mixins/exportData'
import { exportExamineData } from '@/api/export'
const TYPE_CODE = {
  AREA: 2300042,
  CITY: 2300043,
  PROJECT: 2300044
}
export default {
  mixins: [exportData],
  props: {
    hasWeightPermission: {
      type: Boolean,
      default: false
    }
  },
  data() {
    let baseYear = 2023 // 从2023开始的，到当前年
    const currentYear = new Date().getFullYear()
    const yearList = []
    for (baseYear; baseYear <= currentYear; baseYear++) {
      yearList.push({ label: baseYear + '全年', value: baseYear }, { label: baseYear + '上半年', value: baseYear + 'half' })
    }
    return {
      TYPE_CODE,
      CUSTOMER_LEVEL_LIST,
      useBasic: new UseBasicData(),
      typeCode: TYPE_CODE.AREA,
      exportApi: exportExamineData,
      typeList: [
        { label: '区域绩效', value: TYPE_CODE.AREA },
        { label: '城市绩效', value: TYPE_CODE.CITY },
        { label: '项目绩效', value: TYPE_CODE.PROJECT }
      ],
      yearList,
      searchForm: {
        pointTypeId: null,
        statisticsYear: currentYear,
        statisticsMonth: null,
        statisticsQuarter: null,
        weightCode: 1
      },
      tableLoading: false,
      tableData: [],
      updateTime: null
    }
  },
  computed: {
    // 季度列表
    quarterList() {
      if (!this.searchForm.statisticsYear) return []
      if (this.searchForm.statisticsYear.toString().indexOf('half') !== -1) {
        return ['01', '02']
      }
      return ['01', '02', '03', '04']
    },
    // 月度列表
    monthList() {
      if (!this.searchForm.statisticsQuarter) return []
      let month = 12
      switch (this.searchForm.statisticsQuarter) {
        case 1: month = [1, 2, 3]; break
        case 2: month = [4, 5, 6]; break
        case 3: month = [7, 8, 9]; break
        case 4: month = [10, 11, 12]; break
      }
      return month
    }
  },
  activated() {
    this.$parent.setUpdateTime(this.updateTime)
  },
  created() {
    this.useBasic.getPersonLevel()
    this.getList()
  },
  methods: {
    formatExportParams() {
      const yearlyFlag = this.searchForm.statisticsYear.toString().indexOf('half') === -1 ? 1 : 0
      return { ...this.searchForm, statisticsYear: parseInt(this.searchForm.statisticsYear), yearlyFlag, orgTypeCode: this.typeCode }
    },
    getList() {
      let api = null
      switch (this.typeCode) {
        case TYPE_CODE.AREA: api = getAreaCompanyScore; break
        case TYPE_CODE.CITY: api = getCityCompanyScore; break
        case TYPE_CODE.PROJECT: api = getProjectScore; break
      }
      this.tableLoading = true
      const yearlyFlag = this.searchForm.statisticsYear.toString().indexOf('half') === -1 ? 1 : 0
      api({ ...this.searchForm, statisticsYear: parseInt(this.searchForm.statisticsYear), yearlyFlag }).then(res => {
        const { dataCreateTime, orgDataList = [], scoreMapList = [] } = res.data || {}
        this.updateTime = dataCreateTime
        this.$parent.setUpdateTime(dataCreateTime)
        // const newArr = orgDataList.concat(scoreMapList.map(item => {
        //   return {
        //     ...item,
        //     rank: '行业' + item.standardScore + '分位',
        //     sat: +item.satisfaction,
        //     performanceScore: item.switchScore,
        //     needCombine: true // 需要合并
        //   }
        // }))
        const newArr = orgDataList
        newArr.sort((prev, next) => {
          return parseFloat(next.sat) - parseFloat(prev.sat)
        })
        console.log(newArr)
        this.tableData = newArr
      }).finally(() => {
        this.tableLoading = false
      })
    },
    rowClass({ row }) {
      return row.needCombine ? 'combine' : ''
    },
    // 判断是增长还是降低
    isUp(value) {
      if (value === undefined || value === null) {
        return 'none'
      }
      return value > 0
    },
    objectSpanMethod({ row, columnIndex }) {
      if (row.needCombine) {
        let colLength = 1 // 需要合并的列数，区域，公司，项目这三个
        switch (this.typeCode) {
          case TYPE_CODE.AREA: colLength = 2; break
          case TYPE_CODE.CITY: colLength = 3; break
          case TYPE_CODE.PROJECT: colLength = 4; break
        }
        if (columnIndex === 0) {
          return {
            rowspan: 1,
            colspan: colLength
          }
        } else if (columnIndex === colLength) {
          return {
            rowspan: 1,
            colspan: 2
          }
        } else if (columnIndex === colLength + 2) {
          return {
            rowspan: 1,
            colspan: 1
          }
        } else {
          return {
            rowspan: 0,
            colspan: 0
          }
        }
      }
    },
    // 取绝对值
    absNumber(num) {
      if (num === null || num === undefined) {
        return '--'
      }
      return Math.abs(num)
    }
  }

}
</script>

<style lang="scss" scoped>
.g-body {
   height: calc(100% - 94px);
   display: flex;
   flex-direction: column;
   &__btm {
    flex: 1;
    background-color: #fff;
    margin-top: 20px;
    padding: 20px;
    display: flex;
    .icon {
      width: 18px;
      height: 21px;
      margin: 0 4px;
    }
    .u-tag {
        line-height: 20px;
        padding: 0 6px;
        font-size: 12px;
        border-radius: 4px;
        flex-shrink: 0;
        margin-left: 5px;
        &.success {
            background: rgba(47,193,140,0.10);
            color: #36C35A;
        }
        &.error {
            color: #F8716B;
            background: rgba(248,113,107,0.10)
        }
    }
    ::v-deep {
        .el-table.el-table--striped .el-table__body tr.el-table__row.combine td.el-table__cell{
            &:first-child {
                padding-left: 100px;
            }
            background-color: $--color-primary!important;
            color: #fff!important;
        }
    }
   }
}
</style>
