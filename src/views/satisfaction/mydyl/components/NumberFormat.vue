<template>
  <span>
    <template v-if="format">{{ format.left }}<span class="mini">.{{ format.right }}</span></template>
    <template v-else><span>--</span></template>%
  </span>
</template>
<script>

export default {
  props: {
    num: {
      type: [String, Number],
      default: null
    }
  },
  data() {
    return {}
  },
  computed: {
    format() {
      if (this.num === undefined || this.num === null) {
        return null
      }
      const arr = this.num.toString().split('.')
      return {
        left: Math.abs(arr[0]),
        right: arr[1] ? arr[1].padEnd(2, 0) : '00'
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.mini {
    font-size: 16px;
    font-weight: 400;
}
</style>
