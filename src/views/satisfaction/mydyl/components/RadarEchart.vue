<template>
  <div class="g-chart">
    <div class="charts">
      <div id="chart" ref="chart" />
    </div>
    <div class="flexStart margin-t-20">
      <div v-for="(item,index) in tips" :key="index" class="tip f-flex-ajc margin-r-20">
        <div :class="item.type" :style="'background-color:' + item.color" />
        <div class="margin-l-10">{{ item.title }}</div>
      </div>
    </div>
  </div>
</template>

<script>
import * as ECharts from 'echarts'
const minChartWidth = 40 // 柱状图最小宽度
const chartSpace = 200 // 图表的左右间隔
const chartMargin = 20// 柱子的右间隔
export default {
  name: 'RadarEchart',
  props: {
    tips: {
      type: Array,
      default: () => []
    },
    data: {
      type: Object,
      default: () => {}
    },
    needClick: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      myChart: null
    }
  },
  watch: {
    data: {
      immediate: true,
      deep: true,
      handler(data) {
        if (this.$refs.chart) {
          this.$nextTick(() => {
          // 根据数据量，动态设置图标区域宽度，出现x轴滚动条
            const length = data?.xAxis?.data?.length || 0
            const chartOffsetWidth = this.$refs.chart.offsetWidth
            const chartLength = Math.floor((chartOffsetWidth - chartSpace) / (minChartWidth + chartMargin))
            if (length > chartLength) {
              this.$refs.chart.style.width = chartOffsetWidth + (length - chartLength) * (minChartWidth + chartMargin) + 'px'
              this.resize()
            }
          })
          this.init(data)
        }
      }
    }
  },
  activated() {
    this.resize()
  },
  mounted() {
    this.init(this.data)
    window.addEventListener('resize', this.resize)
    this.needClick && this.onChartClick()
  },
  beforeDestroy() {
    window.removeEventListener('resize', this.resize)
  },
  methods: {
    resize() {
      this.myChart && this.myChart.resize()
    },
    init(data) {
      this.myChart = ECharts.init(this.$refs.chart, null, { devicePixielRatio: 2 })
      this.myChart.setOption(data)
    },
    onChartClick() {
      const chartZr = this.myChart.getZr()
      chartZr.on('mousemove', (params) => {
        const pointInPixel = [params.offsetX, params.offsetY]
        if (this.myChart.containPixel('grid', pointInPixel)) {
          // 将此区域的 鼠标样式变为 小手
          this.myChart.getZr().setCursorStyle('pointer')
        }
      })
      // echart全图点击事件
      chartZr.on('click', (param) => {
        // 获取 点击的 触发点像素坐标
        const pointInPixel = [param.offsetX, param.offsetY]
        // 判断给定的点是否在指定的坐标系或者系列上
        if (this.myChart.containPixel('grid', pointInPixel)) {
          // 获取到点击的 x轴 下标  转换为逻辑坐标
          const xIndex = this.myChart.convertFromPixel({ seriesIndex: 0 }, pointInPixel)[0]
          this.$emit('click', xIndex)
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
#chart {
  width: 100%;
  min-width: 100%!important;
  height: 100%;
  margin: 0 auto;
}
.g-chart {
  border-radius: 8px;
}
.charts {
  background: #F7F8F9;
  height: 254px;
  width: 100%;
  margin: 0 auto;
  overflow-x: auto;
}

.tip {
  font-size: 12px;
  color: #31415F;
  letter-spacing: 0;
  line-height: 12px;
  .block {
    width: 12px;
    height: 6px;
  }
  .line {
    width: 16px;
    height: 2px;
  }
  .dot {
    width: 7px;
    height: 7px;
    border-radius: 50%;
  }
}
</style>
