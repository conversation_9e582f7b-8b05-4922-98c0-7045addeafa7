<template>
  <div class="g-container">
    <div class="g-container">
      <el-card class="card-pd0">
        <el-form ref="searchForm" :model="searchForm" label-width="70px" label-suffix="：">
          <el-row :gutter="30">
            <el-col :span="8" :md="8" :xl="6">
              <el-form-item prop="areaCompanyId" label="区域">
                <UmBusCommon v-model="projDto" value-key="areaCompanyId" />
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item prop="productTypeId" label="业态">
                <el-select v-model="searchForm.productTypeId" clearable filterable style="width: 100%;">
                  <el-option
                    v-for="opt in useBasic.productTypeList"
                    :key="opt.code"
                    :label="opt.name"
                    :value="opt.code"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item prop="decorateTypeCode" label="装修类型">
                <el-select v-model="searchForm.decorateTypeCode" filterable clearable style="width: 100%">
                  <el-option
                    v-for="opt in decorationList"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item label="年度" prop="statisticsYear">
                <el-date-picker
                  v-model="searchForm.statisticsYear"
                  :value="new Date()"
                  type="year"
                  placeholder="选择年"
                  value-format="yyyy-MM-dd"
                  format="yyyy"
                  style="width: 100%"
                  :clearable="false"
                  :editable="false"
                  :picker-options="{
                    disabledDate: (date) => {
                      // 2023 开始有数据的, 当前年份之后的年也不能选择
                      return new Date(2023, 0, 1) > date || new Date() < date
                    },
                  }"
                  @change="useBasic.yearChange(searchForm,$event)"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item prop="statisticsMonth" label="月份">
                <el-date-picker
                  v-model="searchForm.statisticsMonth"
                  style="width: 100%;"
                  type="monthrange"
                  range-separator="至"
                  start-placeholder="开始月份"
                  end-placeholder="结束月份"
                  value-format="yyyy-MM"
                  format="MM月"
                  :clearable="false"
                  :editable="false"
                  :picker-options="pickerOptions"
                  @blur="pickStartMonth = null"
                />
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item prop="pointTypeId" label="业主阶段">
                <el-select v-model="searchForm.pointTypeId" filterable clearable style="width: 100%">
                  <el-option v-for="item in useBasic.personLevel" :key="item.value" :label="item.dictName" :value="item.cfgDictId" />
                </el-select>
              </el-form-item>
            </el-col>
            <el-col :span="10" :md="8" :xl="6">
              <el-form-item prop="examineFlag" label="统计口径">
                <el-select v-model="searchForm.assessTypeCode" filterable clearable style="width: 100%">
                  <el-option
                    v-for="opt in examineFlagList"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-form-item>
            </el-col>
            <!-- <el-col v-if="hasWeightPermission" :span="10" :md="8" :xl="6">
              <el-form-item prop="weightCode" label="职能权重">
                <el-select v-model="searchForm.weightCode" filterable clearable style="width: 100%">
                  <el-option
                    v-for="opt in weightCodeList"
                    :key="opt.value"
                    :label="opt.label"
                    :value="opt.value"
                  />
                </el-select>
              </el-form-item>
            </el-col> -->
            <el-col :span="4" :md="8" :xl="6" class="fr text-right">
              <el-button :loading="tableLoading" type="primary" icon="el-icon-search" @click="getData(false)">搜索</el-button>
            </el-col>
          </el-row>
        </el-form>
      </el-card>
      <el-card v-loading="tableLoading">
        <el-row type="flex" align="middle" class="margin-b-20">
          <CommonHeader title="总体满意度" padding-left="0" margin-bottom="0" height="20" />
          <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 0" icon="el-icon-upload2" type="primary" @click="beforeExportData(0)">导出</el-button>
        </el-row>
        <RadarEchart :data="allEchartData" :tips="allTipsList" />
      </el-card>
      <el-card v-loading="tableLoading">
        <el-tabs v-model="pointTabId">
          <el-tab-pane v-for="(point) in useBasic.pointList" :key="point.pointId" :name="point.pointId" :label="point.pointName" />
        </el-tabs>
        <el-row type="flex" align="middle" class="margin-b-20">
          <CommonHeader padding-left="0" margin-bottom="0" height="20">
            <template slot="title">
              <div class="head-title">{{ useBasic.currentPointName }}</div>
              <el-tooltip v-if="tips[useBasic.currentPointName]" class="item" effect="dark" :content="tips[useBasic.currentPointName]" placement="right">
                <i class="el-icon-question" style="cursor: pointer;color: #8B8B7A" />
              </el-tooltip>
            </template>
          </CommonHeader>
          <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 0" icon="el-icon-upload2" type="primary" @click="beforeExportData(1)">导出</el-button>
        </el-row>
        <RadarEchart :data="pointEchartData" :tips="pointTipsList" />
      </el-card>
      <el-card v-loading="tableLoading">
        <el-tabs v-model="targetTabId">
          <el-tab-pane v-for="(target) in useBasic.targetList" :key="target.targetId" :name="target.targetId" :label="target.targetName" />
        </el-tabs>
        <el-row type="flex" align="middle" class="margin-b-20">
          <CommonHeader :title="useBasic.currentTargetName" padding-left="0" margin-bottom="0" height="20" />
          <el-button v-if="$checkPermission(['MYDFX_MYDGL_DC'])" :loading="exportLoading && exportIndex === 0" icon="el-icon-upload2" type="primary" @click="beforeExportData(2)">导出</el-button>
        </el-row>
        <RadarEchart :data="targetEchartData" :tips="targetTipsList" />
      </el-card>
    </div>
  </div>
</template>

<script>
import CommonHeader from '@/components/CommonTitle/index.vue'
import RadarEchart from '@/views/satisfaction/mydyl/components/RadarEchart.vue'
import { getCityPointSatOverview, getCitySatOverview, getCityTargetSatOverview } from '@/api/satisfaction/mydyl'
import { UseBasicData, tips, getDefaultMonthRange } from '../hooks'
import { EXAMINE_TYPE, CUSTOMER_LEVEL_LIST } from '@/enum'
import exportData from '@/mixins/exportData'
import { exportCitySatOverviewData, exportCityPointSatOverviewData, exportCityTargetSatOverviewData } from '@/api/export'

export default {
  name: 'SatisfactionMydylCity',
  components: { RadarEchart, CommonHeader },
  mixins: [exportData],
  props: {
    hasWeightPermission: {
      type: Boolean,
      default: false
    }
  },
  data() {
    const shorts = [
      { label: '一季度', sMonth: 0, eMonth: 2 },
      { label: '二季度', sMonth: 3, eMonth: 5 },
      { label: '三季度', sMonth: 6, eMonth: 8 },
      { label: '四季度', sMonth: 9, eMonth: 11 },
      { label: '上半年', sMonth: 0, eMonth: 5 },
      { label: '下半年', sMonth: 6, eMonth: 11 }
    ]
    const currentDate = new Date()
    const generateShorts = (sMonth, eMonth, picker) => {
      const year = new Date(this.searchForm.statisticsYear).getFullYear()
      if (year === currentDate.getFullYear()) { // 如果是当前年，则结束月份不能大于当前月份
        picker.$emit('pick', [new Date(year, sMonth, 1), new Date(year, currentDate.getMonth() < eMonth ? currentDate.getMonth() : eMonth, 1)])
      } else {
        picker.$emit('pick', [new Date(year, sMonth, 1), new Date(year, eMonth, 1)])
      }
    }
    return {
      tips,
      useBasic: new UseBasicData(),
      CUSTOMER_LEVEL_LIST,
      projDto: {},
      pickerOptions: {
        disabledDate: (date) => {
          const searchDate = new Date(this.searchForm.statisticsYear)
          const year = searchDate.getFullYear()
          const month = new Date().getMonth() + 1
          // 如果面板打开，并且选择了月份
          // 如果是1月份或者12月份, 或者没有选择任何月份
          if (this.pickStartMonth === 1 || this.pickStartMonth === 12 || !this.pickStartMonth) {
            if (year === new Date().getFullYear()) { // 当前年份，则只能选择1月份到当前月份
              return new Date(year, 0, 1) > date || new Date() < date
            } else { // 不是当前年份，则只能选择本年1月份到12月份
              return new Date(year, 0, 1) > date || new Date(year + 1, -1, 1) < date
            }
          } else if ([4, 7, 10].includes(this.pickStartMonth)) { // 如果是4,7,10月份，则可以选择前一个月份和后两个
            if (year === new Date().getFullYear()) { // 当前年份，当前季度开始月份的前一个月，和后两个月，不能超过当前月份
              if (this.pickStartMonth === 7) { // 7月份可以选择到下半年
                return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, month - 1, 1) < date
              }
              let endMonth = this.pickStartMonth + 2 > month ? this.pickStartMonth + 1 : this.pickStartMonth + 2
              endMonth = endMonth > month ? endMonth - 1 : endMonth
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, endMonth - 1, 1) < date
            } else { // 不是当前年份，当前季度开始月份的前一个月，和后两个月
              if (this.pickStartMonth === 7) { // 7月份可以选择到下半年
                return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, 11, 1) < date
              }
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth + 1, 1) < date
            }
          } else if (this.pickStartMonth !== 12) { // 如果没选择到12月份，则只能选择前后跨度一个月
            if (year === new Date().getFullYear()) { // 当前年份，不能超过当前月份
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth < month ? this.pickStartMonth : this.pickStartMonth - 1, 1) < date
            } else { // 不是当前年份，当前月份前后月份
              return new Date(year, this.pickStartMonth - 2, 1) > date || new Date(year, this.pickStartMonth, 1) < date
            }
          }
        },
        onPick: ({ maxDate, minDate }) => {
          this.pickStartMonth = new Date(minDate).getMonth() + 1
        },
        shortcuts: shorts.filter(item => currentDate.getMonth() >= item.sMonth).map(item => {
          return {
            text: item.label,
            onClick: (picker) => {
              generateShorts(item.sMonth, item.eMonth, picker)
            }
          }
        })
      },
      searchForm: {
        areaCompanyId: '',
        productTypeId: '',
        decorateTypeCode: '',
        statisticsYear: new Date(),
        statisticsMonth: getDefaultMonthRange(), // 月份多传，连续月份
        pointTypeId: '',
        assessTypeCode: null,
        weightCode: 1
      },
      allEchartData: {}, // 总体满意度
      allTipsList: [], // 总体满意度底部展示信息
      pointDataList: [], // 全部触点数据
      pointTabId: null, // 选中的触点
      pointTipsList: [], // 触点底部展示信息
      pointEchartData: {}, // 触点可视化数据
      targetDataList: [], // 全部指标数据
      targetTabId: null, // 选中的指标
      targetTipsList: [], // 指标底部展示信息
      targetEchartData: {}, // 指标可视化数据
      decorationList: [
        {
          label: '毛坯',
          value: '2200031'
        },
        {
          label: '精装',
          value: '2200032'
        }
      ],
      weightCodeList: [
        { label: '不加权', value: 0 },
        { label: '加权', value: 1 }
      ],
      // 统计口径
      examineFlagList: [
        { label: '年度考核', value: EXAMINE_TYPE.YEAR },
        { label: '半年度考核', value: EXAMINE_TYPE.HALF_YEAR },
        { label: '季度考核', value: EXAMINE_TYPE.QUARTER },
        { label: '月度考核', value: EXAMINE_TYPE.MONTH },
        { label: '仅监控，不考核', value: EXAMINE_TYPE.NO }
      ],
      updateTime: null,
      exportIndex: null,
      tableLoading: false
    }
  },
  watch: {
    pointTabId(id) {
      this.computedPointChart(id)
    },
    targetTabId(id) {
      this.computedTargetChart(id)
    }
  },
  activated() {
    this.$parent.setUpdateTime(this.updateTime)
  },
  async created() {
    try {
      await this.getData()
      await Promise.all([this.useBasic.getProductType(), this.useBasic.getPointOption(), this.useBasic.getTargetList(), this.useBasic.getPersonLevel()])
      this.pointTabId = this.useBasic.pointList[0]?.pointId
      this.useBasic.pointTabChange(this.pointTabId)
      this.targetTabId = this.useBasic.targetList[0]?.targetId
      this.useBasic.targetTabChange(this.targetTabId)
    } catch (error) {

    }
  },
  mounted() {
    this.useBasic.getTargetList()
  },
  methods: {
    beforeExportData(index) {
      this.exportIndex = index
      switch (index) {
        case 0: this.exportApi = exportCitySatOverviewData; break
        case 1: this.exportApi = exportCityPointSatOverviewData; break
        case 2: this.exportApi = exportCityTargetSatOverviewData; break
      }
      this.exportData()
    },
    formatExportParams() {
      return this.useBasic.formatParams(this.searchForm, this.projDto)
    },
    // 统一获取数据
    getData() {
      this.tableLoading = true
      Promise.all([this.getCitySatOverview(), this.getCityPointSatOverview(), this.getCityTargetSatOverview()]).then(res => {
      }).finally(() => {
        this.tableLoading = false
      })
    },
    // 获取总体满意度
    getCitySatOverview() {
      return getCitySatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        const allStatisticsList = res.data.dataList || []
        const scoreMapList = res.data.scoreMapList || []
        this.updateTime = res.data.updateTime
        this.$parent.setUpdateTime(this.updateTime)
        const [allTipsList, allEchartData] = this.useBasic.chartFormat(scoreMapList, allStatisticsList, res.data.totalSat, '城市')
        this.allTipsList = allTipsList
        this.allEchartData = allEchartData
      })
    },
    // 获取认购服务
    getCityPointSatOverview() {
      return getCityPointSatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this.pointDataList = res.data || []
        this.pointTabId && this.computedPointChart(this.pointTabId)
      })
    },
    // 获取房屋设计
    getCityTargetSatOverview() {
      return getCityTargetSatOverview(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this.targetDataList = res.data || []
        this.targetTabId && this.computedTargetChart(this.targetTabId)
      })
    },
    computedPointChart(id) {
      this.useBasic.pointTabChange(id)
      const pointItem = this.pointDataList.find((point) => {
        return point.pointId.toString() === id
      })
      const pointData = pointItem?.data || {
        dataList: [],
        scoreMapList: [],
        totalSat: ''
      }
      const [pointTipsList, pointEchartData] = this.useBasic.chartFormat(pointData.scoreMapList, pointData.dataList, pointData.totalSat, '城市')
      this.pointTipsList = pointTipsList
      this.pointEchartData = pointEchartData
    },
    computedTargetChart(id) {
      this.useBasic.targetTabChange(id)
      const targetItem = this.targetDataList.find((target) => {
        return target.targetId === id
      })
      const targetData = targetItem?.data || {
        dataList: [],
        scoreMapList: [],
        totalSat: ''
      }
      const [targetTipsList, targetEchartData] = this.useBasic.chartFormat(targetData.scoreMapList, targetData.dataList, targetData.totalSat, '城市', false)
      this.targetTipsList = targetTipsList
      this.targetEchartData = targetEchartData
    }
  }
}
</script>

<style lang="scss" scoped>
::v-deep .el-tabs__nav-wrap::after {
  height: 0px;
}
</style>

