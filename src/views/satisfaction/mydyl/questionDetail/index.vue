<template>
  <div class="g-container">
    <div style="flex-shrink: 0;">
      <UmSearchLayout label-width="70px">
        <template #default>
          <el-form-item prop="areaCompanyId" label="区域：">
            <UmBusCommon v-model="projDto" value-key="areaCompanyId" is-auth :clearable="hasGuFenAuth" />
          </el-form-item>
          <el-form-item prop="cityCompanyId" label="城市：">
            <UmBusCommon v-model="projDto" value-key="cityCompanyId" :area-company-id="projDto.areaCompanyId" is-auth :clearable="hasGuFenAuth" />
          </el-form-item>
          <el-form-item prop="projectId" label="项目：">
            <UmBusCommon v-model="projDto" value-key="projectId" :area-company-id="projDto.areaCompanyId" :city-company-id="projDto.cityCompanyId" is-auth :clearable="hasGuFenAuth" />
          </el-form-item>
          <el-form-item prop="productTypeId" label="业态：">
            <el-select-tree
              ref="elSelectTree"
              v-model="searchForm.productTypeId"
              :options="useBasic.productTypeList"
              :filterable="true"
              separator="-"
              :show-all-levels="true"
              :clearable="true"
              :props="{
                emitPath: false,
                label: 'name',
                value: 'code'
              }"
              style="width: 100%;"
            />
          </el-form-item>
          <el-form-item prop="decorateTypeCode" label="装修类型：">
            <el-select v-model="searchForm.decorateTypeCode" filterable clearable style="width: 100%">
              <el-option
                v-for="opt in decorationList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="pointId" label="触点：">
            <el-select v-model="searchForm.pointId" filterable style="width: 100%" clearable>
              <el-option
                v-for="point in useBasic.pointList"
                :key="point.pointId"
                :label="point.pointName"
                :value="point.pointId"
              />
            </el-select>
          </el-form-item>
          <el-form-item prop="assessTypeCode" label="考核：">
            <el-select v-model="searchForm.assessTypeCode" filterable style="width: 100%" clearable>
              <el-option
                v-for="opt in examineFlagList"
                :key="opt.value"
                :label="opt.label"
                :value="opt.value"
              />
            </el-select>
          </el-form-item>
          <el-form-item label="年度：" prop="statisticsYear">
            <el-date-picker
              v-model="searchForm.statisticsYear"
              :value="new Date()"
              type="year"
              placeholder="选择年"
              value-format="yyyy-MM-dd"
              format="yyyy"
              style="width: 100%"
              :clearable="false"
              :picker-options="{
                disabledDate: (date) => {
                  // 2023 开始有数据的
                  return new Date(2023, 0, 1) > date
                },
              }"
              @change="useBasic.yearChange(searchForm,$event)"
            />
          </el-form-item>
          <el-form-item prop="statisticsMonth" label="月份：">
            <el-date-picker
              v-model="searchForm.statisticsMonth"
              style="width: 100%;"
              type="monthrange"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              format="MM月"
              :picker-options="{
                disabledDate: (date) => {
                  const year = new Date(searchForm.statisticsYear).getFullYear()
                  return new Date(year, 0, 1) > date || new Date(year + 1, -1, 1) < date
                },
              }"
            />
          </el-form-item>
        </template>

        <template #suffix>
          <el-button :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button>
          <el-button type="primary" icon="el-icon-search" class="margin-b-20" :loading="tableLoading" @click="getQuestionData();getList()">搜索</el-button>
        </template>
      </UmSearchLayout>
    </div>
    <div v-loading="tableLoading" class="g-container__btm">
      <div class="u-tips">
        <div>{{ info.targetName }}-{{ isDis ? `不满意${type===1?'原因':'分类'}` : `满意${type===1?'原因':'分类'}` }}：{{ info.reasonName | formatterTable }}</div>
        <div>
          <span class="margin-r-10">样框量：{{ info.frameNum | formatterTable }}</span>
          <span class="margin-r-10">回收样本量：{{ info.recycleNum | formatterTable }}</span>
          <span class="margin-r-10">填写次数：{{ info.grantNum | formatterTable }}</span>
          <span class="margin-r-10">意见占比：{{ info.rate | formatterTable }}%</span>
        </div>
      </div>
      <um-table-full :data="tableData" scroll>
        <el-table-column prop="name" label="序号" type="index" width="60" />
        <el-table-column prop="areaCompanyName" label="区域" width="120" />
        <el-table-column prop="projectName" label="项目" width="160" />
        <el-table-column prop="stageName" label="分期" width="120" />
        <el-table-column prop="buildingName" label="楼栋" width="120" />
        <el-table-column prop="unitName" label="单元" width="120" />
        <el-table-column prop="doorNo" label="房号" width="120" />
        <el-table-column prop="planName" label="调研计划" width="160" />
        <el-table-column prop="pointName" label="调研触点" width="160" />
        <el-table-column prop="name" label="客户信息" width="120">
          <template #default="scope">
            <span>{{ scope.row.customerName }}</span>
          </template>
        </el-table-column>
        <!--<el-table-column prop="houseInfo" label="房产信息" min-width="200" />-->
        <el-table-column prop="recycleTime" label="回收时间" width="170" />
        <el-table-column label="操作" width="120" fixed="right">
          <template #default="scope">
            <el-button type="text" @click="openQuestion(scope.row)">查看问卷</el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList('page')"
      />
    </div>
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { DECORATION_STATUS, EXAMINE_TYPE } from '@/enum'
import { UseBasicData } from '@/views/satisfaction/mydyl/hooks'
import { getQuestionnaireDetailList, getQuestionnaireStatisticsData } from '@/api/satisfaction/mydyl/analyse'
import { exportQuestionnaireDetail } from '@/api/export'
import exportData from '@/mixins/exportData'
import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'SatisfactionMydylQuestionDetail',
  mixins: [getList, exportData],
  data() {
    return {
      exportApi: exportQuestionnaireDetail,
      listApi: getQuestionnaireDetailList,
      useBasic: new UseBasicData(),
      projDto: {
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null
      },
      info: {
        reasonName: '',
        frameNum: '',
        recycleNum: '',
        grantNum: '',
        rate: ''
      },
      searchForm: {
        reasonId: null,
        pointId: '',
        areaCompanyId: '',
        cityCompanyId: '',
        projectId: '',
        productTypeId: '',
        decorateTypeCode: null, // 装修标准
        assessTypeCode: EXAMINE_TYPE.YEAR, // 考核类型（统计口径）
        statisticsYear: new Date(),
        statisticsMonth: [] // 月份多传，连续月份
      },
      pointList: [],
      // 装修类型
      decorationList: [
        {
          label: '毛坯',
          value: DECORATION_STATUS.SIMPLE
        },
        {
          label: '精装',
          value: DECORATION_STATUS.FINE
        }
      ],
      // 考核
      examineFlagList: [
        { label: '年度考核', value: EXAMINE_TYPE.YEAR },
        { label: '半年度考核', value: EXAMINE_TYPE.HALF_YEAR },
        { label: '季度考核', value: EXAMINE_TYPE.QUARTER },
        { label: '月度考核', value: EXAMINE_TYPE.MONTH },
        { label: '仅监控，不考核', value: EXAMINE_TYPE.NO }
      ],
      isDis: false,
      type: 1 // 1,按照原因统计2,按照分类统计
    }
  },
  computed: {
    ...mapGetters(['hasGuFenAuth'])
  },
  watch: {
    projDto: {
      immediate: true,
      deep: true,
      handler(v) {
        this.searchForm.areaCompanyId = v.areaCompanyId
        this.searchForm.cityCompanyId = v.cityCompanyId
        this.searchForm.projectId = v.projectId
      }
    }
  },
  async created() {
    this['user/getGuFenAuth']()
    try {
      const params = this.$route.query.searchParams ? JSON.parse(this.$route.query.searchParams) : {
        areaCompanyId: '',
        cityCompanyId: '',
        projectId: '',
        statisticsYear: null
      }
      this.projDto.areaCompanyId = params.areaCompanyId
      this.projDto.cityCompanyId = params.cityCompanyId
      this.projDto.projectId = params.projectId
      this.isDis = !!+params.isDis
      this.type = params.type
      this.searchForm = {
        ...this.searchForm,
        ...params,
        pointId: params.pointId?.toString(),
        statisticsYear: new Date(params.statisticsYear, 1)
      }
      this.getQuestionData()
      await this.getList()
      await this.useBasic.getProductType()
      await this.useBasic.getPointOption()
    } catch (error) {

    }
  },
  methods: {
    ...mapActions(['user/getGuFenAuth']),
    beforeApiCallBack() {
      return this.useBasic.formatParams(this.searchForm, this.projDto)
    },
    formatExportParams() {
      return this.useBasic.formatParams(this.searchForm, this.projDto)
    },
    getQuestionData() {
      getQuestionnaireStatisticsData(this.useBasic.formatParams(this.searchForm, this.projDto)).then(res => {
        this.info = res.data
      })
    },
    openQuestion(data) {
      const info = JSON.stringify({
        customerName: data.customerName,
        customerAllMobile: data.customerMobile,
        houseInfo: data.projectName + '-' + data.stageName + '-' + data.buildingName + '-' + data.unitName + '-' + data.doorNo
      })
      this.$router.push({
        path: '/satisfaction/mydyl/watch',
        query: {
          id: data.uuid,
          info: info
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    &__btm {
        margin-top: 20px;
        flex:1;
        display: flex;
        flex-direction: column;
        padding: 20px;
        background-color: #fff;
    }
  .u-tips {
    display: flex;
    justify-content: space-between;
    line-height: 24px;
    background: rgba(#FF9645, 0.1);
    border-radius: 4px;
    color: #FF9645;
    padding:5px 12px;
    font-size: 12px;
    position: relative;
    margin-bottom: 12px;
    font-weight: 500;
    &::before {
      content: '';
      width: 2px;
      height: 12px;
      background: #FF9645 ;
      position: absolute;
      left: 0;
      top: 11px;
    }
  }
}
</style>
