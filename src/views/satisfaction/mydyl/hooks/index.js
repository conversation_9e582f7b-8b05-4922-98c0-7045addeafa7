import { getProductType, getPointOption, getDictList } from '@/api/common'
import { getTargetTree } from '@/api/base/indicator'
import { has } from 'lodash'
import { formatterTable } from '@/filters'
import { DICT_CODE, POINT_RULE } from '@/enum'
import { flattenTree } from '@/utils'
export class UseBasicData {
  constructor() {
    this.productTypeList = []// 业态
    this.pointList = []// 触点列表
    this.targetList = []// 二级指标列表
    this.personLevel = [] // 业主阶段
    this.currentPointName = ''
    this.currentTargetName = ''
    this.barWidth = 50
    this.markLineColors = ['#FF9645', '#F24040', '#3D80FC', '#62DE85', '#F7C739', '#F3653C', '#7262DE', '#E7CAAF', '#263EDE', '#DB2F8F', '#B63CF3', '#62D1DE']
  }

  getProductType() {
    if (this.productTypeList.length) return
    return getProductType().then(res => {
      this.productTypeList = res.data || []
    })
  }

  getPointOption() {
    if (this.pointList.length) return
    return getPointOption({ all: true }).then(res => {
      const arr = res.data || []
      this.pointList = arr.filter(item => item.ruleCode !== POINT_RULE.SPECIAL)
    })
  }

  getPersonLevel() {
    return getDictList({ dictCode: DICT_CODE.POINT_TYPE }).then(res => {
      this.personLevel = res.data || []
    })
  }

  getTargetList() {
    if (this.targetList.length) return
    return getTargetTree({ disableFlag: true }).then(res => {
      this.targetList = flattenTree(res.data || [], 'targetChildren').filter(item => item.levelCode !== 2200061).map(item => {
        return {
          targetId: item.tgtTargetId,
          targetName: item.targetName
        }
      })
    })
  }

  pushMarkLineColors() {
    this.markLineColors.push(this.randomColor())
  }

  pointTabChange(id) {
    const currentPoint = this.pointList.find((point) => {
      return point.pointId === id
    })
    this.currentPointName = currentPoint ? currentPoint.pointName : ''
  }

  targetTabChange(id) {
    const currentTarget = this.targetList.find((target) => {
      return target.targetId === id
    })
    this.currentTargetName = currentTarget ? currentTarget.targetName : ''
  }

  randomColor() {
    let color = '#'
    for (let i = 0; i < 6; i++) color += parseInt(Math.random() * 16).toString(16)
    return color
  }

  // 年份改变，动态修改月份的年份
  yearChange(searchForm, value) {
    // searchForm.statisticsMonth = searchForm.statisticsMonth.map(item => {
    //   return value.substr(0, 4) + item.substr(4)
    // })
    searchForm.statisticsMonth = getDefaultMonthRange(searchForm)
  }

  // 参数格式化
  formatParams(searchForm, projDto = {}) {
    if (Object.keys(projDto).length > 0) {
      searchForm.areaCompanyId = projDto.areaCompanyId || ''
      searchForm.cityCompanyId = projDto.cityCompanyId || ''
      searchForm.projectId = projDto.projectId || ''
    }
    const arr = []
    // 处理连续月份，在两个间隔月份中补充值
    if (searchForm.statisticsMonth?.length) {
      const [startMonth, endMonth] = searchForm.statisticsMonth
      const startIndex = +startMonth.slice(5)
      const endIndex = +endMonth.slice(5)
      for (let index = startIndex; index <= endIndex; index++) {
        arr.push(index)
      }
    }
    // 如果选中1到12月，则当作没选
    // if (arr.length === 12) {
    //   arr.splice(0, arr.length)
    // }
    return {
      ...searchForm,
      statisticsYear: new Date(searchForm.statisticsYear).getFullYear(),
      statisticsMonth: arr.join(',')
    }
  }

  chartFormat(scoreMapList, allStatisticsList, totalSat, tip, showYearGoal = true) {
    if (this.markLineColors.length < scoreMapList.length) {
      this.pushMarkLineColors()
    }
    const tips = scoreMapList.map((item, index) => {
      return {
        title: item.score + '分位',
        color: this.markLineColors[index],
        type: 'line'
      }
    })
    const allTipsList = [
      {
        title: tip + '总体满意度',
        color: '#005DCF',
        type: 'block' // block  line  dot
      }, ...tips, {
        title: '股份总体满意度',
        color: '#33C5E2',
        type: 'line'
      }
    ]
    // 格式化分位线
    let allStatisticsMarkLine = scoreMapList.map((item, index) => {
      return {
        name: item.score,
        yAxis: parseInt(item.sat),
        label: {
          show: true,
          position: 'end',
          color: this.markLineColors[index],
          formatter: function(params) {
            return `${params.value}%`
          }
        },
        lineStyle: {
          color: this.markLineColors[index]
        }
      }
    })
    // 满意度目标线
    allStatisticsMarkLine = [...allStatisticsMarkLine, {
      name: '股份总体满意度',
      yAxis: totalSat || '',
      label: {
        show: true,
        position: 'end',
        color: '#33C5E2',
        formatter: function(params) {
          return `${params.value}%`
        }
      },
      lineStyle: {
        color: '#33C5E2'
      }
    }]
    const option = {
      color: ['#005DCF'],
      tooltip: {
        className: 'echarts-tooltip',
        trigger: 'axis',
        formatter: function(params) {
          const { name, value, dataIndex } = params[0]
          return `
            <div>${name}</div>
            <div><span>满意度：${formatterTable(value)}%</span></div>
            ${has(allStatisticsList[dataIndex], 'satGoal') && showYearGoal ? `<div><span>年度目标：${formatterTable(allStatisticsList[dataIndex].satGoal)}%</span></div>` : ''}
            <div><span>总样框：${formatterTable(allStatisticsList[dataIndex].unWeightFrameNum)}</span></div>
            <div><span>加权样框：${formatterTable(allStatisticsList[dataIndex].totalSendNum)}</span></div>
            <div><span>样本量：${formatterTable(allStatisticsList[dataIndex].recycleNum)}</span></div>
          `
        }
      },
      grid: {
        left: '20px',
        right: '60px',
        bottom: '5%',
        top: '10%',
        containLabel: true
      },
      xAxis: {
        type: 'category',
        axisTick: {
          alignWithLabel: true
        },
        axisLabel: {
          interval: 0,
          formatter: function(params) {
            if (params.length < 6) {
              return params
            } else if (params.length < 11) {
              return params.substr(0, 5) + '\n' + params.substr(5)
            } else {
              return params.substr(0, 5) + '\n' + params.substr(5, 4) + '...'
            }
          }
        },
        data: allStatisticsList.map(item => item.projectName || item.cityCompanyName || item.areaCompanyName)
      },
      yAxis: [
        {
          type: 'value',
          // name: '%',
          min: 0,
          max: 100,
          splitLine: {
            lineStyle: {
              type: 'dashed'// 虚线
            }
          },
          axisLine: {
            show: true
          },
          axisLabel: {
            formatter: '{value}'
          }
        }
      ],
      series: [
        {
          name: '总体满意度',
          type: 'bar',
          data: allStatisticsList.map(item => item.sat),
          label: {
            show: true,
            position: 'top',
            formatter({ value }) {
              return value + '%'
            }
          },
          markLine: {
            symbol: ['none', 'none'],
            lineStyle: {
              type: 'solid'
            },
            data: allStatisticsMarkLine
          },
          barMaxWidth: this.barWidth
        }
      ]
    }

    return [
      allTipsList,
      option
    ]
  }
}

export const tips = {
  '认购': '认购后24小时内',
  '签约': '签约后24小时内',
  '准业主1': '签约后第2个月',
  '准业主2': '签约后第7个月',
  '准业主3': '签约后第13个月',
  '交付': '收楼后24小时内',
  '磨合期1': '收楼后第2个月',
  '磨合期2': '收楼后第7个月',
  '稳定期': '收楼后第13个月',
  '老业主1': '收楼后第25个月',
  '老业主2': '收楼后2~5年',
  '老业主3': '收楼后5~20年',
  '老业主4': '收楼后20~30年',
  '老业主5': '收楼后30年以上',
  '维修工单': '完结后1小时内',
  '投诉工单': '完结后1小时内',
  '咨询工单': '完结后1小时内'
}

export function getDefaultMonthRange(searchForm) {
  const currentYear = searchForm?.statisticsYear ? new Date(searchForm?.statisticsYear).getFullYear() : new Date().getFullYear()
  const currentDate = new Date()
  if (currentYear === currentDate.getFullYear()) {
    return [currentDate.getFullYear() + '-01', currentDate.getFullYear() + '-' + (currentDate.getMonth() + 1)]
  } else {
    return [currentYear + '-01', currentYear + '-12']
  }
}
