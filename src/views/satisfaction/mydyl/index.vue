<template>
  <div class="g-container">
    <el-card class="card-pd0 margin-b-20">
      <el-row type="flex" justify="space-between" align="middle">
        <CommonTabs :tabs="tabList" class="margin-b-20" @changeTab="changeTab" />
        <span class="margin-b-20">数据更新时间：{{ updateTime || '--' }}</span>
      </el-row>
    </el-card>
    <keep-alive>
      <component :is="avtive" :has-weight-permission="hasWeightPermission" />
    </keep-alive>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs/index.vue'
import Overall from '@/views/satisfaction/mydyl/components/overall.vue'
import Area from '@/views/satisfaction/mydyl/components/area.vue'
import City from '@/views/satisfaction/mydyl/components/city.vue'
import Project from '@/views/satisfaction/mydyl/components/project.vue'
import Score from '@/views/satisfaction/mydyl/components/Score.vue'
import { UseBasicData } from './hooks'
export default {
  name: 'SatisfactionMydyl',
  components: { CommonTabs, Overall, Area, City, Project, Score },
  mixins: [getList1],
  props: {},
  data() {
    return {
      tabList: [
        {
          label: '总体概览',
          value: 'Overall'
        },
        {
          label: '区域排名',
          value: 'Area'
        },
        {
          label: '城市排名',
          value: 'City'
        },
        {
          label: '项目排名',
          value: 'Project'
        },
        {
          label: '绩效考核',
          value: 'Score'
        }
      ],
      avtive: 'Overall',
      updateTime: null,
      useBasic: new UseBasicData(),
      hasWeightPermission: false
    }
  },
  created() {
    this.hasWeightPermission = this.$checkPermission(['MYDFX_MYDGL_ZNQZ'])
  },
  mounted() {},
  methods: {
    changeTab(val) {
      this.avtive = val
    },
    setUpdateTime(time) {
      this.updateTime = time
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
}
</style>

