<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="区域/城市：">
          <UmBusOrgan v-model="searchForm.projDto" />
        </el-form-item>
        <el-form-item label="项目/分期：">
          <UmBusProject v-model="searchForm.projDto" :area-company-id="searchForm.projDto.areaCompanyId" :city-company-id="searchForm.projDto.cityCompanyId" />
        </el-form-item>
        <el-form-item prop="pointId" label="调研触点：">
          <UmBusScene v-model="searchForm.pointId" placeholder="请选择" />
        </el-form-item>
        <el-form-item prop="examineCode" label="是否考核：">
          <el-select v-model="searchForm.examineCode" filterable clearable style="width: 100%" placeholder="请选择">
            <el-option label="考核" :value="EXAMINE_STATUS.YES" />
            <el-option label="不考核" :value="EXAMINE_STATUS.NO" />
          </el-select>
        </el-form-item>
        <el-form-item label="年度：" prop="statisticsYear">
          <el-date-picker
            v-model="searchForm.statisticsYear"
            type="year"
            placeholder="选择年"
            value-format="yyyy"
            style="width: 100%"
            :clearable="false"
          />
        </el-form-item>
        <el-form-item prop="statisticsMonth" label="月份：">
          <el-select v-model="searchForm.statisticsMonth" style="width: 100%" clearable>
            <el-option v-for="(item, index) in ['一', '二', '三', '四', '五', '六', '七', '八', '九', '十', '十一', '十二',]" :key="item" :label="item + '月'" :value="index + 1" />
          </el-select>
        </el-form-item>
      </template>
      <template #suffix>
        <el-button v-if="$checkPermission(['MYDFX_HFGL_DC'])" :loading="exportLoading" icon="el-icon-upload2" type="primary" @click="exportData">导出</el-button>
        <el-button type="primary" :loading="tableLoading" icon="el-icon-search" style="margin-bottom: 40px;" @click="activeTab === 1 ? getList() : getOverViewGather()">搜索</el-button>
      </template>
    </UmSearchLayout>
    <el-card
      v-loading="tableLoading"
      class="g-container__btm"
    >
      <el-row :gutter="30" class="margin-b-20">
        <el-col :span="12">
          <CommonTabs :tabs="tabList" @changeTab="changeTab" />
        </el-col>
      </el-row>
      <um-table-full
        ref="tableData"
        :data="tableData"
        scroll
        style="width: 100%;"
        :row-class-name="rowClass"
      >
        <el-table-column
          key="pointName"
          label="触点名称"
          prop="pointName"
          min-width="160"
        />
        <template v-if="activeTab === 1">
          <el-table-column
            key="areaCompanyName"
            label="区域"
            prop="areaCompanyName"
            width="160"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="cityCompanyName"
            label="城市公司"
            prop="cityCompanyName"
            width="160"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="projectName"
            label="项目"
            prop="projectName"
            width="160"
            :formatter="$formatterTable"
          />
          <el-table-column
            key="stageName"
            label="分期"
            prop="stageName"
            width="160"
            :formatter="$formatterTable"
          />
        </template>

        <el-table-column
          key="houseNum"
          label="样框量"
          prop="houseNum"
          width="120"
        />

        <!-- <el-table-column
            key="totalSendNum"
            label="总样本量"
            prop="totalSendNum"
            width="120"
          /> -->
        <el-table-column
          key="recycleNum"
          label="回收样本量"
          prop="recycleNum"
          width="120"
        >
          <template slot="header">
            <span>回收样本量</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="全部回收的问卷量"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          key="recycleRate"
          label="回收率"
          prop="recycleRate"
          width="100"
        >
          <template slot="header">
            <span>回收率</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="回收样本量/样框量"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          key="successSendNum"
          label="网络推送量"
          prop="successSendNum"
          width="120"
        >
          <template slot="header">
            <span>网络推送量</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="通过短信、公众号、小程序等线上渠道成功推送的问卷数量。"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          key="onlineRecycleNum"
          label="网络回收样本量"
          prop="onlineRecycleNum"
          width="140"
        >
          <template slot="header">
            <span>网络回收样本量</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="通过短信、公众号、小程序等线上渠道推送并回收的问卷数量。"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          key="successSendRecycleRate"
          label="网络回收率"
          prop="successSendRecycleRate"
          width="140"
        >
          <template slot="header">
            <span>网络回收率</span>
            <el-tooltip
              class="item"
              effect="dark"
              content="网络回收样本量/网络推送量"
              placement="top"
            >
              <i class="el-icon-question" />
            </el-tooltip>
          </template>
        </el-table-column>
        <el-table-column
          key="mobileRecycleNum"
          prop="mobileRecycleNum"
          label="电话回收样本量"
          width="140"
        />
      </um-table-full>

      <pagination
        v-if="activeTab === 1"
        style="margin-top: 20px;padding-bottom: 20px"
        :total="+tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        @pagination="getList('page')"
      />
      <div v-else style="height: 20px;" />
    </el-card>
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { getOverViewGather, getDetailPage } from '@/api/satisfaction/returnVisit'
import { EXAMINE_STATUS } from '@/enum'
import CommonTabs from '@/components/CommonTabs'
import { exportGatherData, exportDetailData } from '@/api/export'
import exportData from '@/mixins/exportData'
import Big from 'big.js'
const keys = ['houseNum', 'totalSendNum', 'recycleNum', 'recycleRate', 'onlineRecycleNum', 'mobileRecycleNum', 'successSendNum', 'successSendRecycleRate']
export default {
  name: 'SatisfactionAbnormal',
  components: { CommonTabs },
  mixins: [getList, exportData],
  data() {
    return {
      listApi: getDetailPage,
      exportApi: exportGatherData,
      EXAMINE_STATUS,
      tableLoading: false,
      searchForm: {
        statisticsYear: new Date(),
        statisticsMonth: null,
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null,
        stageId: null,
        pointId: null,
        examineCode: null,
        projDto: {}
      },
      activeTab: 0,
      tabList: [
        {
          label: '汇总',
          value: 0
        },
        {
          label: '明细',
          value: 1
        }
      ]
    }
  },
  activated() {
    this.getOverViewGather()
  },
  created() {
  },
  methods: {
    formatExportParams() {
      this.exportApi = this.activeTab === 1 ? exportDetailData : exportGatherData
      return this.beforeApiCallBack(this.searchForm)
    },
    changeTab(v) {
      this.activeTab = v
      if (v === 1) {
        this.getList()
      } else {
        this.getOverViewGather()
      }
    },
    getOverViewGather() {
      this.tableLoading = true
      getOverViewGather(this.beforeApiCallBack(this.searchForm)).then(res => {
        const arr = res.data?.list || res.data || [] // 这里看文档包了list，所以先兼容写法，后续可以去掉
        const totalItem = {
          pointName: '总计',
          isTotal: true
        }
        /**
           * onlineRecycleNum 网络回收量
           * successSendNum 网络推送量
           */
        // 合计里面，网络推送量就是简单的累加，网络回收率是网络回收样本量/网络推送量*100%
        if (arr.length) {
          arr.forEach(item => {
            keys.forEach(key => {
              if (key !== 'successSendRecycleRate') {
                totalItem[key] = this.computedTotal(totalItem[key], item[key])
              }
            })
          })
          keys.forEach(key => {
            if (key === 'recycleRate') { // 回收率取平均值，
              totalItem[key] = totalItem.houseNum ? new Big(totalItem.recycleNum || 0).div(totalItem.houseNum).times(100).toFixed(2) + '%' : '0%'
            } else if (key === 'successSendRecycleRate') {
              totalItem[key] = totalItem.successSendNum === 0 ? 0 : new Big(totalItem.onlineRecycleNum || 0).div(totalItem.successSendNum || 0).times(100).toFixed(2) + '%'
            } else {
              totalItem[key] = parseFloat(totalItem[key])
            }
          })
          arr.unshift(totalItem)
        }

        this.tableData = arr
      }).finally(() => {
        this.tableLoading = false
      })
    },
    rowClass({ row }) {
      return row.isTotal ? 'total' : ''
    },
    computedTotal(num1, num2) {
      return new Big(parseFloat(num1) || 0).plus(parseFloat(num2) || 0).toFixed(2)
    },
    beforeApiCallBack(params) {
      return {
        ...params,
        statisticsYear: new Date(params.statisticsYear).getFullYear(),
        statisticsMonth: this.searchForm.statisticsMonth ? new Date(params.statisticsYear).getFullYear() + '-' + (this.searchForm.statisticsMonth.toString().padStart(2, 0)) : null,
        ...this.searchForm.projDto
      }
    },
    organSuccess({ areaCompanyId, cityCompanyId }) {
      this.searchForm.areaCompanyId = areaCompanyId
      this.searchForm.cityCompanyId = cityCompanyId
    },
    projectSuccess({ projectId, stageId }) {
      this.searchForm.projectId = projectId
      this.searchForm.stageId = stageId
    }
  }
}
</script>
  <style lang="scss" scoped>
  .g-container {
    height: 100%;
    display: flex;
    flex-direction: column;
    ::v-deep {
      .el-card__body {
        padding-bottom: 0;
      }
      .el-table.el-table--striped .el-table__body tr.el-table__row.total td.el-table__cell{
        background-color: $--color-primary!important;
        color: #fff!important;
      }
    }
    .el-icon-question {
      color: #8B8B7A;
      cursor: pointer;
      margin-left: 2px;
    }
    &__btm {
      flex: 1;
      overflow-y: auto;
      ::v-deep .el-card__body {
        height: 100%;
        display: flex;
        flex-direction: column;
      }
    }
    ::v-deep.el-date-editor .el-range-separator {
      width: 10%;
    }
  }
  </style>

