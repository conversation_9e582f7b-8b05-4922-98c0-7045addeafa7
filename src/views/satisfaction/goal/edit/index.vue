<!--行业对标-->
<template>
  <div class="g-container" :class="{ detail: isDetail }">
    <el-card class="card-pd0">
      <el-form label-width="100px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="name" label="满意度目标">
              <el-input v-model="name" placeholder="请输入" disabled />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="year" label="预算年份">
              <el-date-picker
                v-model="year"
                type="year"
                placeholder="请选择年份"
                style="width: 100%"
                disabled
              />
            </el-form-item>
          </el-col>
          <el-button
            v-if="$checkPermission(['MYDFX_MYDMB_DC']) && isDetail"
            :loading="exportLoading"
            style="float: right"
            icon="el-icon-upload2"
            type="primary"
            class="margin-r-20"
            @click="exportData"
          >
            导出
          </el-button>
        </el-row>
      </el-form>
      <el-steps :active="currentStep" align-center>
        <el-step title="提交样框" />
        <el-step title="目标拆解" />
        <el-step title="提交目标" />
        <el-step title="完成" />
      </el-steps>
      <component
        :is="currentComponent[currentStep]"
        :is-detail="isDetail"
        :all-area-list="allAreaList"
        :all-city-list="allCityList"
        :all-project-list="allProjectList"
        @setStep="setStep"
      />
    </el-card>
  </div>
</template>

<script>
import StepOne from './components/StepOne.vue'
import StepTwo from './components/StepTwo.vue'
import StepThree from './components/StepThree.vue'
import StepFour from './components/StepFour.vue'
import { mapGetters, mapActions } from 'vuex'
import exportData from '@/mixins/exportData'
import { exportGoalItem } from '@/api/satisfaction/goal'
export default {
  name: 'SatisfactionGoalEdit',
  components: { StepOne, StepTwo, StepThree, StepFour },
  mixins: [exportData],
  data() {
    return {
      exportApi: exportGoalItem,
      name: null,
      year: null,
      currentStep: null,
      currentComponent: {
        1: 'StepOne', 2: 'StepTwo', 3: 'StepThree', 4: 'StepFour'
      },
      searchForm: {
        year: null,
        configId: null
      },
      processStatusCode: null, // 2300091 提交样框 2300092 目标拆解 2300093 提交目标 2300094 完成
      isDetail: false,
      allAreaList: [], // 【有权限】区域数据
      allCityList: [], // 【有权限】城市数据
      allProjectList: [] // 【有权限】项目数据
    }
  },
  computed: {
    ...mapGetters(['authOrganTree', 'organTree', 'hasGuFenAuth'])
  },
  watch: {
    '$route': {
      immediate: true,
      handler(v) {
        if (v.path.indexOf('detail') !== -1) {
          this.isDetail = true
        }
      }
    },
    processStatusCode(val) {
      switch (val) {
        case 2300091:
          this.currentStep = 1; break
        case 2300092:
          this.currentStep = 2; break
        case 2300093:
          this.currentStep = 3; break
        case 2300094:
          this.currentStep = 4; break
      }
    }
  },
  async created() {
    const { year, name, processStatusCode, id } = this.$route.query
    this.year = year
    this.name = name
    this.searchForm.year = year
    this.searchForm.configId = id
    await Promise.all([
      this['user/getAuthOrganTree'](),
      this['user/getGuFenAuth']()
    ])
    this.formatData()
    this.processStatusCode = +processStatusCode
  },
  mounted() {

  },
  methods: {
    ...mapActions(['user/getAuthOrganTree', 'user/getGuFenAuth']),
    // 处理【有权限】省市区数据
    formatData() {
      this.organTree.forEach(item => {
        item.codes = item.codes.replace(item.code, '').split(',').filter(item => item)
        if (item.codes.length === 0) {
          this.allAreaList.push(item)
        } else if (item.codes.length === 1) {
          this.allCityList.push(item)
        } else if (item.codes.length === 2) {
          this.allProjectList.push(item)
        }
      })
    },
    setStep(step) {
      // this.currentStep = step
      this.$router.replace({
        query: {
          year: this.year,
          name: this.name,
          id: this.$route.query.id,
          processStatusCode: [2300091, 2300092, 2300093, 2300094][step - 1]
        }
      })
      this.currentStep = step
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: calc(100% - 80px);
  margin-bottom: 80px;
  overflow-y: auto;
  &.detail {
    overflow-y: visible;
  }
  &__body {
    background: #fff;
    padding: 20px;
  }
}
.tips {
  font-size: 12px;
  color: #999999;
  text-align: right;
  line-height: 12px;
  font-weight: 400;
  margin-right: 20px;
}
.applyTips {
  font-size: 12px;
  color: #F8716B;
  letter-spacing: 0;
  line-height: 12px;
  font-weight: 500;
  min-height: 24px;
  background: rgba(248,113,107,0.10);
  border-radius: 4px;
  display: flex;
  align-items: center;
  padding: 6px 0;
  .applyTips-nei {
    padding-left: 10px;
    padding-right: 10px;
    border-left: 2px solid #F8716B;
    display: flex;
    line-height: 1.5;
    > span:last-child {
      flex: 1;
    }
  }
}
.btn-groups {
  margin-bottom: 20px;
  .btn-items {
    height: 32px;
    border: 1px solid rgba(216,220,230,1);
    border-radius: 4px;
    padding: 0 20px;
    line-height: 32px;
    font-size: 14px;
    color: #333333;
    letter-spacing: 0;
    font-weight: 400;
    text-align: center;
    margin-right: 12px;
    cursor: pointer;
  }
  .btn-items.click {
    border: 1px solid $--color-primary;
    color: $--color-primary;
  }
}
</style>
