<template>
  <el-dialog
    :visible.sync="showDialog"
    :title="`选择退回${label[orgTypeCode]}`"
  >
    <um-table-full ref="table" :data="tableData" row-key="value" @selection-change="handleSelectionChange">
      <el-table-column type="selection" :selectable="selectable" />
      <el-table-column :label="label[orgTypeCode]" prop="label" />
    </um-table-full>
    <div slot="footer" class="dialog-footer">
      <el-button @click="showDialog = false">取消</el-button>
      <el-button type="primary" @click="confirm">确定</el-button>
    </div>
  </el-dialog>
</template>

<script>
import { cancelLockItem } from '@/api/satisfaction/goal'
import { ORG_TYPE_CODE } from './enum'
export default {
  name: 'ReturnBack',
  props: {
    show: {
      type: Boolean,
      default: false
    },
    configId: {
      type: String,
      default: ''
    },
    tableData: {
      type: Array,
      default: () => []
    },
    orgTypeCode: {
      type: [String, Number],
      default: null
    },
    disabledKeys: {
      type: Array,
      default: () => []
    },
    searchForm: {
      type: Object,
      default: () => ({})
    }
  },
  data() {
    return {
      loading: false,
      ORG_TYPE_CODE,
      multipleSelection: [],
      label: {
        [ORG_TYPE_CODE.HEAD]: '区域',
        [ORG_TYPE_CODE.AREA]: '城市',
        [ORG_TYPE_CODE.CITY]: '项目'
      }
    }
  },
  computed: {
    showDialog: {
      get() {
        return this.show
      },
      set(value) {
        this.$emit('update:show', value)
      }
    }
  },
  watch: {
    showDialog(v) {
      if (!v) {
        this.multipleSelection = []
        this.$refs.table?.el.clearSelection()
      }
    }
  },
  methods: {
    handleSelectionChange(val) {
      this.multipleSelection = val
    },
    confirm() {
      if (!this.multipleSelection.length) {
        this.$message.warning('请选择退回的目标')
        return
      }
      const load = this.$load()
      cancelLockItem({
        configId: this.configId,
        orgTypeCode: this.orgTypeCode,
        areaCompanyId: this.searchForm.areaCompanyId,
        cityCompanyId: this.searchForm.cityCompanyId,
        areaCompanyIdList: this.orgTypeCode === ORG_TYPE_CODE.HEAD ? this.multipleSelection.map(item => item.value) : [],
        cityCompanyIdList: this.orgTypeCode === ORG_TYPE_CODE.AREA ? this.multipleSelection.map(item => item.value) : [],
        projectIdList: this.orgTypeCode === ORG_TYPE_CODE.CITY ? this.multipleSelection.map(item => item.value) : []
      }).then(res => {
        this.showDialog = false
        this.$emit('success')
      }).finally(() => {
        load.close()
      })
    },
    selectable(row, index) {
      return !this.disabledKeys.includes(row.value)
    }
  }
}
</script>
