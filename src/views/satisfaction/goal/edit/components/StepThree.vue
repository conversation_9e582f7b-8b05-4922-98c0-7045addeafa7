<!--行业对标-->
<template>
  <div class="g-container__body margin-t-20">
    <el-row :gutter="30" class="margin-b-20">
      <el-col :span="12">
        <CommonTabs :tabs="tabList" @changeTab="changeTabs" />
      </el-col>
      <div style="float: right">
        <span class="tips">数据更新时间：{{ baseInfo.modifyTime || '--' }}</span>
        <el-button
          v-if="$checkPermission(['MYDFX_MYDMB_CZRZ'])"
          type="primary"
          class="margin-r-20"
          @click="toLog"
        >
          查看日志
        </el-button>
      </div>
    </el-row>
    <!-- 大区切换 -->
    <div v-if="searchForm.orgTypeCode === ORG_TYPE_CODE.AREA" class="flex btn-groups">
      <div
        v-for="item in allAreaList"
        :key="item.code"
        class="btn-items"
        :class="areaParams.areaCompanyId === item.code ? 'click' : ''"
        @click="areaParams.areaCompanyId = item.code;changeTabs(ORG_TYPE_CODE.AREA)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- 城市切换 -->
    <div v-if="searchForm.orgTypeCode === ORG_TYPE_CODE.CITY" class="flex btn-groups">
      <el-select v-model="cityParams.areaCompanyId" filterable style="width: 132px;margin-right: 20px" placeholder="请选择" @change="getCityList">
        <el-option
          v-for="item in allAreaList"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
      <div
        v-for="item in cityList"
        :key="item.code"
        class="btn-items"
        :class="cityParams.cityCompanyId === item.code ? 'click': ''"
        @click="cityParams.cityCompanyId = item.code;changeTabs(ORG_TYPE_CODE.CITY)"
      >
        {{ item.name }}
      </div>
    </div>
    <!-- 项目切换 -->
    <div v-if="searchForm.orgTypeCode === ORG_TYPE_CODE.PROJECT" class="flex btn-groups">
      <el-select-tree
        ref="elSelectTree"
        v-model="projectParams"
        is-leaf
        placeholder="请选择"
        :options="organList"
        style="width: 300px"
        filterable
        :show-all-levels="true"
        :check-strictly="false"
        :default-props="{
          emitPath: true,
          label: 'name',
          value: 'code',
        }"
        @change="changeTabs(ORG_TYPE_CODE.PROJECT)"
      />
    </div>
    <div v-if="unCommitList.length" class="applyTips">
      <div class="applyTips-nei"><span>未上报：</span><span>{{ unCommitList.map(item => item.name).join('、') }}</span></div>
    </div>
    <DynamicTable ref="DynamicTable" :process-status-code="2300093" :standard-satisfaction="baseInfo.standardSatisfaction" title="纳入考核" :is-detail="isDetail" :table-date="checkTableData" :dynamic-headers="dynamicHeaders" :org-type-code="searchForm.orgTypeCode" />
    <DynamicTable :process-status-code="2300093" title="暂不纳入考核" :is-detail="isDetail" :table-date="unCheckTableData" :dynamic-headers="dynamicHeaders" :org-type-code="searchForm.orgTypeCode" />

    <div v-if="!isDetail" class="m-fixed__btm">
      <!-- 2300021 待发布 2300022 已发布 -->
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_TJMB_FHSYB'])" type="primary" plain @click="undoConfig(2)">返回上一步</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_TJMB_BC'])" v-show="baseInfo.orgTypeCode === ORG_TYPE_CODE.PROJECT" type="primary" :disabled="btnAuth || baseInfo.statusCode === 2300022" plain @click="save(2300021)">保存</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_TJMB_BCBTJ'])" type="primary" :disabled="btnAuth || baseInfo.statusCode === 2300022" @click="save(2300022)">保存并提交</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_TJMB_TH'])" v-show="baseInfo.orgTypeCode !== ORG_TYPE_CODE.PROJECT" type="primary" :disabled="btnAuth || !baseInfo.canCancelFlag" @click="cancelLockItem">退回</el-button>
    </div>
    <ReturnBack
      :config-id="searchForm.configId"
      :show.sync="showReturnBack"
      :table-data="returnBackData"
      :disabled-keys="unCommitList.map(item => item.id)"
      :org-type-code="searchForm.orgTypeCode"
      :search-form="searchForm"
      @success="getGoalItemDetail"
    />
  </div>
</template>

<script>
import CommonTabs from '@/components/CommonTabs/index.vue'
import { getGoalItemDetail, saveItem } from '@/api/satisfaction/goal'
import DynamicTable from './DynamicTable'
import { mapGetters } from 'vuex'
import ReturnBack from './ReturnBack'
import { ORG_TYPE_CODE } from './enum'
import mixins from './mixins'
export default {
  name: 'StepThree',
  components: { CommonTabs, DynamicTable, ReturnBack },
  mixins: [mixins],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ORG_TYPE_CODE,
      searchForm: {
        configId: null,
        orgTypeCode: ORG_TYPE_CODE.HEAD,
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null
      },
      baseInfo: {}, // 基础数据信息
      unCommitList: [], // 未上报组织名称
      checkTableData: [], // 纳入考核数据
      unCheckTableData: [], // 不纳入考核数据
      dynamicHeaders: [], // 动态表头
      cityList: [], // 城市数据
      organList: [], // 区域到项目 三级树
      areaParams: {}, // 区域合计筛选条件
      cityParams: {
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null
      }, // 城市合计筛选条件
      projectParams: {}, // 项目合计筛选条件
      tabList: [
        {
          label: '总部合计',
          value: ORG_TYPE_CODE.HEAD
        },
        {
          label: '区域合计',
          value: ORG_TYPE_CODE.AREA
        },
        {
          label: '城市合计',
          value: ORG_TYPE_CODE.CITY
        },
        {
          label: '项目目标',
          value: ORG_TYPE_CODE.PROJECT
        }
      ],
      showReturnBack: false,
      returnBackData: []
    }
  },
  computed: {
    ...mapGetters(['authOrganTree', 'hasGuFenAuth']),
    btnAuth() { // 按钮权限根据当前角色是否拥有对应权限控制
      let flag = true
      switch (this.searchForm.orgTypeCode) {
        case ORG_TYPE_CODE.HEAD: flag = !this.hasGuFenAuth; break
        case ORG_TYPE_CODE.AREA:
          flag = this.authOrganTree.findIndex(item => item.code === this.searchForm.areaCompanyId) === -1
          break
        case ORG_TYPE_CODE.CITY:
          flag = this.authOrganTree.findIndex(item => item.code === this.searchForm.cityCompanyId) === -1
          break
        case ORG_TYPE_CODE.PROJECT: flag = false; break
      }
      return flag
    }
  },
  async created() {
    const { id } = this.$route.query
    this.searchForm.configId = id
    this.getGoalItemDetail()
  },
  methods: {
    getGoalItemDetail() {
      const load = this.$load()
      getGoalItemDetail(this.searchForm).then(res => {
        const { baseInfo, baseInfo: { unCommitList }, itemList } = res.data
        this.baseInfo = baseInfo || {}
        this.unCommitList = unCommitList || []
        this.formatTableData(itemList)
      }).finally(() => load.close())
    },
    formatTableData(arr) {
      const checkTableData = []
      const unCheckTableData = []
      arr.forEach(item => {
        // 判断是否汇总数据，汇总数据，修改pointName为汇总
        if (item.gatherFlag) {
          item.pointName = '汇总'
        }
        // 根据是否考核数据塞进不同数组
        item.checkFlag === 1 ? checkTableData.push(item) : unCheckTableData.push(item)
      })
      this.checkTableData = checkTableData
      this.unCheckTableData = unCheckTableData
    },
    // 查看日志
    toLog() {
      this.$router.push('/satisfaction/goal/log?id=' + this.searchForm.configId)
    },
    // 切换大区
    async changeTabs(val) {
      switch (val) {
        case ORG_TYPE_CODE.HEAD:
          this.searchForm.areaCompanyId = null
          this.searchForm.cityCompanyId = null
          this.searchForm.projectId = null
          break
        case ORG_TYPE_CODE.AREA:
          if (!this.areaParams.areaCompanyId) {
            this.areaParams.areaCompanyId = this.allAreaList[0]?.code
          }
          this.searchForm.areaCompanyId = this.areaParams.areaCompanyId
          this.searchForm.cityCompanyId = null
          this.searchForm.projectId = null
          break
        case ORG_TYPE_CODE.CITY:
          // 如果没有区域id,
          if (!this.cityParams.areaCompanyId) {
            this.cityParams.areaCompanyId = this.allAreaList[0]?.code
          }
          // 如果没有城市id
          if (!this.cityParams.cityCompanyId) {
            this.getCityList(this.cityParams.areaCompanyId)
            this.cityParams.cityCompanyId = this.cityList[0]?.code
          }
          this.searchForm.areaCompanyId = this.cityParams.areaCompanyId
          this.searchForm.cityCompanyId = this.cityParams.cityCompanyId
          this.searchForm.projectId = null
          break
        case ORG_TYPE_CODE.PROJECT:
          if (!this.projectParams.length) {
            await this.getTreeStage()
          }
          [this.searchForm.areaCompanyId, this.searchForm.cityCompanyId, this.searchForm.projectId] = this.projectParams
          break
      }
      this.searchForm.orgTypeCode = val
      this.getGoalItemDetail()
    },
    save(statusCode) {
      // 提交并保存需要检验
      if (statusCode === 2300022 && this.$refs.DynamicTable.notSubmit) {
        this.$message.warning('不能低于满意度标准')
        return
      }
      this.$confirm(`确定${statusCode === 2300021 ? '保存' : '保存并提交'}么？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const itemList = this.checkTableData.concat(this.unCheckTableData).filter(item => !item.gatherFlag).map(item => {
          return {
            itemId: item.itemId,
            pointId: item.pointId,
            pointName: item.pointName,
            forecastFrameNum: item.forecastFrameNum,
            yearSatisfaction: item.yearSatisfaction,
            checkFlag: item.checkFlag
          }
        })
        const load = this.$load()
        saveItem({
          ...this.searchForm,
          statusCode,
          itemList
        }).then(res => {
          this.$message.success('操作成功')
          // 总部保存提交，进入下一步
          if (this.searchForm.orgTypeCode === ORG_TYPE_CODE.HEAD && statusCode === 2300022) {
            this.$emit('setStep', 4)
            return
          }
          this.getGoalItemDetail()
        }).finally(() => load.close())
      })
    },
    async cancelLockItem() {
      switch (this.searchForm.orgTypeCode) {
        case ORG_TYPE_CODE.HEAD:
          this.returnBackData = this.allAreaList.map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
          break
        case ORG_TYPE_CODE.AREA:
          this.returnBackData = this.allCityList.filter(item => {
            return item.codes.includes(this.areaParams.areaCompanyId)
          }).map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
          break
        case ORG_TYPE_CODE.CITY:
          this.returnBackData = this.getProjectList(this.cityParams.cityCompanyId).map(item => {
            return {
              label: item.name,
              value: item.code
            }
          })
          break
      }
      this.showReturnBack = true
    }
  }
}
</script>

<style lang="scss" scoped>
@import url(./common.scss);
</style>
