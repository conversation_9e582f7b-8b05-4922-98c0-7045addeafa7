import { getTreeStage } from '@/api/common'
import { undoConfig } from '@/api/satisfaction/goal'
export default {
  props: {
    allAreaList: {
      type: Array,
      default: () => []
    },
    allCityList: {
      type: Array,
      default: () => []
    },
    allProjectList: {
      type: Array,
      default: () => []
    }
  },
  methods: {
    // 获取城市数据
    getCityList(areaId) {
      this.cityList = this.allCityList.filter(item => {
        return item.codes.includes(areaId)
      })
    },
    // 获取项目数据
    getProjectList(cityId) {
      return this.allProjectList.filter(item => {
        return item.codes.includes(cityId)
      })
    },
    // 获取区域到项目数据
    getTreeStage() {
      if (this.organList.length) {
        return Promise.resolve()
      }
      return getTreeStage().then(res => {
        const arr = res.data || []
        // 删除最末级children
        arr.forEach(item => {
          if (Array.isArray(item.children) && item.children.length) {
            item.children.forEach(_item => {
              if (Array.isArray(_item.children) && _item.children.length) {
                _item.children.forEach(_ => {
                  delete _.children
                })
              }
            })
          }
        })
        this.organList = res.data || []
        if (this.organList.length) {
          const obj = this.organList.map(this.findFirstDisabledFalse).filter(item => item)[0]
          this.projectParams = obj.codes.split(',').filter(item => item)
        }
      })
    },
    findFirstDisabledFalse(node) {
      if (!node.children || node.children.length === 0) {
        // 如果节点没有子节点，或者子节点为空数组，则返回当前节点
        return node
      }
      // 遍历子节点
      for (const child of node.children) {
        // 递归调用函数，查找子节点中第一个 disabled 属性为 false 的节点
        const result = this.findFirstDisabledFalse(child)
        // 如果找到了符合条件的节点，则返回结果
        if (result && !result.disabled) {
          return result
        }
      }
      // 如果当前节点和所有子节点都没有符合条件的节点，则返回 null
      return false
    },
    // 返回上一步
    undoConfig(step) {
      this.$confirm('确定返回上一步么？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        undoConfig({ configId: this.searchForm.configId }).then(res => {
          const { organNameList } = res.data
          if (organNameList?.length) {
            this.$alert(organNameList.join(','), '以下项目已提交目标，无法返回上一步', {
              confirmButtonText: '确定',
              type: 'warning'
            })
            return
          }
          this.$message.success('操作成功')
          this.$emit('setStep', step)
        })
      })
    }
  }
}
