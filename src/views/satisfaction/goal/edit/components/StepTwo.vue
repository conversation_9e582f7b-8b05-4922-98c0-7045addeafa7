<!--行业对标-->
<template>
  <div class="g-container__body margin-t-20">
    <el-row :gutter="30" class="margin-b-20">
      <el-col :span="12">
        <CommonTabs :tabs="tabList" @changeTab="changeTabs" />
      </el-col>
      <div style="float: right">
        <span class="tips">数据更新时间：{{ baseInfo.modifyTime || '--' }}</span>
        <el-button
          v-if="$checkPermission(['MYDFX_MYDMB_CZRZ'])"
          type="primary"
          class="margin-r-20"
          @click="toLog"
        >
          查看日志
        </el-button>
      </div>
    </el-row>
    <!-- 城市目标 -->
    <div v-if="searchForm.orgTypeCode === ORG_TYPE_CODE.AREA" class="flex btn-groups">
      <el-select v-model="areaParams.areaCompanyId" filterable style="width: 132px;margin-right: 20px" placeholder="请选择" @change="changeTabs(ORG_TYPE_CODE.AREA)">
        <el-option
          v-for="item in allAreaList"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
    </div>
    <!-- 项目目标 -->
    <div v-if="searchForm.orgTypeCode === ORG_TYPE_CODE.CITY" class="flex btn-groups">
      <el-select v-model="cityParams.areaCompanyId" filterable style="width: 132px;margin-right: 20px" placeholder="请选择" @change="getCityList">
        <el-option
          v-for="item in allAreaList"
          :key="item.code"
          :label="item.name"
          :value="item.code"
        />
      </el-select>
      <div
        v-for="item in cityList"
        :key="item.code"
        class="btn-items"
        :class="cityParams.cityCompanyId === item.code ? 'click': ''"
        @click="cityParams.cityCompanyId = item.code;changeTabs(ORG_TYPE_CODE.CITY)"
      >
        {{ item.name }}
      </div>
    </div>
    <div v-if="unCommitList.length" class="applyTips">
      <div class="applyTips-nei"><span>未上报：</span><span>{{ unCommitList.join('、') }}</span></div>
    </div>
    <DynamicTableTwo :is-detail="isDetail" :table-date="tableData" :org-type-code="searchForm.orgTypeCode" :show-edit="showEdit" />
    <div v-if="!isDetail" class="m-fixed__btm">
      <!-- 2300021 待发布 2300022 已发布 -->
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_MBCJ_FHSYB'])" type="primary" plain @click="undoConfig(1)">返回上一步</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_MBCJ_BC'])" v-show="!showEdit" :disabled="btnAuth" type="primary" plain @click="saveSubmit(2300021)">保存</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_MBCJ_TJ'])" v-show="!showEdit" :disabled="btnAuth" type="primary" @click="saveSubmit(2300022)">提交</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_MBCJ_BJ'])" v-show="showEdit" type="primary" plain :disabled="!hasGuFenAuth || btnAuth" @click="showEdit = false">编辑</el-button>
      <el-button v-if="$checkPermission(['MYDFX_MYDMB_MBCJ_SZWB'])" type="primary" :disabled="btnAuth" @click="complete">设置完毕</el-button>
    </div>
  </div>
</template>

<script>
import CommonTabs from '@/components/CommonTabs/index.vue'
import { getStandardDetail, saveStandard } from '@/api/satisfaction/goal'
import DynamicTableTwo from './DynamicTableTwo'
import { mapGetters } from 'vuex'
import mixins from './mixins'
import { ORG_TYPE_CODE } from './enum'

export default {
  name: 'StepTwo',
  components: { CommonTabs, DynamicTableTwo },
  mixins: [mixins],
  props: {
    isDetail: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ORG_TYPE_CODE,
      searchForm: {
        configId: null,
        orgTypeCode: ORG_TYPE_CODE.HEAD,
        areaCompanyId: null,
        cityCompanyId: null,
        projectId: null
      },
      baseInfo: {}, // 基础数据信息
      unCommitList: [],
      tableData: [],
      areaList: [], // 大区数据
      cityList: [], // 城市数据
      areaParams: { // 城市目标合计筛选条件
        areaCompanyId: null,
        cityCompanyId: null
      },
      cityParams: { // 项目目标合计筛选条件
        areaCompanyId: null,
        cityCompanyId: null
      }, // 城市合计筛选条件
      tabList: [
        {
          label: '区域目标',
          value: ORG_TYPE_CODE.HEAD
        },
        {
          label: '城市目标',
          value: ORG_TYPE_CODE.AREA
        },
        {
          label: '项目目标',
          value: ORG_TYPE_CODE.CITY
        }
      ],
      showEdit: false // 是否展示编辑按钮
    }
  },
  computed: {
    ...mapGetters(['authOrganTree', 'hasGuFenAuth']),
    btnAuth() { // 按钮权限根据当前角色是否拥有对应权限控制
      let flag = true
      switch (this.searchForm.orgTypeCode) {
        case ORG_TYPE_CODE.HEAD: flag = !this.hasGuFenAuth; break
        case ORG_TYPE_CODE.AREA:
          flag = this.authOrganTree.findIndex(item => item.code === this.searchForm.areaCompanyId) === -1
          break
        case ORG_TYPE_CODE.CITY:
          flag = this.authOrganTree.findIndex(item => item.code === this.searchForm.cityCompanyId) === -1
          break
      }
      return flag
    }
  },
  async created() {
    const { id } = this.$route.query
    this.searchForm.configId = id
    this.getStandardDetail()
  },
  methods: {
    getStandardDetail() {
      const load = this.$load()
      getStandardDetail(this.searchForm).then(res => {
        const { baseInfo, baseInfo: { unCommitList }, standardList } = res.data
        this.tableData = standardList
        this.showEdit = this.tableData.some(item => item.statusCode === 2300022)
        this.baseInfo = baseInfo || {}
        this.unCommitList = unCommitList || []
      }).finally(() => load.close())
    },
    // 查看日志
    toLog() {
      this.$router.push('/satisfaction/goal/log?id=' + this.searchForm.configId)
    },
    // 切换大区
    async changeTabs(val) {
      console.log(val, this.areaParams.areaCompanyId)
      switch (val) {
        case ORG_TYPE_CODE.HEAD:
          this.searchForm.areaCompanyId = null
          this.searchForm.cityCompanyId = null
          break
        case ORG_TYPE_CODE.AREA:

          // 如果没有区域id,
          if (!this.areaParams.areaCompanyId) {
            this.areaParams.areaCompanyId = this.allAreaList[0]?.code
          }
          this.searchForm.areaCompanyId = this.areaParams.areaCompanyId
          this.searchForm.cityCompanyId = null
          break
        case ORG_TYPE_CODE.CITY:
          // 如果没有区域id,
          if (!this.cityParams.areaCompanyId) {
            this.cityParams.areaCompanyId = this.allAreaList[0]?.code
          }
          // 如果没有城市id
          if (!this.cityParams.cityCompanyId) {
            this.getCityList(this.cityParams.areaCompanyId)
            this.cityParams.cityCompanyId = this.cityList[0]?.code
          }
          this.searchForm.areaCompanyId = this.cityParams.areaCompanyId
          this.searchForm.cityCompanyId = this.cityParams.cityCompanyId
          break
      }
      this.searchForm.orgTypeCode = val
      this.tableData = []
      this.getStandardDetail()
    },
    // 保存提交
    saveSubmit(statusCode) {
      this.$confirm(`确定${statusCode === 2300021 ? '保存' : '提交'}么？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const standardList = this.tableData.map(item => {
          return {
            ...item,
            statusCode
          }
        })
        const load = this.$load()
        saveStandard({
          ...this.searchForm,
          statusCode: 2300021,
          standardList
        }).then(res => {
          this.$message.success('操作成功')
          this.getStandardDetail()
        }).finally(() => load.close())
      })
    },
    // 设置完毕
    complete() {
      this.$confirm('确定全部设置完毕么？', '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const standardList = this.tableData.map(item => {
          return {
            statusCode: 2300022,
            ...item
          }
        })
        const load = this.$load()
        saveStandard({
          ...this.searchForm,
          statusCode: 2300022,
          standardList
        }).then(res => {
          this.$message.success('操作成功')
          this.$emit('setStep', 3)
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scoped>
@import url(./common.scss);
</style>
