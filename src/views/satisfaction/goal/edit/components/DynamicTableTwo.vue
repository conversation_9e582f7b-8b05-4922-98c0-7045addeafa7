<template>
  <el-table
    :data="tableDate"
    stripe
  >
    <el-table-column
      v-if="orgTypeCode === ORG_TYPE_CODE.HEAD"
      key="areaCompanyName"
      label="区域"
      prop="areaCompanyName"
      align="center"
    />
    <el-table-column
      v-if="orgTypeCode === ORG_TYPE_CODE.AREA"
      key="cityCompanyName"
      label="城市"
      prop="cityCompanyName"
      align="center"
    />
    <el-table-column
      v-if="orgTypeCode === ORG_TYPE_CODE.CITY"
      key="projectName"
      label="项目"
      prop="projectName"
      align="center"
    />
    <el-table-column
      prop="frameNum"
      label="样框量"
      align="center"
      :formatter="$formatterTable"
    />
    <el-table-column
      :label="`${$route.query.year}满意度目标`"
      prop="standardSatisfaction"
      align="center"
    >
      <template slot-scope="scope">
        <el-row v-if="!isDetail" type="flex" align="middle" justify="center">
          <el-input-number v-model="scope.row.standardSatisfaction" :disabled="checkDisabled(scope.row)" style="width: 100px;margin-right: 10px;" :precision="2" :min="0" :max="100" :controls="false" /> %
        </el-row>
        <div v-else>{{ scope.row.standardSatisfaction | formatterTable }}%</div>
      </template>
    </el-table-column>
  </el-table>
</template>

<script>
import { mapGetters } from 'vuex'
import { ORG_TYPE_CODE } from './enum'
export default {
  name: 'DynamicTableTwo',
  props: {
    tableDate: {
      type: Array,
      default: () => []
    },
    orgTypeCode: {
      type: [String, Number],
      default: null
    },
    title: {
      type: String,
      default: null
    },
    isDetail: {
      type: Boolean,
      default: false
    },
    showEdit: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      ORG_TYPE_CODE,
      total: 0 // 汇总数据，仅展示
    }
  },
  computed: {
    ...mapGetters(['authOrganTree'])
  },
  methods: {
    checkDisabled(data) {
      let key = null
      switch (this.orgTypeCode) {
        case ORG_TYPE_CODE.HEAD:
          key = 'areaCompanyId'
          break
        case ORG_TYPE_CODE.AREA:
          key = 'cityCompanyId'
          break
        case ORG_TYPE_CODE.CITY:
          key = 'projectId'
          break
      }
      return this.authOrganTree.findIndex(item => item.code === data[key]) === -1 || this.showEdit
    }
  }
}
</script>
