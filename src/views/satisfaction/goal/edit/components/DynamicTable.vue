<template>
  <div>
    <CommonTitle :title="title" margin-bottom="0" padding-left="0" />
    <el-table
      :data="tableDate"
      stripe
    >
      <el-table-column
        label="触点/节点"
        prop="pointName"
        align="center"
      />
      <el-table-column
        prop="forecastFrameNum"
        label="预估样框量"
        align="center"
      >
        <template slot="header">
          <div>预估样框量<span style="color: #F24040;">（注意:按照产权人数预估）</span></div>
        </template>
        <template slot-scope="scope">
          <el-input-number
            v-if="orgTypeCode === ORG_TYPE_CODE.PROJECT && !scope.row.gatherFlag && !isDetail && processStatusCode !== 2300093"
            v-model="scope.row.forecastFrameNum"
            :disabled="scope.row.statusCode === 2300022"
            style="width: 100px"
            :min="0"
            :max="999999"
            :controls="false"
            @blur="computedTotal"
          />
          <div v-else>{{ scope.row.forecastFrameNum | formatterTable }}</div>
        </template>
      </el-table-column>
      <el-table-column
        v-if="processStatusCode === 2300093"
        :label="`${$route.query.year}年度目标`"
        prop="yearSatisfaction"
        align="center"
      >
        <template slot-scope="scope">
          <el-row v-if="orgTypeCode === ORG_TYPE_CODE.PROJECT && !scope.row.gatherFlag && !isDetail" type="flex" align="middle" justify="center">
            <el-input-number

              v-model="scope.row.yearSatisfaction"
              style="width: 100px;margin-right: 10px;"
              :disabled="scope.row.statusCode === 2300022"
              :precision="2"
              :min="0"
              :max="100"
              :controls="false"
              @blur="computedTotal"
            /> %
          </el-row>
          <div v-else-if="scope.row.gatherFlag">
            <el-row type="flex" justify="center">
              <span v-if="orgTypeCode !== ORG_TYPE_CODE.HEAD" style="position: absolute;left: 10px">满意度标准：{{ standardSatisfaction }}%</span>
              <span :class="{ error: checkError(scope.row.yearSatisfaction) }">{{ scope.row.yearSatisfaction }}%</span>
            </el-row>
            <span v-if="checkError(scope.row.yearSatisfaction)" class="error">不能低于满意度标准</span>
          </div>
          <div v-else>{{ scope.row.yearSatisfaction }}%</div>
        </template>
      </el-table-column>
    </el-table>
  </div>
</template>

<script>
import CommonTitle from '@/components/CommonTitle/index.vue'
import BigJs from 'big.js'
import { ORG_TYPE_CODE } from './enum'
export default {
  name: 'DynamicTable',
  components: { CommonTitle },
  props: {
    tableDate: {
      type: Array,
      default: () => []
    },
    orgTypeCode: {
      type: [String, Number],
      default: null
    },
    title: {
      type: String,
      default: null
    },
    isDetail: { // 是否是详情页面
      type: Boolean,
      default: false
    },
    processStatusCode: { // 流程步骤状态码
      type: Number,
      default: null
    },
    standardSatisfaction: { // 标准满意度
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      ORG_TYPE_CODE,
      total: 0, // 汇总数据，仅展示
      notSubmit: false // 不允许提交
    }
  },
  methods: {
    getBigJs(num) {
      return new BigJs(num || 0)
    },
    computedTotal() {
      let sum = this.getBigJs()
      let pointSum = this.getBigJs()
      let forecastFrameNumTotal = this.getBigJs()
      this.tableDate.forEach(item => {
        if (!item.gatherFlag) {
          sum = sum.plus(this.getBigJs(item.forecastFrameNum).times(this.getBigJs(item.yearSatisfaction).div(100)))
          pointSum = pointSum.plus(this.getBigJs(item.forecastFrameNum))
          console.log(item.forecastFrameNum)

          forecastFrameNumTotal = forecastFrameNumTotal.plus(this.getBigJs(item.forecastFrameNum))
        }
      })
      this.tableDate.forEach(item => {
        if (item.gatherFlag) {
          item.yearSatisfaction = pointSum.toNumber() === 0 ? 0 : sum.div(pointSum).times(100).toFixed(2)
          item.forecastFrameNum = forecastFrameNumTotal.toNumber()
        }
      })
    },
    checkError(yearSatisfaction) {
      this.notSubmit = +yearSatisfaction < +this.standardSatisfaction && this.orgTypeCode !== ORG_TYPE_CODE.HEAD
      return this.notSubmit
    }
  }
}
</script>
