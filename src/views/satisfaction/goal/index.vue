<!--行业对标-->
<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form ref="searchForm" :model="searchForm1" label-width="80px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="10" :md="8" :xl="6">
            <el-form-item prop="goalName" label="目标名称">
              <el-input v-model="searchForm1.goalName" placeholder="请输入" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="10" :md="8" :xl="6">
            <el-form-item prop="budgetYear" label="预算年份">
              <el-date-picker
                v-model="searchForm1.budgetYear"
                type="year"
                value-format="yyyy"
                placeholder="请选择年份"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="4" :md="8" :xl="6" class="fr text-right">
            <el-button :loading="tableLoading1" type="primary" icon="el-icon-search" @click="getList1">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row :gutter="30" class="margin-b-20">
        <el-col :span="12">
          <CommonTabs :tabs="tabList" @changeTab="changeTab" />
        </el-col>
        <el-button
          v-if="$checkPermission(['MYDFX_MYDMB_XZ'])"
          style="float: right"
          icon="el-icon-circle-plus-outline"
          type="primary"
          class="margin-r-20"
          @click="addGoal"
        >
          新增
        </el-button>
      </el-row>
      <um-table-full
        :data="tableData1"
        scroll
      >
        <el-table-column
          label="序号"
          width="80"
          type="index"
        />
        <el-table-column
          label="满意度目标"
          min-width="200"
          prop="goalName"
        />
        <el-table-column
          prop="budgetYear"
          label="预算年份"
          min-width="120"
        />
        <el-table-column
          prop="createUser"
          label="创建人"
          width="180"
        />
        <el-table-column
          prop="createTime"
          label="创建时间"
          width="200"
        />
        <el-table-column
          label="操作"
          width="180"
        >
          <template slot-scope="{ row }">
            <el-button v-if="$checkPermission(['MYDFX_MYDMB_XQ'])" type="text" @click="editGoal(row, true)">详情</el-button>
            <el-button v-if="$checkPermission(['MYDFX_MYDMB_BJ']) && row.statusCode === YEAR_TARGET_STATUS.DRAFT" type="text" @click="editGoal(row)">编辑</el-button>
            <template v-if="row.statusCode === YEAR_TARGET_STATUS.PUBLISH">
              <el-button v-if="$checkPermission(['MYDFX_MYDMB_QXFB'])" type="text" @click="unpublish(row)"><span class="danger">取消发布</span></el-button>
              <el-button v-if="$checkPermission(['MYDFX_MYDMB_ZF'])" type="text" @click="voidClick(row)"><span class="danger">作废</span></el-button>
            </template>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        style="margin-top: 20px;"
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        @pagination="getList1('page')"
      />
    </div>
    <el-dialog :visible.sync="dialogVisible" custom-class="flex-body" title="新增" width="450px" @close="$refs.formInline.resetFields()">
      <el-form ref="formInline" :model="formInline" class="demo-form-inline" label-width="80px">
        <el-form-item ref="goalName" label="满意度目标" prop="goalName" :rules="[{required: true, message: '请输入', trigger: 'change' }]">
          <el-input v-model="formInline.goalName" placeholder="请输入" clearable maxlength="20" />
        </el-form-item>
        <el-form-item ref="budgetYear" label="预算年份" prop="budgetYear" :rules="[{required: true, message: '请选择', trigger: 'change' }]">
          <el-date-picker
            v-model="formInline.budgetYear"
            type="year"
            style="width: 100%"
            value-format="yyyy"
            placeholder="选择年份"
          />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="submitHandler">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs/index.vue'
import { getYearGoalPageList, addYearGoal, cancelYearPush, invalidYearGoal } from '@/api/satisfaction/goal'
import { YEAR_TARGET_STATUS } from '@/enum'

export default {
  name: 'SatisfactionGoal',
  components: { CommonTabs },
  mixins: [getList1],
  data() {
    return {
      listApi1: getYearGoalPageList,
      YEAR_TARGET_STATUS,
      searchForm1: {
        goalName: null,
        budgetYear: null,
        statusCode: YEAR_TARGET_STATUS.DRAFT
      },
      dialogForm: {
        dialogTableData: [],
        dictCode: null,
        dialogTitle: null
      },
      dialogVisible: false,
      tabList: [
        {
          label: '草稿',
          value: YEAR_TARGET_STATUS.DRAFT,
          count: 0,
          key: 'state2300021'
        },
        {
          label: '已发布',
          value: YEAR_TARGET_STATUS.PUBLISH,
          count: 0,
          key: 'state2300022'
        },
        {
          label: '已作废',
          value: YEAR_TARGET_STATUS.DUTY,
          count: 0,
          key: 'state2300023'
        }
      ],
      formInline: {
        budgetYear: '',
        goalName: null
      }
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    tableCallBack1() {
      this.tabList.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    // 作废
    voidClick(row) {
      this.$confirm(`是否确定作废满意度目标：${row.goalName}`, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            invalidYearGoal({ id: row.configId }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    // 取消发布
    unpublish(row) {
      this.$confirm(`是否确定取消发布满意度目标：${row.goalName}`, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            cancelYearPush({ id: row.configId }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    editGoal(row, isDetail = false) {
      if (isDetail) {
        this.$router.push('/satisfaction/goal/detail?id=' + row.configId + '&year=' + row.budgetYear + '&name=' + row.goalName + '&processStatusCode=' + row.processStatusCode)
        return
      }
      this.$router.push('/satisfaction/goal/edit?id=' + row.configId + '&year=' + row.budgetYear + '&name=' + row.goalName + '&processStatusCode=' + row.processStatusCode)
    },
    // 新增弹框
    addGoal() {
      this.dialogVisible = true
    },
    // 选择年份
    submitHandler() {
      this.$refs.formInline.validate((valid) => {
        if (valid) {
          const load = this.$load()
          addYearGoal(this.formInline).then(res => {
            this.$message.success(res.msg)
            this.dialogVisible = false
            this.getList1()
          }).finally(() => load.close())
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .u-dialog__tip {
    flex: 1;
    line-height: 32px;
    background: #FEF0F0;
    border-radius: 4px;
    color: $--color-danger;
    margin-right: 20px;
    padding-left: 16px;
    position: relative;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #F8716B;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
}
</style>
