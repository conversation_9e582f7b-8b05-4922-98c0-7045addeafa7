<!--行业对标-->
<template>
  <div class="g-container">
    <el-card class="card-pd0">
      <el-form ref="searchForm" :model="searchForm" label-width="80px" label-suffix="：">
        <el-row :gutter="30">
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="areaCompanyId" label="区域">
              <UmBusCommon v-model="projDto" value-key="areaCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="cityCompanyId" label="城市">
              <UmBusCommon v-model="projDto" value-key="cityCompanyId" :area-company-id="projDto.areaCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="8" :md="8" :xl="6">
            <el-form-item prop="projectId" label="项目">
              <UmBusCommon v-model="projDto" value-key="projectId" :area-company-id="projDto.areaCompanyId" :city-company-id="projDto.cityCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="userName" label="操作人">
              <el-input v-model="searchForm.userName" placeholder="请输入" clearable />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6">
            <el-form-item prop="year" label="操作时间">
              <el-date-picker
                v-model="datetime"
                type="datetimerange"
                start-placeholder="开始时间"
                end-placeholder="结束时间"
                value-format="yyyy-MM-dd HH:mm:ss"
                format="yyyy-MM-dd HH:mm:ss"
                range-separator="至"
                style="width: 100%"
              />
            </el-form-item>
          </el-col>
          <el-col :span="12" :md="8" :xl="6" class="fr text-right">
            <el-button type="primary" icon="el-icon-search" @click="getList">搜索</el-button>
          </el-col>
        </el-row>
      </el-form>
    </el-card>
    <div class="g-table_full margin-t-20">
      <um-table-full
        :data="tableData"
        scroll
      >
        <el-table-column
          label="操作时间"
          width="180"
          prop="createTime"
        />
        <el-table-column
          prop="createUser"
          label="操作人"
          width="140"
        />
        <el-table-column
          prop="areaCompanyName"
          label="区域"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="cityCompanyName"
          label="城市"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="projectName"
          label="项目"
          :formatter="$formatterTable"
        />
        <el-table-column
          prop="optContent"
          label="操作内容"
        />
      </um-table-full>
      <pagination
        style="margin-top: 20px;"
        :total="tableTotal"
        :page.sync="searchForm.page.pageNum"
        :limit.sync="searchForm.page.pageSize"
        background
        @pagination="getList('page')"
      />
    </div>
  </div>
</template>

<script>
import getList from '@/mixins/getList'
import { getGoalConfigLogPageList } from '@/api/satisfaction/goal'
export default {
  name: 'SatisfactionGoalLog',
  mixins: [getList],
  data() {
    return {
      tableTotal: 0,
      listApi: getGoalConfigLogPageList,
      datetime: null,
      searchForm: {
        configId: null,
        optBeginTime: null,
        optEndTime: null,
        userName: null,
        abilityTypeCode: 2000071 // 2000071 地产 2000072 物业
      },
      projDto: {}
    }
  },
  activated() {
  },
  created() {
    this.searchForm.configId = this.$route.query.id
    this.getList()
  },
  methods: {
    beforeApiCallBack() {
      if (this.datetime) {
        [this.searchForm.optBeginTime, this.searchForm.optEndTime] = this.datetime
      } else {
        [this.searchForm.optBeginTime, this.searchForm.optEndTime] = [null, null]
      }
      return {
        ...this.searchForm,
        ...this.projDto
      }
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
  .u-dialog__tip {
    flex: 1;
    line-height: 32px;
    background: #FEF0F0;
    border-radius: 4px;
    color: $--color-danger;
    margin-right: 20px;
    padding-left: 16px;
    position: relative;
    &::before {
      content: '';
      width: 4px;
      height: 16px;
      background: #F8716B;
      position: absolute;
      left: 0;
      top: 8px;
    }
  }
}
</style>
