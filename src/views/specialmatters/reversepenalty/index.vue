<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="处置编号：">
          <el-input v-model="searchForm1.applyCode" placeholder="请输入处置编号" clearable />
        </el-form-item>
        <el-form-item label="区域/城市：">
          <UmBusOrgan v-model="searchForm1.projDto" is-auth placeholder="请选择区域城市" />
        </el-form-item>
        <el-form-item label="发起人：">
          <el-input v-model="searchForm1.applyUserName" placeholder="请输入发起人姓名" clearable />
        </el-form-item>
      </template>
      <template #suffix>
        <div style="margin-bottom: 20px;">
          <el-button
            v-if="$checkPermission(['TSSX_FXCF_XZ'])"
            type="primary"
            icon="el-icon-circle-plus-outline"
            class="margin-r-10"
            @click="$router.push('/specialmatters/reversepenalty/add')"
          >
            处罚信息登记
          </el-button>
          <el-button :loading="tableLoading1" type="primary" icon="el-icon-search" @click="getList1">搜索</el-button>
        </div>
      </template>
    </UmSearchLayout>
    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row type="flex" justify="space-between" class="mb-20">
        <CommonTabs :tabs="tabs" @changeTab="changeTab" />
      </el-row>
      <um-table-full :data="tableData1" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column prop="applyCode" label="处置编号" min-width="160px" />
        <el-table-column label="区域/城市" width="200px">
          <template slot-scope="{row}">
            {{ row.areaCompanyName }}-{{ row.cityCompanyName }}
          </template>
        </el-table-column>
        <el-table-column label="项目/分期" width="200px">
          <template slot-scope="{row}">
            {{ row.projectName }}{{ row.stageName ? '-' + row.stageName : '' }}
          </template>
        </el-table-column>
        <el-table-column prop="applyTypeName" label="处置原因" width="160px" />
        <el-table-column prop="remark" label="呈文摘要" width="220px">
          <template slot-scope="{ row }">
            <span class="row_3" :title="row.remark">{{ row.remark }}</span>
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="发起人" width="160px" />
        <el-table-column prop="createTime" label="创建时间" width="200px" />
        <el-table-column width="160px" label="操作" fixed="right">
          <template slot-scope="{ row }">
            <el-button
              v-if="$checkPermission(['TSSX_FXCF_BJ']) && [OA_STATUS.DRAFT, OA_STATUS.RETURN].includes(row.statusCode)"
              type="text"
              @click="$router.push('/specialmatters/reversepenalty/edit?id=' + row.uuid)"
            >
              编辑
            </el-button>
            <el-button
              v-if="$checkPermission(['TSSX_FXCF_SC']) && row.statusCode === OA_STATUS.DRAFT"
              type="text"
              @click="del(row)"
            >
              <span class="danger">删除</span>
            </el-button>
            <template v-if="row.statusCode === OA_STATUS.PASS">
              <el-button
                v-if="$checkPermission(['TSSX_FXCF_KCPF']) && row.deductStatusCode === DEDUCT_CODE.WAIT"
                type="text"
                @click="deduct(row)"
              >
                <span class="danger">扣除评分</span>
              </el-button>
              <el-button
                v-else-if="$checkPermission(['TSSX_FXCF_QXKC']) && row.deductStatusCode === DEDUCT_CODE.DEDUCT_COMPLETE"
                type="text"
                @click="cancelDeduct(row)"
              >
                <span class="danger">取消扣除</span>
              </el-button>
              <span v-else style="color: #999;margin-right: 10px;">{{ DEDUCT_CODE_TEXT[row.deductStatusCode] }}</span>
            </template>
            <el-button
              v-if="$checkPermission(['TSSX_FXCF_XQ']) && row.statusCode !== OA_STATUS.DRAFT"
              type="text"
              @click="$router.push('/specialmatters/reversepenalty/detail?id=' + row.uuid)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList1('page')"
      />
    </div>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs'
import { pageList, deleteInspect, updateDeductStatus } from '@/api/specialmatters/reversepenalty'
import { OA_STATUS } from '@/enum'
import UmBusOrgan from '@/components/UmBusOrgan'
const DEDUCT_CODE = {
  WAIT: 2200291, // 待操作
  WAIT_DEDUCT: 2200292, // 扣除待生效
  DEDUCT_COMPLETE: 2200293, // 扣除完成
  CANCEL_DEDUCT: 2200294, // 取消扣除待生效
  CANCEL_COMPLETE: 2200295 // 取消扣除成功
}
const DEDUCT_CODE_TEXT = {
  [DEDUCT_CODE.WAIT]: '待操作',
  [DEDUCT_CODE.WAIT_DEDUCT]: '扣除待生效',
  [DEDUCT_CODE.DEDUCT_COMPLETE]: '扣除完成',
  [DEDUCT_CODE.CANCEL_DEDUCT]: '取消扣除待生效',
  [DEDUCT_CODE.CANCEL_COMPLETE]: '取消扣除成功'
}
// 特殊事项申请
export default {
  name: 'SpecialmattersPenalty',
  components: { CommonTabs, UmBusOrgan },
  mixins: [getList1],
  props: {},
  data() {
    return {
      listApi1: pageList,
      OA_STATUS,
      DEDUCT_CODE,
      DEDUCT_CODE_TEXT,
      searchForm1: {
        statusCode: OA_STATUS.DRAFT,
        applyUserName: null,
        applyCode: null,
        projDto: {}
      },
      tabs: [
        {
          label: '草稿',
          value: OA_STATUS.DRAFT,
          count: 0,
          key: 'draft'
        },
        {
          label: '审批中',
          value: OA_STATUS.APPROVAL,
          count: 0,
          key: 'review'
        },
        {
          label: '审批通过',
          value: OA_STATUS.PASS,
          count: 0,
          key: 'pass'
        },
        {
          label: '审批不通过',
          value: OA_STATUS.NO_PASS,
          count: 0,
          key: 'noPass'
        },
        {
          label: '驳回',
          value: OA_STATUS.RETURN,
          count: 0,
          key: 'return'
        }
      ]
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    // tab上的统计数据
    tableCallBack1() {
      this.tabs.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    del(data) {
      this.$confirm('是否确定删除当前申请编号：' + data.applyCode, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            deleteInspect({ uuid: data.uuid }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    // 扣除评分
    deduct(data) {
      this.$confirm('是否确定扣除当前申请编号：' + data.applyCode, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            updateDeductStatus({ uuid: data.uuid, deductFlag: 1 }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    },
    // 取消扣除
    cancelDeduct(data) {
      this.$confirm('是否确定取消扣除当前申请编号：' + data.applyCode, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            updateDeductStatus({ uuid: data.uuid, deductFlag: 0 }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

