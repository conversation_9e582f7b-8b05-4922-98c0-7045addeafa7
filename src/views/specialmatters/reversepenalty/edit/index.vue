<template>
  <div class="m_apply_add">
    <div class="f-flex-acjcsb f-flex-ac">
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">稽查处置</div>
      </div>
      <div v-if="flowStatus !== 6">
        <el-button type="primary" plain class="margin-r-10" @click="save(0)">保存为草稿</el-button>
        <el-button type="primary" @click="save(1)">发起审批</el-button>
      </div>
      <div v-else>
        <el-button type="primary" plain class="margin-r-10" @click="save(0)">保存</el-button>
      </div>
    </div>
    <div class="g-plan-form">
      <el-form
        ref="addForm"
        :model="addForm"
        label-width="78px"
        label-position="left"
      >
        <el-row :gutter="30">
          <el-col :span="12">
            <el-form-item label="区域/城市：" prop="projDto.areaCompanyId" :rules="[{ required: true, message: '请选择' }]">
              <UmBusOrgan v-model="addForm.projDto" is-auth :check-strictly="false" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="项目/分期：" prop="projDto.projectId" :rules="[{ required: true, message: '请选择' }]">
              <UmBusProject v-model="addForm.projDto" :area-company-id="addForm.projDto.areaCompanyId" :city-company-id="addForm.projDto.cityCompanyId" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="处置原因：" prop="applyTypeId" :rules="[{ required: true, message: '请选择' }]">
              <el-select v-model="addForm.applyTypeId" filterable clearable style="width: 100%">
                <el-option
                  v-for="item in reasonList"
                  :key="item.cfgDictId"
                  :label="item.dictName"
                  :value="item.cfgDictId"
                />
              </el-select>
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="触点名称：" prop="pointId" :rules="[{ required: true, message: '请选择', trigger: ['change'] }]">
              <UmBusScene v-model="addForm.pointId" placeholder="请选择" />
            </el-form-item>
          </el-col>
          <el-col :span="12">
            <el-form-item label="扣除月份：" prop="deductMonth" :rules="[{ required: true, type: 'array', message: '请选择' }]">
              <el-date-picker
                v-model="addForm.deductMonth"
                style="width: 100%"
                type="months"
                value-format="yyyy-MM"
                clearable
                placeholder="请选择（多选）"
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="呈文摘要：" prop="remark" :rules="[{ required: true, message: '请选择' }]">
              <el-input
                v-model="addForm.remark"
                placeholder="请输入"
                type="textarea"
                :rows="8"
                maxlength="2000"
                show-word-limit
              />
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件：" prop="fileUrls" :rules="[{ required: true, type: 'array', message: '请选择' }]">
              <um-upload-file v-model="addForm.fileUrls" style="width: 388px" :size="204800" :limit="5" tip="请上传与处置相关的文件资料" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import UmBusOrgan from '@/components/UmBusOrgan'
import { getDictList } from '@/api/common'
import { DICT_CODE } from '@/enum'
import { addApply, updateApply, getDetail } from '@/api/specialmatters/reversepenalty'
import { getFileName } from '@/utils'
export default {
  name: 'SpecialmattersPenaltyEdit',
  components: { UmBusOrgan },
  props: {},
  data() {
    return {
      reasonList: [],
      // 调研计划
      addForm: {
        applyTypeId: null,
        remark: null,
        pointId: null,
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null,
          projectId: null,
          stageId: null
        },
        deductMonth: [],
        fileUrls: [],
        /**
         * 1、前端根据后台返回的审批状态，如果是驳回情况，并且oa状态为6，隐藏存草稿和发起审批按钮。只允许编辑并保存。状态不发生改变
         * 2、前端根据后台返回审批状态，如果是驳回情况，并且oa状态为3，则保留原样。允许存草稿和发起审批
         * 3、后端根据oa状态，如果返回6的时候，后续该审批通过，需要把状态更新为审批通过
        */
        flowStatus: null, // 流程状态
        oaFlag: 0, // 是否发起OA审批
        uuid: null
      }
    }
  },
  async created() {
    try {
      await this.getReasonList()
      const { id } = this.$route.query
      if (id) {
        this.addForm.uuid = id
        this.getDetail()
      }
    } catch (error) {

    }
  },
  mounted() { },
  methods: {
    // 获取处置原因数据字典
    getReasonList() {
      const load = this.$load()
      return getDictList({ dictCode: DICT_CODE.REVERSE_APPLY_REASON }).then(res => {
        this.reasonList = res.data || []
      }).finally(() => load.close())
    },
    // 获取详情
    getDetail() {
      const load = this.$load()
      getDetail({ uuid: this.addForm.uuid }).then(res => {
        const data = res.data || {}
        this.flowStatus = data.flowStatus
        this.addForm.projDto = {
          areaCompanyId: data.areaCompanyId,
          cityCompanyId: data.cityCompanyId
        }
        this.addForm.applyTypeId = data.applyTypeId || null
        this.addForm.pointId = data.pointId ? data.pointId.toString() : null
        this.addForm.deductMonth = data.deductMonth || []
        this.addForm.remark = data.remark
        this.addForm.fileUrls = data.fileUrls ? data.fileUrls.map(item => {
          return {
            url: item,
            name: getFileName(item)
          }
        }) : []
        // 这里这么处理因为组件内部实现了监听区域城市的数据，会清空projectId和stageId，所以在下一次循环中赋值
        this.$nextTick(() => {
          this.addForm.projDto.projectId = data.projectId
          this.addForm.projDto.stageId = data.stageId
        })
      }).finally(() => load.close())
    },
    save(type) {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        this.addForm.oaFlag = type
        const API = this.addForm.uuid ? updateApply : addApply
        const load = this.$load()
        API({ ...this.addForm, fileUrls: this.addForm.fileUrls.map(item => item.filePath || item.url) }).then(res => {
          this.$alert('操作成功', '提示', {
            type: 'success',
            showClose: false
          }).then(() => {
            this.$router.back()
          })
        }).finally(() => load.close())
      })
    },
    // 添加申请范围
    add() {
      this.showDialog = true
    }
  }
}
</script>

<style scoped lang="scss">
.m_apply_add{
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  padding: 20px;
  box-sizing: border-box;
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #31415F;
    }
  }
  .g-plan-form{
    padding: 0 8px;
    margin-top: 24px;
  }
}

</style>
