<template>
  <div class="m_apply_add">
    <div class="f-flex-acjcsb f-flex-ac">
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">稽查处置</div>
      </div>
    </div>
    <div class="g-plan-form">
      <el-form
        label-width="76px"
        class="detailForm"
      >
        <el-row :gutter="30">
          <el-col :span="12" :xs="24">
            <el-form-item label="区域/城市：">
              {{ addForm.areaCompanyName }}-{{ addForm.cityCompanyName }}
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="项目/分期：">
              {{ addForm.projectName }}{{ addForm.stageName ? '-' + addForm.stageName : '' }}
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="处置原因：">
              {{ addForm.applyTypeName }}
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="触点名称：">
              {{ addForm.pointName }}
            </el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="扣除月份：">
              {{ addForm.deductMonth.join('、') }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="呈文摘要：" prop="remark">
              {{ addForm.remark }}
            </el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="附件：" prop="fileUrls">
              <um-upload-file v-model="addForm.fileUrls" disabled style="width: 388px" :size="102400" :limit="5" />
            </el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </div>
  </div>
</template>

<script>
import { getDetail } from '@/api/specialmatters/reversepenalty'
import { getReverseDetailByApplyCode } from '@/api/oa'
import { getFileName } from '@/utils'
export default {
  name: 'SpecialmattersPenaltyDetail',
  props: {
    isOa: { // 是否是oa查看详情
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      reasonList: [],
      // 调研计划
      addForm: {
        deductMonth: [],
        fileUrls: [],
        uuid: null
      }
    }
  },
  async created() {
    const { id, code } = this.$route.query
    if (id || code) {
      this.getDetail(this.isOa ? code : id)
    }
  },
  mounted() { },
  methods: {
    // 获取详情
    getDetail(uuid) {
      const load = this.$load()
      const api = this.isOa ? getReverseDetailByApplyCode : getDetail
      api(this.isOa ? { applyCode: uuid } : { uuid }).then(res => {
        const data = res.data || {}
        this.addForm = { ...data }
        this.addForm.deductMonth = data.deductMonth || []
        this.addForm.fileUrls = data.fileUrls ? data.fileUrls.map(item => {
          return {
            url: item,
            name: getFileName(item)
          }
        }) : []
      }).finally(() => load.close())
    }
  }
}
</script>

<style scoped lang="scss">
.m_apply_add{
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  padding: 20px;
  box-sizing: border-box;
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #31415F;
    }
  }
  .g-plan-form{
    padding: 0 8px;
    margin-top: 24px;
  }
}

</style>
<style lang="scss">
@import url(../../mobile.scss);
</style>
