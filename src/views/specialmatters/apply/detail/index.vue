<template>
  <div class="m_apply_add">
    <div class="f-flex-acjcsb f-flex-ac">
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">特殊报备事项</div>
      </div>
    </div>
    <el-form
      class="g-plan-form detailForm"
      label-width="76px"
    >
      <el-row :gutter="30">
        <el-col
          :span="12"
          :xs="24"
        >
          <el-form-item label="区域/城市：" prop="projDto.cityCompanyId">
            {{ detail.areaCompanyName || '--' }} - {{ detail.cityCompanyName || '--' }}
          </el-form-item>
        </el-col>
        <el-col
          :span="12"
          :xs="24"
        >
          <el-form-item ref="quesTemplateId" label="申请编号：" prop="quesTemplateId">
            {{ detail.applyCode }}
          </el-form-item>
        </el-col>
        <el-col
          :span="24"
        >
          <el-form-item ref="remark" label="备注：" prop="remark">
            {{ detail.remark }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件：" prop="fileUrls">
            <um-upload-file v-model="detail.fileUrls" disabled style="width: 388px" :size="102400" :limit="5" />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="f-flex-acjcsb f-flex-ac margin-t-20 margin-b-20">
        <div class="g-plan-title-icon">
          <div class="u-txt-icon" />
          <div class="u-txt-title">申请范围</div>
        </div>
      </div>
      <um-table-full :data="tableData" scroll>
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="projectName" label="项目" min-width="200">
          <template slot-scope="{ row }">{{ row.projDto.projectName }} {{ row.projDto.stageName ? ' - ' + row.projDto.stageName : '' }}</template>
        </el-table-column>
        <el-table-column prop="pointNames" label="触点名称" min-width="360">
          <template slot-scope="{row}">{{ row.pointNames.join('、') }}</template>
        </el-table-column>
        <el-table-column prop="answer" label="申请类型" width="150px">
          <template slot-scope="{ row }">{{ specialApplyTypeText[row.applyTypeCode] }}</template>
        </el-table-column>
        <el-table-column prop="houseNum" label="户数" width="100px" />
        <el-table-column prop="applyReasonName" label="申请原因" width="160px" />
        <el-table-column label="操作" width="80px">
          <template slot-scope="scope">
            <el-button type="text" @click="openDialog(scope.row)">详情</el-button>
          </template>
        </el-table-column>
      </um-table-full>
    </el-form>
    <el-dialog :visible.sync="detailVisible" title="详情" width="800px">
      <el-form label-position="top">
        <el-row>
          <el-col :span="12" :xs="24">
            <el-form-item label="所属项目：">{{ detailInfo.projectName || '--' }} {{ detailInfo.stageName ? ' - ' + detailInfo.stageName : '' }}</el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="申请原因：">{{ detailInfo.applyReasonName | formatterTable }}</el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item label="申请类型：">{{ detailInfo.applyTypeName | formatterTable }}</el-form-item>
          </el-col>
          <el-col :span="12" :xs="24">
            <el-form-item :label="`${specialApplyTypeText[detailInfo.applyTypeCode]}：`">
              {{ (detailInfo.unSurveyStartMonth || '--') + ' ~ ' + (detailInfo.unSurveyEndMonth || '--') }}
            </el-form-item>
          </el-col>
          <el-col v-if="detailInfo.applyTypeCode === SPECIAL_APPLY_TYPE.DELAY" :span="12" :xs="24">
            <el-form-item label="延期至：">
              {{ detailInfo.delaySurveyMonth | formatterTable }}
            </el-form-item>
          </el-col>
          <el-col :span="24" :xs="24">
            <el-form-item label="楼栋：">{{ detailInfo.applyScopeCode === SPECIAL_APPLY_RANGE.ALL ? '全项目' : roomInfo.join('、') }}</el-form-item>
          </el-col>
          <el-col :span="24">
            <el-form-item label="触点/节点：">{{ detailInfo.pointNames ? detailInfo.pointNames.join('、') : '--' }}</el-form-item>
          </el-col>
        </el-row>
      </el-form>
    </el-dialog>
  </div>
</template>

<script>
import { getDetail } from '@/api/specialmatters/apply'
import { getSpecialDetailByApplyCode } from '@/api/oa'
import { SPECIAL_APPLY_TYPE, SPECIAL_APPLY_RANGE, HOUSE_TYPE } from '@/enum'
import { getRoomTree } from '@/api/common'
import { getFileName } from '@/utils'
const specialApplyTypeText = {
  [SPECIAL_APPLY_TYPE.NO_SURVEY]: '不调研',
  [SPECIAL_APPLY_TYPE.ONLY_WATCH]: '仅监控，不考核',
  [SPECIAL_APPLY_TYPE.DELAY]: '延期调研'
}
export default {
  name: 'SpecialmattersApplyDetail',
  props: {
    isOa: { // 是否是oa查看详情
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      specialApplyTypeText,
      SPECIAL_APPLY_TYPE,
      SPECIAL_APPLY_RANGE,
      detail: {
        fileUrls: []
      },
      detailVisible: false,
      tableData: [],
      detailInfo: {}, // 查看申请详情
      roomInfo: []
    }
  },
  created() {
    const { id, code } = this.$route.query
    if (id || code) {
      this.getDetail(this.isOa ? code : id)
    }
  },
  mounted() {},
  methods: {
    // 获取详情
    getDetail(uuid) {
      const load = this.$load()
      const api = this.isOa ? getSpecialDetailByApplyCode : getDetail
      api(this.isOa ? { applyCode: uuid } : { uuid }).then(res => {
        const detail = res.data
        this.detail = {
          ...detail,
          fileUrls: detail.fileUrls ? detail.fileUrls.map(item => {
            return {
              url: item,
              name: getFileName(item)
            }
          }) : []
        }
        this.tableData = detail.items.map(item => ({
          projDto: {
            projectId: item.projectId,
            stageId: item.stageId,
            projectName: item.projectName,
            stageName: item.stageName
          },
          ...item
        }))
      }).finally(() => load.close())
    },
    async openDialog(data) {
      const load = this.$load()
      try {
        await this.getRoomTree(data)
        this.detailInfo = data
        this.detailVisible = true
      } catch (error) {

      } finally {
        load.close()
      }
    },
    getRoomTree({ projectId, stageId, scopes }) {
      return getRoomTree({ projectId, stageId }).then(res => {
        const arr = res.data
        this.formatData(arr, scopes)
      })
    },
    formatData(arr, scopes, _parent) {
      arr.forEach(item => {
        _parent && (item._parent = _parent)
        scopes.forEach(_item => {
          switch (_item.houseScopeCode) {
            case HOUSE_TYPE.BUILDING:
              if (_item.buildingId === item.code) {
                this.roomInfo.push(item.name)
              }
              break
            case HOUSE_TYPE.UNIT:
              if (_item.unitId === item.code) {
                this.roomInfo.push(`${item._parent.name}-${item.name}`)
              }
              break
            case HOUSE_TYPE.ROOM:
              if (_item.houseId === item.code) {
                this.roomInfo.push(`${item._parent._parent.name}-${item._parent.name}-${item.name}`)
              }
              break
          }
        })
        if (Array.isArray(item.children) && item.children.length) {
          this.formatData(item.children, scopes, item)
        }
      })
    }
  }

}
</script>

<style scoped lang="scss">

.m_apply_add{
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  padding: 20px;
  box-sizing: border-box;
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #31415F;
    }
  }
  .g-plan-form{
    padding: 0 8px;
    margin-top: 24px;
  }
}
</style>
<style lang="scss">
@import url(../../mobile.scss);
</style>
