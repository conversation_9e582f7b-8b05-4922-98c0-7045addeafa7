<template>
  <el-dialog
    title="申请明细"
    z-index="1999"
    :visible.sync="innerVisible"
    width="800px"
    append-to-body
    :close-on-click-modal="false"
    destroy-on-close
    @close="dialogClose"
  >
    <el-form ref="addForm" :model="addForm" label-width="70px" label-position="top">
      <el-row :gutter="30">
        <el-col :span="12">
          <el-form-item label="所属项目/分期" prop="projDto" :rules="[{ required: true, message: '请选择', trigger: ['change']}]">
            <UmBusProject
              ref="UmBusProject"
              v-model="addForm.projDto"
              :check-strictly="false"
              :choosed-info="choosedProjDtos"
              :city-company-id="cityCompanyId"
              :area-company-id="areaCompanyId"
            />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请原因：" prop="applyReasonId" :rules="[{ required: true, message: '请选择', trigger: ['change']}]">
            <el-select v-model="addForm.applyReasonId" filterable clearable style="width: 100%" @change="reasonChange">
              <el-option
                v-for="item in reasonList"
                :key="item.cfgDictId"
                :label="item.dictName"
                :value="item.cfgDictId"
              />
            </el-select>
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item label="申请类型：" prop="applyTypeCode" :rules="[{ required: true, message: '请选择', trigger: ['change']}]">
            <el-radio-group
              v-model="addForm.applyTypeCode"
              @change="dateRange = null;addForm.delaySurveyMonth = null;$nextTick(() => {
                $refs.addForm.clearValidate('unSurveyStartMonth')
                $refs.addForm.clearValidate('delaySurveyMonth')
              })"
            >
              <el-radio :label="SPECIAL_APPLY_TYPE.NO_SURVEY">不调研</el-radio>
              <el-radio :label="SPECIAL_APPLY_TYPE.ONLY_WATCH">仅监控，不考核</el-radio>
              <el-radio :label="SPECIAL_APPLY_TYPE.DELAY">延期调研</el-radio>
            </el-radio-group>
          </el-form-item>
        </el-col>
        <el-col v-show="addForm.applyTypeCode !== SPECIAL_APPLY_TYPE.DELAY" :span="12">
          <el-form-item key="unSurveyStartMonth" :label="addForm.applyTypeCode === SPECIAL_APPLY_TYPE.NO_SURVEY ? '不调研周期：' : '监控周期：'" prop="unSurveyStartMonth" :rules="[{ required: true, message: '请选择', trigger: ['change']}]">
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              :disabled="!addForm.applyTypeCode"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              style="width: 100%"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="addForm.applyTypeCode === SPECIAL_APPLY_TYPE.DELAY" :span="12">
          <el-form-item prop="unSurveyStartMonth" label="申请范围：" :rules="[{ required: true, message: '请选择'}]">
            <el-date-picker
              v-model="dateRange"
              type="monthrange"
              :disabled="!addForm.applyTypeCode"
              range-separator="至"
              start-placeholder="开始月份"
              end-placeholder="结束月份"
              value-format="yyyy-MM"
              style="width: 100%"
              :picker-options="{
                disabledDate: (date) => {
                  const delayMonth = new Date(addForm.delaySurveyMonth)
                  return addForm.delaySurveyMonth && new Date(delayMonth.getFullYear(), delayMonth.getMonth()) <= date
                },
              }"
            />
          </el-form-item>
        </el-col>
        <el-col v-show="addForm.applyTypeCode === SPECIAL_APPLY_TYPE.DELAY" :span="12">
          <el-form-item key="delaySurveyMonth" ref="delaySurveyMonth" label="延期至：" prop="delaySurveyMonth" :rules="[{ required: addForm.applyTypeCode === SPECIAL_APPLY_TYPE.DELAY, message: '请选择', trigger: ['blur']}]">
            <el-date-picker
              v-model="addForm.delaySurveyMonth"
              type="month"
              :disabled="!addForm.applyTypeCode"
              placeholder="请选择"
              value-format="yyyy-MM"
              style="width: 100%"
              :picker-options="{
                disabledDate: (date) => {
                  const endMonth = new Date(addForm.unSurveyEndMonth)
                  return new Date(endMonth.getFullYear(), endMonth.getMonth() + 1) > date
                }
              }"
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item key="applyScopeCode" label="楼栋" prop="applyScopeCode" :rules="[{ required: true, message: '请选择', trigger: ['change']}]">
            <div class="f-flex-ac">
              <el-radio-group v-model="addForm.applyScopeCode">
                <el-radio :label="SPECIAL_APPLY_RANGE.ALL">全项目</el-radio>
                <el-radio :label="SPECIAL_APPLY_RANGE.APPOINT">选择房间</el-radio>
              </el-radio-group>
              <div style="flex: 1;margin-left: 10px;">
                <UmBusRoom
                  v-show="addForm.applyScopeCode === SPECIAL_APPLY_RANGE.APPOINT"
                  ref="UmBusRoom"
                  v-model="addForm.scopes"
                  :project-id="addForm.projDto.projectId"
                  :stage-id="addForm.projDto.stageId"
                  multiple
                  :emit-path="false"
                  :init-cb="initCb"
                />
              </div>
            </div>
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="触点/节点" prop="pointIds" :rules="[{ required: true, type: 'array', message: '请选择', trigger: ['change']}]">
            <el-cascader
              ref="pointCasc"
              v-model="addForm.pointIds"
              placeholder="请选择"
              :options="pointOptions"
              collapse-tags
              :props="{ multiple: true, emitPath: false }"
              filterable
              style="width: 100%;"
            />
          </el-form-item>
        </el-col>
      </el-row>
    </el-form>
    <span slot="footer" class="dialog-footer">
      <el-button class="cancel-btn" @click="innerVisible = false">取 消</el-button>
      <el-button type="primary" @click="submitHandle">确 定</el-button>
    </span>
  </el-dialog>
</template>
<script>
import { SPECIAL_APPLY_RANGE, SPECIAL_APPLY_TYPE, DICT_CODE } from '@/enum'
import { getDictList } from '@/api/common'
import { cloneDeep } from 'lodash'
import getPointList from '@/mixins/getPointList'
import { getMaxRangeCheckedData, getCheckedNodes, formatHouse, computedCheckedNodes } from '../../../../plan/satisfactionSurvey/add/utils'
export default {
  name: 'EditDialog',
  mixins: [getPointList],
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    cityCompanyId: {
      type: [String, Number],
      default: null
    },
    areaCompanyId: {
      type: [String, Number],
      default: null
    },
    editData: {
      type: Object,
      default: null
    },
    choosedProjDtos: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      SPECIAL_APPLY_TYPE,
      SPECIAL_APPLY_RANGE,
      addForm: {
        applyTypeCode: null,
        applyScopeCode: null,
        applyReasonId: null,
        applyReasonName: null,
        unSurveyStartMonth: null,
        unSurveyEndMonth: null,
        delaySurveyMonth: null,
        houseNum: null,
        projDto: {
          projectId: null,
          stageId: null
        },
        pointIds: [],
        scopes: []
      },
      needSpecial: false,
      dateRange: null,
      option: [],
      editInit: false, // 编辑初始化，每次编辑用一次，用于回显房间, 防止每次请求房间接口都触发
      reasonList: [] // 申请原因字典数据
    }
  },
  computed: {
    innerVisible: {
      get() {
        return this.visible
      },
      set(val) {
        this.$emit('update:visible', val)
      }
    }
  },
  watch: {
    visible(v) {
      if (v) {
        !this.pointOptions.length && this.getPointOption()
        this.$nextTick(async() => {
          const load = this.$load()
          try {
            await this.$refs.UmBusProject.getTwoLevelProject(false)
            if (this.editData) { // 编辑的时候
              this.addForm = cloneDeep(this.editData)
              this.dateRange = this.addForm.unSurveyStartMonth ? [this.addForm.unSurveyStartMonth, this.addForm.unSurveyEndMonth] : []
            }
          } catch (error) {
          } finally {
            load.close()
          }
        })
      }
      if (v && !this.reasonList.length) {
        this.getReasonList()
      }
    },
    dateRange(v) {
      if (v) {
        this.addForm.unSurveyStartMonth = v[0]
        this.addForm.unSurveyEndMonth = v[1]
      } else {
        this.addForm.unSurveyStartMonth = null
        this.addForm.unSurveyEndMonth = null
      }
    }
  },
  methods: {
    // 编辑时将经过处理的房间id，再重新计算出来回显
    initCb(data) {
      if (this.addForm.applyScopeCode === SPECIAL_APPLY_RANGE.APPOINT && !this.editInit) { // 如果是指定房间
        const arr = []
        this.editData?.scopes.forEach(item => {
          if (item.buildingId && item.unitId && item.houseId) {
            arr.push(item.houseId)
          } else if (item.buildingId && item.unitId) {
            arr.push(item.unitId)
          } else if (item.buildingId) {
            arr.push(item.buildingId)
          }
        })
        this.addForm.scopes = computedCheckedNodes(Array.from(new Set(arr)), data)
        this.editInit = true
      }
    },
    dialogClose() {
      this.editInit = false
      this.dateRange = null
      this.$refs.addForm.resetFields()
      this.$nextTick(() => {
        this.$refs.addForm.clearValidate()
      })
    },
    submitHandle() {
      this.$refs.addForm.validate(valid => {
        if (!valid) { return }
        const arr = cloneDeep(this.$refs.UmBusRoom.getSelectNodes()) || []
        getMaxRangeCheckedData(arr)
        // 全项目统计所有，不然统计选中的房间
        this.addForm.houseNum = this.$refs.UmBusRoom.getRoomNums(this.addForm.applyScopeCode === SPECIAL_APPLY_RANGE.APPOINT)
        this.$emit('success', {
          ...cloneDeep(this.addForm),
          projectName: this.$refs.UmBusProject.$el.getElementsByTagName('input')[0].value,
          pointNames: this.$refs.pointCasc.getCheckedNodes(true).map(item => item.label),
          scopes: this.addForm.applyScopeCode === SPECIAL_APPLY_RANGE.APPOINT ? formatHouse(getCheckedNodes(arr)) : []
        })
        this.innerVisible = false
      })
    },
    reasonChange(data) {
      this.addForm.applyReasonName = this.reasonList.filter(item => item.cfgDictId === data)[0].dictName
    },
    // 获取申请原因数据字典
    getReasonList() {
      const load = this.$load()
      getDictList({ dictCode: DICT_CODE.SPECIAL_APPLY_REASON }).then(res => {
        this.reasonList = res.data || []
      }).finally(() => load.close())
    }
  }
}
</script>
