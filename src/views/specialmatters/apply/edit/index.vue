<template>
  <div class="m_apply_add">
    <div class="f-flex-acjcsb f-flex-ac">
      <div class="g-plan-title-icon">
        <div class="u-txt-icon" />
        <div class="u-txt-title">特殊报备事项</div>
      </div>
      <!-- <div v-if="flowStatus !== 6"> -->
      <div>
        <el-button type="primary" plain class="margin-r-10" @click="save(0)">保存为草稿</el-button>
        <el-button type="primary" @click="save(1)">发起审批</el-button>
      </div>
      <!-- </div> -->
      <!-- <div v-else> -->
      <!-- <el-button type="primary" plain class="margin-r-10" @click="save(0)">保存</el-button> -->
      <!-- </div> -->
    </div>
    <el-form
      ref="addForm"
      :model="addForm"
      class="g-plan-form"
      label-width="78px"
      label-position="left"
      @submit.native.prevent
    >
      <el-row :gutter="30">
        <el-col :span="12">
          <el-form-item label="区域/城市：" prop="projDto.areaCompanyId" :rules="[{ required: true, message: '请选择区域/城市' }]">
            <UmBusOrgan v-model="addForm.projDto" is-auth :disabled="!!addForm.items.length" placeholder="请选择区域/城市" :check-strictly="false" />
          </el-form-item>
        </el-col>
        <el-col :span="12">
          <el-form-item v-if="applyCode" ref="quesTemplateId" label="申请编号：" prop="quesTemplateId">
            {{ applyCode || '--' }}
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item ref="remark" label="备注：" prop="remark" :rules="[{ required: true, message: '请输入备注' }]">
            <el-input
              v-model="addForm.remark"
              placeholder="请输入"
              type="textarea"
              :rows="8"
              maxlength="2000"
              show-word-limit
            />
          </el-form-item>
        </el-col>
        <el-col :span="24">
          <el-form-item label="附件：" prop="fileUrls">
            <um-upload-file v-model="addForm.fileUrls" style="width: 388px" :size="204800" :limit="5" />
          </el-form-item>
        </el-col>
      </el-row>

      <div class="f-flex-acjcsb f-flex-ac margin-t-20 margin-b-20">
        <div class="g-plan-title-icon">
          <div class="u-txt-icon" />
          <div class="u-txt-title">申请范围</div>
        </div>
        <div>
          <el-button type="primary" @click="add">新增</el-button>
        </div>
      </div>
      <um-table-full :data="addForm.items" scroll>
        <el-table-column type="index" label="序号" width="80" />
        <el-table-column prop="projectName" label="项目" min-width="160">
          <template slot-scope="{ row }">{{ row.projectName }}</template>
        </el-table-column>
        <el-table-column prop="pointNames" label="触点名称" min-width="360">
          <template slot-scope="{row}">{{ row.pointNames ? row.pointNames.join('、') : '--' }}</template>
        </el-table-column>
        <el-table-column prop="answer" label="申请类型" width="160">
          <template slot-scope="{ row }">{{ specialApplyTypeText[row.applyTypeCode] }}</template>
        </el-table-column>
        <el-table-column prop="houseNum" label="户数" width="120px" />
        <el-table-column prop="applyReasonName" label="申请原因" width="140px" />
        <el-table-column label="操作" fixed="right" width="120">
          <template slot-scope="scope">
            <el-button
              type="text"
              @click="editApply(scope.row, scope.$index)"
            >
              编辑
            </el-button>
            <el-button
              type="text"
              @click="delApply(scope.$index)"
            >
              <span class="danger">删除</span>
            </el-button>
          </template>
        </el-table-column>
      </um-table-full>
    </el-form>
    <EditDialog
      :visible.sync="showDialog"
      :edit-data="editData"
      :city-company-id="addForm.projDto.cityCompanyId"
      :area-company-id="addForm.projDto.areaCompanyId"
      :choosed-proj-dtos="choosedProjDtos"
      @success="addSuccess"
    />
  </div>
</template>

<script>
import { addApply, updateApply, getDetail } from '@/api/specialmatters/apply'
import EditDialog from './components/EditDialog'
import { SPECIAL_APPLY_TYPE } from '@/enum'
import { getFileName } from '@/utils'
const specialApplyTypeText = {
  [SPECIAL_APPLY_TYPE.NO_SURVEY]: '不调研',
  [SPECIAL_APPLY_TYPE.ONLY_WATCH]: '仅监控，不考核',
  [SPECIAL_APPLY_TYPE.DELAY]: '延期调研'
}
export default {
  name: 'SpecialmattersApplyEdit',
  components: { EditDialog },
  props: {},
  data() {
    return {
      specialApplyTypeText,
      // 调研计划
      addForm: {
        uuid: null,
        oaFlag: 0, // 是否发起OA审批 1-发起 0 不发器
        fileUrls: [],
        projDto: {
          areaCompanyId: null,
          cityCompanyId: null
        },
        remark: null,
        items: []
      },
      /**
       * 1、前端根据后台返回的审批状态，如果是驳回情况，并且oa状态为6，隐藏存草稿和发起审批按钮。只允许编辑并保存。状态不发生改变
       * 2、前端根据后台返回审批状态，如果是驳回情况，并且oa状态为3，则保留原样。允许存草稿和发起审批
       * 3、后端根据oa状态，如果返回6的时候，后续该审批通过，需要把状态更新为审批通过
      */
      flowStatus: null, // 流程状态
      applyCode: null, // 申请编号
      showDialog: false,
      editIndex: null,
      editData: null // 弹窗编辑数据
    }
  },
  computed: {
    choosedProjDtos() { // 计算所有已经选中的项目分期数据
      const set = new Set()
      let arr = this.addForm.items
      if (this.editIndex !== null) { // 如果是编辑，则当前编辑行的不去除
        arr = arr.slice(0, this.editIndex).concat(arr.slice(this.editIndex + 1))
      }
      arr.forEach(item => {
        item.projDto.projectId && set.add(item.projDto.projectId)
        item.projDto.stageId && set.add(item.projDto.stageId)
      })
      return Array.from(set)
    }
  },
  created() {
    const { id } = this.$route.query
    if (id) {
      this.addForm.uuid = id
      this.getDetail()
    }
  },
  mounted() {},
  methods: {
    // 获取详情
    getDetail() {
      const load = this.$load()
      getDetail({ uuid: this.addForm.uuid }).then(res => {
        const detail = res.data
        this.flowStatus = detail.flowStatus
        this.applyCode = detail.applyCode
        this.addForm = {
          uuid: detail.uuid,
          projDto: {
            areaCompanyId: detail.areaCompanyId,
            cityCompanyId: detail.cityCompanyId
          },
          remark: detail.remark,
          fileUrls: detail.fileUrls ? detail.fileUrls.map(item => {
            return {
              url: item,
              name: getFileName(item)
            }
          }) : [],
          items: detail.items.map(item => ({
            projDto: {
              projectId: item.projectId,
              stageId: item.stageId,
              projectName: item.projectName
            },
            ...item,
            projectName: item.projectName + (item.stageName ? '-' + item.stageName : item.stageName)
          }))
        }
      }).finally(() => load.close())
    },
    // 修改申请范围
    editApply(data, index) {
      this.editIndex = index
      this.editData = data
      this.showDialog = true
    },
    // 删除修改范围
    delApply(index) {
      this.addForm.items.splice(index, 1)
    },
    // 保存草稿/发起oA
    save(type) {
      this.$refs.addForm.validate(valid => {
        if (!valid) return
        if (!this.addForm.items.length) {
          this.$message.warning('请添加申请范围！')
          return
        }
        this.addForm.oaFlag = type
        const API = this.addForm.uuid ? updateApply : addApply
        const load = this.$load()
        API({ ...this.addForm, fileUrls: this.addForm.fileUrls.map(item => item.filePath || item.url) }).then(res => {
          this.$alert('操作成功', '提示', {
            type: 'success',
            showClose: false
          }).then(() => {
            this.$router.back()
          })
        }).finally(() => load.close())
      })
    },
    // 弹窗增加成功
    addSuccess(data) {
      if (this.editIndex !== null) {
        // const item = this.addForm.items[this.editIndex]
        this.$set(this.addForm.items, this.editIndex, data)
        this.$forceUpdate()
      } else {
        this.addForm.items.push(data)
      }
      this.editIndex = null
    },
    // 添加申请范围
    add() {
      if (!this.addForm.projDto.areaCompanyId && !this.addForm.projDto.cityCompanyId) {
        this.$message.warning('请选择区域/城市！')
        return
      }
      this.editIndex = null
      this.editData = null
      this.showDialog = true
    }
  }

}
</script>

<style scoped lang="scss">

.m_apply_add{
  background: #fff;
  border-radius: 12px;
  box-shadow: 0px 3px 5px 1px rgba(229,229,229,0.43);
  padding: 20px;
  box-sizing: border-box;
  .g-plan-title-icon{
    display: flex;
    align-items: center;
    margin-left: 5px;
    .u-txt-icon{
      width: 16px;
      height: 16px;
      background: url("~@/assets/plan/<EMAIL>") no-repeat center/100%;
    }
    .u-txt-title{
      margin-left: 4px;
      font-size: 16px;
      font-weight: 600;
      color: #31415F;
    }
  }
  .g-plan-form{
    padding: 0 8px;
    margin-top: 24px;
  }
}
</style>
