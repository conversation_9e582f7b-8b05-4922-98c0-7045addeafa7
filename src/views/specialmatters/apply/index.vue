<template>
  <div class="g-container">
    <UmSearchLayout label-width="80px">
      <template #default>
        <el-form-item label="申请编号：">
          <el-input v-model="searchForm1.applyCode" placeholder="请输入申请编号" clearable />
        </el-form-item>
        <el-form-item label="区域/城市：">
          <UmBusOrgan v-model="searchForm1.projDto" is-auth placeholder="请选择区域城市" />
        </el-form-item>
        <el-form-item label="创建人：">
          <el-input v-model="searchForm1.applyUserName" placeholder="请输入创建人姓名" clearable />
        </el-form-item>
      </template>
      <template #suffix>
        <div style="margin-bottom: 20px;">
          <el-button
            v-if="$checkPermission(['TSSX_TSSXSQ_TSSXSQ'])"
            type="primary"
            icon="el-icon-circle-plus-outline"
            class="margin-r-10"
            @click="$router.push('/specialmatters/apply/add')"
          >
            特殊事项申请
          </el-button>
          <el-button :loading="tableLoading1" type="primary" icon="el-icon-search" @click="getList1">搜索</el-button>
        </div>
      </template>
    </UmSearchLayout>
    <div v-loading="tableLoading1" class="g-table_full margin-t-20">
      <el-row type="flex" justify="space-between" class="mb-20">
        <div>
          <CommonTabs :tabs="tabs" @changeTab="changeTab" />
        </div>
      </el-row>
      <um-table-full :data="tableData1" scroll>
        <el-table-column prop="xh" label="序号" width="80" />
        <el-table-column prop="applyCode" label="申请编号" />
        <el-table-column prop="cityCompanyName" label="区域/城市">
          <template slot-scope="{ row }">
            {{ row.areaCompanyName }}-{{ row.cityCompanyName }}
          </template>
        </el-table-column>
        <el-table-column prop="creator" label="创建人" />
        <el-table-column prop="createTime" label="创建时间" />
        <el-table-column width="140px" label="操作">
          <template slot-scope="{ row }">
            <el-button
              v-if="$checkPermission(['TSSX_TSSXSQ_BJ']) && [OA_STATUS.DRAFT, OA_STATUS.RETURN].includes(row.statusCode)"
              type="text"
              @click="$router.push('/specialmatters/apply/edit?id=' + row.uuid)"
            >
              编辑
            </el-button>
            <el-button
              v-if="$checkPermission(['TSSX_TSSXSQ_SC']) && row.statusCode === OA_STATUS.DRAFT"
              type="text"
              @click="del(row)"
            >
              <span class="danger">删除</span>
            </el-button>
            <el-button
              v-if="$checkPermission(['TSSX_TSSXSQ_XQ']) && row.statusCode !== OA_STATUS.DRAFT"
              type="text"
              @click="$router.push('/specialmatters/apply/detail?id=' + row.uuid)"
            >
              详情
            </el-button>
          </template>
        </el-table-column>
      </um-table-full>
      <pagination
        :total="tableTotal1"
        :page.sync="searchForm1.page.pageNum"
        :limit.sync="searchForm1.page.pageSize"
        background
        layout="total, sizes, prev, pager, next, jumper"
        @pagination="getList1('page')"
      />
    </div>
  </div>
</template>

<script>
import getList1 from '@/mixins/getList1'
import CommonTabs from '@/components/CommonTabs'
import { pageList, deleteMatter } from '@/api/specialmatters/apply'
import { OA_STATUS } from '@/enum'
import UmBusOrgan from '@/components/UmBusOrgan'
// 特殊事项申请
export default {
  name: 'SpecialmattersApply',
  components: { CommonTabs, UmBusOrgan },
  mixins: [getList1],
  props: {},
  data() {
    return {
      listApi1: pageList,
      OA_STATUS,
      searchForm1: {
        statusCode: OA_STATUS.DRAFT,
        applyUserName: null,
        applyCode: null,
        projDto: {}
      },
      tabs: [
        {
          label: '草稿',
          value: OA_STATUS.DRAFT,
          count: 0,
          key: 'draft'
        },
        {
          label: '审批中',
          value: OA_STATUS.APPROVAL,
          count: 0,
          key: 'review'
        },
        {
          label: '审批通过',
          value: OA_STATUS.PASS,
          count: 0,
          key: 'pass'
        },
        {
          label: '审批不通过',
          value: OA_STATUS.NO_PASS,
          count: 0,
          key: 'noPass'
        },
        {
          label: '驳回',
          value: OA_STATUS.RETURN,
          count: 0,
          key: 'return'
        }
      ]
    }
  },
  activated() {
    this.getList1()
  },
  methods: {
    // tab上的统计数据
    tableCallBack1() {
      this.tabs.forEach(item => {
        item.count = this.tableCount1[item.key] || 0
      })
    },
    changeTab(value) {
      this.searchForm1.statusCode = value
      this.getList1()
    },
    del(data) {
      this.$confirm('是否确定删除当前申请编号：' + data.applyCode, '提示', {
        confirmButtonText: '确定',
        type: 'warning',
        callback: action => {
          if (action === 'confirm') {
            const load = this.$load()
            deleteMatter({ uuid: data.uuid }).then(res => {
              this.$message.success('操作成功')
              this.getList1()
            }).finally(() => load.close())
          }
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.mb-20 {
  margin-bottom: 20px;
}

.g-container {
  height: 100%;
  display: flex;
  flex-direction: column;
}
</style>

