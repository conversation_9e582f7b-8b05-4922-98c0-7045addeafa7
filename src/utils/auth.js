// import Cookies from 'js-cookie'

import Storage from '@/utils/storage'

const TokenKey = 'Token'

// if (process.env.NODE_ENV === 'localdev') {
//   const token = 'eyJ0eXAiOiJKV1QiLCJhbGciOiJIUzUxMiJ9.eyJzeXNfY29kZSI6MSwic3ViIjoicGNfMDAwMDExMzk1IiwidXNlcl9pZCI6IjAwMDAxMTM5NSIsImV4cGlyZV90aW1lIjoxNjk5MjQ1MTQwMDI0LCJleHAiOjE2OTkyNDUxNDB9.34KIgUV11WEclXUhHjmnbtmQ0u_QY-eSn2EmWPhSgqbZhoj5O8uJHBSK3kNjYI_zauG3QpKK7MdsSL0knu1bAg'
//   setToken(token)
// }

export function getToken() {
  return Storage.getItem(TokenKey)
}

export function setToken(token) {
  // 全局变量方便组件使用
  // window.token = token
  return Storage.setItem(TokenKey, token, 30)
}

export function removeToken() {
  return Storage.removeItem(TokenKey)
}
