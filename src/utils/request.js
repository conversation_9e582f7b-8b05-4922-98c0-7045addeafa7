import axios from 'axios'
import { Message } from 'element-ui'
import store from '@/store'
import Qs from 'qs'
import { GetQueryString } from '@/utils/index'
import { cloneDeep } from 'lodash'
// 接口请求地址
const requestMap = {}
window.requestMap = requestMap
const token = GetQueryString('token')
// create an axios instance
const service = axios.create({
  baseURL: process.env.VUE_APP_BASE_API, // url = base url + request url
  // withCredentials: true, // send cookies when cross-domain requests
  timeout: 60000 * 10, // request timeout
  paramsSerializer: function(params) {
    return Qs.stringify(params, { arrayFormat: 'comma' })
  }
})

/*
* 批量处理分页 序号问题
 */
function handlePage(res) {
  if (res.data && res.data.list && res.data.page && res.data.page.total) {
    // const total = res.data.page.total
    const pageSize = res.data.page.pageSize
    const pageNum = res.data.page.pageNum
    res.data.list.forEach((item, index) => {
      item.xh = index + pageSize * (pageNum - 1) + 1
    })
  }
  // 支持formData
  if (res.data && res.data.data && res.data.data.data && res.data.page && res.data.page.total) {
    // const total = res.data.page.total
    const pageSize = res.data.page.range
    const pageNum = res.data.page.number
    res.data.data.data.forEach((item, index) => {
      item.xh = index + pageSize * (pageNum - 1) + 1
    })
  }
  return res
}
// 下载文件
function createLink(response) {
  const data = response.data
  const blob = new Blob([data], { type: response.headers['content-type'] })
  const blobUrl = window.URL.createObjectURL(blob)
  const a = document.createElement('a')
  a.download = decodeURI(response.headers['filename']) || '文件.xls'
  a.href = blobUrl
  a.click()
}
// 此处特殊处理请求中的项目
// request interceptor
service.interceptors.request.use(
  config => {
    config.headers['Authorization'] = token || store.getters.token
    config.headers['PositionId'] = store.getters.userInfo.positionId
    if (!config.noCache) {
      const CancelToken = axios.CancelToken // 创建一个token
      const source = CancelToken.source()
      if (requestMap[config.url]) requestMap[config.url].cancel()
      requestMap[config.url] = source
      config.cancelToken = source.token
    }
    if (config.noPage) {
      const params = cloneDeep(config.data)
      delete params.page
      config.data = params
    }

    return config
  },
  error => {
    // do something with request error
    console.log(error) // for debug
    return Promise.reject(error)
  }
)

// response interceptor
service.interceptors.response.use(
  /**
   * If you want to get http information such as headers or status
   * Please return  response => response
   */

  /**
   * Determine the request status by custom code
   * Here is just an example
   * You can also judge the status by HTTP Status Code
   */
  async response => {
    let res = response.data
    if (typeof res === 'object' && res instanceof Blob) {
      if (res.type === 'application/json') {
        await new Promise((resolve, reject) => {
          var reader = new FileReader()
          reader.addEventListener('loadend', function(e) {
            res = JSON.parse(e.target.result)
            resolve()
          })
          reader.readAsText(res)
        })
      } else {
        createLink(response)
        return false
      }
    }
    if (res.code === 3001 || res.state === 'success') { // 成功
      return handlePage(res)
    }

    if (res.code === 3002) { // 失败
      Message({
        message: res.msg,
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(res)
    }
    if (res.code === 3004 || res.code === 3006 || res.code === 1036710) { // 3004 未登录 3006 token刷新
      store.dispatch('user/resetToken').then(() => {
        window.location.href = process.env.VUE_APP_AUTH_API + '/api/identity/pc/sso/login?redirectUrl=' + window.location.origin + '/pc'
      })
      Message({
        message: '未登录',
        type: 'error',
        duration: 5 * 1000
      })
      return Promise.reject(res)
    }
    if (res.code === 3005) { // 无权限
      Message({
        message: '无权限',
        type: 'error',
        duration: 5 * 1000,
        onClose: function() {
          // location.reload()
        }
      })
      return Promise.reject(res)
    }
    // if (res.msg) {
    Message({
      message: res.msg || '操作失败',
      type: 'error',
      duration: 5 * 1000
    })
    // }
    return Promise.reject(res)
  },
  error => {
    console.log(error)
    if (!error.__CANCEL__) {
      Message({
        message: error.message,
        // message: '网络错误',
        type: 'error',
        duration: 5 * 1000
      })
    }

    return Promise.reject(error)
  }
)

// 增加中间层
export default service
