// 项目基础配置
const base = '_myd'

/**
 * 写入缓存
 * @param key 缓存key
 * @param value 缓存值
 * @param exp 过期时间，单位天
 */
export function setItem(key, value, exp = 0) {
  var curtime = new Date().getTime()// 获取当前时间
  localStorage.setItem(key + base, JSON.stringify({
    val: value,
    time: exp === 0 ? 0 : curtime + exp * 24 * 3600 * 1000
  }))// 转换成json字符串序列
}

/**
 * 读取缓存
 * @param key 缓存key
 * @returns {string|*}
 */
export function getItem(key) {
  var val = localStorage.getItem(key + base)// 获取存储的元素
  var dataobj = JSON.parse(val)// 解析出json对象
  if (Object.prototype.toString.call(dataobj) !== '[object Object]') return dataobj
  // 如果当前时间-减去存储的元素在创建时候设置的时间 > 0
  if ((new Date().getTime() - dataobj.time > 0) && dataobj.time !== 0) {
    console.log('expires')// 提示过期
    removeItem(key + base)
    return ''
  } else {
    return dataobj.val
  }
}

/**
 * 移除缓存
 * @param key 缓存key
 */
export function removeItem(key) {
  localStorage.removeItem(key + base)
}

export default {
  setItem,
  getItem,
  removeItem
}
