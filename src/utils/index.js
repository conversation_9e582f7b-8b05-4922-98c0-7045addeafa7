import moment from 'moment'
// 错误处理
export function errorHandle(d) {
  if (d.name === 'TypeError') return console.log(d)
  if (typeof this.$refs !== 'object') return false
  if (d.error && typeof d.error === 'object') {
    for (const key in d.error) {
      if (this.$refs[key] !== undefined) {
        if (this.$refs[key] instanceof Array) {
          this.$refs[key][0].validateState = 'error'
          this.$refs[key][0].validateMessage = d.error[key]
        } else {
          this.$refs[key].validateState = 'error'
          this.$refs[key].validateMessage = d.error[key]
        }
      } else {
        // 去除日期格式的Begin 和 End 然后再做匹配
        const key_replace = key.replace(/Begin|End/g, '')
        if (this.$refs[key_replace] !== undefined) {
          if (this.$refs[key_replace] instanceof Array) {
            this.$refs[key_replace][0].validateState = 'error'
            this.$refs[key_replace][0].validateMessage = d.error[key]
          } else {
            this.$refs[key_replace].validateState = 'error'
            this.$refs[key_replace].validateMessage = d.error[key]
          }
        } else {
          // this.$message.error(JSON.stringify(d.error) || '操作失败', 10) // todo
        }
      }
    }
    // this.$message.error(d.msg || '操作失败', 1)
    return false
  }
  // if (d.msg !== '') {
  //   this.$message.error(d.msg, 1)
  //   return false
  // }
  // this.$message.error('操作失败', 1)
}
/**
 * Parse the time to string
 * @param {(Object|string|number)} time
 * @param {string} cFormat
 * @returns {string | null}
 */
export function parseTime(time, cFormat) {
  if (arguments.length === 0 || !time) {
    return null
  }
  const format = cFormat || '{y}-{m}-{d} {h}:{i}:{s}'
  let date
  if (typeof time === 'object') {
    date = time
  } else {
    if ((typeof time === 'string')) {
      if ((/^[0-9]+$/.test(time))) {
        // support "1548221490638"
        time = parseInt(time)
      } else {
        // support safari
        // https://stackoverflow.com/questions/4310953/invalid-date-in-safari
        time = time.replace(new RegExp(/-/gm), '/')
      }
    }

    if ((typeof time === 'number') && (time.toString().length === 10)) {
      time = time * 1000
    }
    date = new Date(time)
  }
  const formatObj = {
    y: date.getFullYear(),
    m: date.getMonth() + 1,
    d: date.getDate(),
    h: date.getHours(),
    i: date.getMinutes(),
    s: date.getSeconds(),
    a: date.getDay()
  }
  const time_str = format.replace(/{([ymdhisa])+}/g, (result, key) => {
    const value = formatObj[key]
    // Note: getDay() returns 0 on Sunday
    if (key === 'a') { return ['日', '一', '二', '三', '四', '五', '六'][value] }
    return value.toString().padStart(2, '0')
  })
  return time_str
}

export function handleFile(arr) {
  if (arr.constructor !== Array || arr.length === 0) {
    return []
  }
  return arr.map(item => item.filePath)
}

export function getFileName(url) {
  if (!url) return
  return url.substr(url.lastIndexOf('/') + 1)
}

/**
 * @param {number} time
 * @param {string} option
 * @returns {string}
 */
export function formatTime(time, option) {
  if (('' + time).length === 10) {
    time = parseInt(time) * 1000
  } else {
    time = +time
  }
  const d = new Date(time)
  const now = Date.now()

  const diff = (now - d) / 1000

  if (diff < 30) {
    return '刚刚'
  } else if (diff < 3600) {
    // less 1 hour
    return Math.ceil(diff / 60) + '分钟前'
  } else if (diff < 3600 * 24) {
    return Math.ceil(diff / 3600) + '小时前'
  } else if (diff < 3600 * 24 * 2) {
    return '1天前'
  }
  if (option) {
    return parseTime(time, option)
  } else {
    return (
      d.getMonth() +
      1 +
      '月' +
      d.getDate() +
      '日' +
      d.getHours() +
      '时' +
      d.getMinutes() +
      '分'
    )
  }
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function getQueryObject(url) {
  url = url == null ? window.location.href : url
  const search = url.substring(url.lastIndexOf('?') + 1)
  const obj = {}
  const reg = /([^?&=]+)=([^?&=]*)/g
  search.replace(reg, (rs, $1, $2) => {
    const name = decodeURIComponent($1)
    let val = decodeURIComponent($2)
    val = String(val)
    obj[name] = val
    return rs
  })
  return obj
}

/**
 * @param {string} input value
 * @returns {number} output value
 */
export function byteLength(str) {
  // returns the byte length of an utf8 string
  let s = str.length
  for (var i = str.length - 1; i >= 0; i--) {
    const code = str.charCodeAt(i)
    if (code > 0x7f && code <= 0x7ff) s++
    else if (code > 0x7ff && code <= 0xffff) s += 2
    if (code >= 0xDC00 && code <= 0xDFFF) i--
  }
  return s
}

/**
 * @param {Array} actual
 * @returns {Array}
 */
export function cleanArray(actual) {
  const newArray = []
  for (let i = 0; i < actual.length; i++) {
    if (actual[i]) {
      newArray.push(actual[i])
    }
  }
  return newArray
}

/**
 * @param {Object} json
 * @returns {Array}
 */
export function param(json) {
  if (!json) return ''
  return cleanArray(
    Object.keys(json).map(key => {
      if (json[key] === undefined) return ''
      return encodeURIComponent(key) + '=' + encodeURIComponent(json[key])
    })
  ).join('&')
}

/**
 * @param {string} url
 * @returns {Object}
 */
export function param2Obj(url) {
  const search = decodeURIComponent(url.split('?')[1]).replace(/\+/g, ' ')
  if (!search) {
    return {}
  }
  const obj = {}
  const searchArr = search.split('&')
  searchArr.forEach(v => {
    const index = v.indexOf('=')
    if (index !== -1) {
      const name = v.substring(0, index)
      const val = v.substring(index + 1, v.length)
      obj[name] = val
    }
  })
  return obj
}

/**
 * @param {string} val
 * @returns {string}
 */
export function html2Text(val) {
  const div = document.createElement('div')
  div.innerHTML = val
  return div.textContent || div.innerText
}

/**
 * Merges two objects, giving the last one precedence
 * @param {Object} target
 * @param {(Object|Array)} source
 * @returns {Object}
 */
export function objectMerge(target, source) {
  if (typeof target !== 'object') {
    target = {}
  }
  if (Array.isArray(source)) {
    return source.slice()
  }
  Object.keys(source).forEach(property => {
    const sourceProperty = source[property]
    if (typeof sourceProperty === 'object') {
      target[property] = objectMerge(target[property], sourceProperty)
    } else {
      target[property] = sourceProperty
    }
  })
  return target
}

/**
 * @param {HTMLElement} element
 * @param {string} className
 */
export function toggleClass(element, className) {
  if (!element || !className) {
    return
  }
  let classString = element.className
  const nameIndex = classString.indexOf(className)
  if (nameIndex === -1) {
    classString += '' + className
  } else {
    classString =
      classString.substr(0, nameIndex) +
      classString.substr(nameIndex + className.length)
  }
  element.className = classString
}

/**
 * @param {string} type
 * @returns {Date}
 */
export function getTime(type) {
  if (type === 'start') {
    return new Date().getTime() - 3600 * 1000 * 24 * 90
  } else {
    return new Date(new Date().toDateString())
  }
}

/**
 * @param {Function} func
 * @param {number} wait
 * @param {boolean} immediate
 * @return {*}
 */
export function debounce(func, wait, immediate) {
  let timeout, args, context, timestamp, result

  const later = function() {
    // 据上一次触发时间间隔
    const last = +new Date() - timestamp

    // 上次被包装函数被调用时间间隔 last 小于设定时间间隔 wait
    if (last < wait && last > 0) {
      timeout = setTimeout(later, wait - last)
    } else {
      timeout = null
      // 如果设定为immediate===true，因为开始边界已经调用过了此处无需调用
      if (!immediate) {
        result = func.apply(context, args)
        if (!timeout) context = args = null
      }
    }
  }

  return function(...args) {
    context = this
    timestamp = +new Date()
    const callNow = immediate && !timeout
    // 如果延时不存在，重新设定延时
    if (!timeout) timeout = setTimeout(later, wait)
    if (callNow) {
      result = func.apply(context, args)
      context = args = null
    }

    return result
  }
}

/**
 * This is just a simple version of deep copy
 * Has a lot of edge cases bug
 * If you want to use a perfect deep copy, use lodash's _.cloneDeep
 * @param {Object} source
 * @returns {Object}
 */
export function deepClone(source) {
  if (!source && typeof source !== 'object') {
    throw new Error('error arguments', 'deepClone')
  }
  const targetObj = source.constructor === Array ? [] : {}
  Object.keys(source).forEach(keys => {
    if (source[keys] && typeof source[keys] === 'object') {
      targetObj[keys] = deepClone(source[keys])
    } else {
      targetObj[keys] = source[keys]
    }
  })
  return targetObj
}

/**
 * @param {Array} arr
 * @returns {Array}
 */
export function uniqueArr(arr) {
  return Array.from(new Set(arr))
}

/**
 * @returns {string}
 */
export function createUniqueString() {
  const timestamp = +new Date() + ''
  const randomNum = parseInt((1 + Math.random()) * 65536) + ''
  return (+(randomNum + timestamp)).toString(32)
}

/**
 * Check if an element has a class
 * @param {HTMLElement} elm
 * @param {string} cls
 * @returns {boolean}
 */
export function hasClass(ele, cls) {
  return !!ele.className.match(new RegExp('(\\s|^)' + cls + '(\\s|$)'))
}

/**
 * Add class to element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function addClass(ele, cls) {
  if (!hasClass(ele, cls)) ele.className += ' ' + cls
}

/**
 * Remove class from element
 * @param {HTMLElement} elm
 * @param {string} cls
 */
export function removeClass(ele, cls) {
  if (hasClass(ele, cls)) {
    const reg = new RegExp('(\\s|^)' + cls + '(\\s|$)')
    ele.className = ele.className.replace(reg, ' ')
  }
}

/**
 *
 * @param list1 比较的第一个数组
 * @param list2 比较的第二个数组
 * @returns {boolean}
 */

export function sameList(list1, list2) {
  if (!Array.isArray(list1) || !Array.isArray(list2)) {
    return false
  }
  if (list2.length === 0) return true
  return list1.some(item => list2.includes(item))
}

/**
 * 数组对象去重
 * @param {Array} arr
 * @param {String} key 唯一标志
 */
export function uniqueArrObj(arr, key) {
  const newJson = []
  for (const item1 of arr) {
    let flag = true
    for (const item2 of newJson) {
      if (item1[key] === item2[key]) {
        flag = false
      }
    }
    if (flag) {
      newJson.push(item1)
    }
  }
  return newJson
}

// 平级结构 转树
export function setTreeData(tmp, key = 'id', pid = 'parentId') {
  const arr = uniqueArrObj(tmp, 'id')
  // console.log(arr)
  arr.forEach(function(item) {
    delete item.children
  })
  const map = {}
  arr.forEach(item => {
    map[item[key]] = item
  })
  const treeData = []
  arr.forEach(child => {
    const mapItem = map[child[pid]]
    if (mapItem) {
      (mapItem.children || (mapItem.children = [])).push(child)
    } else {
      treeData.push(child)
    }
  })

  return treeData
}

// 两个日期算出中间所有的日期数组
export function getDateInfo(begin, end) {
  const arr = []
  const str_b = begin.split('-')
  const str_e = end.split('-')
  const date_b = new Date()
  date_b.setUTCFullYear(str_b[0], str_b[1] - 1, str_b[2])
  const date_e = new Date()
  date_e.setUTCFullYear(str_e[0], str_e[1] - 1, str_e[2])
  const unixDb = date_b.getTime() - 24 * 60 * 60 * 1000
  const unixDe = date_e.getTime() - 24 * 60 * 60 * 1000
  for (let j = unixDb; j <= unixDe;) {
    j = j + 24 * 60 * 60 * 1000

    arr.push(moment(parseInt(j)).format('YYYY-MM-DD'))
  }
  return arr
}
// 分钟转 （12:30）
export function changeHourMinutestr(str) {
  if (str !== '0' && str !== '' && str !== null) {
    return ((Math.floor(str / 60)).toString().length < 2 ? '0' + (Math.floor(str / 60)).toString()
      : (Math.floor(str / 60)).toString()) + ':' + ((str % 60).toString().length < 2 ? '0' + (str % 60).toString() : (str % 60).toString())
  } else {
    return '00:00'
  }
}
// 列表删除数据 保持当前页（如果最后一页只有一条数据，删除后回到上一页）
export function tableDelKeep(total, pageNum, pageSize) {
  // 获取删除后最后一页数据的数目
  const lastPageSize = (total - 1) % pageSize
  // 如果最后一页数据数目小于1就将pageNum-1传给获取数据方法，否则不变
  return lastPageSize < 1 ? pageNum - 1 : pageNum
}
// 比较两个日期的大小
export function comparedate(date1, date2, flag) {
  const oDate1 = new Date(date1)
  let oDate2 = date2
  if (flag) {
    oDate2 = new Date(date2)
  }

  if (oDate1.getTime() === oDate2.getTime()) {
    return true
  } else if (oDate1.getTime() > oDate2.getTime()) {
    return true
  } else {
    return false
  }
}

/**
 * 10000 => "10,000"
 * @param {number} num
 */
export function toThousandFilter(num) {
  return (+num || 0).toString().replace(/^-?\d+/g, m => m.replace(/(?=(?!\b)(\d{3})+$)/g, ','))
}
// 格式化金额
export function formatMoney(val) {
  return toThousandFilter(val)
}

// 图片转换base64  , 异步处理
export function base64Img(path) {
  function getBase64Image(img) { // 转码格式方法
    var canvas = document.createElement('canvas')// 创建一个canvas
    canvas.width = img.width // 设置对应的宽高
    canvas.height = img.height
    var ctx = canvas.getContext('2d') // 二维绘图环境
    ctx.drawImage(img, 0, 0, img.width, img.height) // 将图片画在画布上
    var ext = img.src.substring(img.src.lastIndexOf('.') + 1).toLowerCase() // 获取到图片的格式
    var dataURL = canvas.toDataURL('image/' + ext) // 得到base64 编码的 dataURL
    return dataURL
  }
  return new Promise(function(resolve, reject) { // 使用Promise进行异步处理
    const image = new Image()
    // 解决图片跨域问题
    image.crossOrigin = ''
    // 获取传入的图片路径
    image.src = path
    // 图片加载完后的回调函数,调用转码函数
    image.onload = function() {
      resolve(getBase64Image(image)) // 回调函数返回base64值
    }
  })
}

// 获取浏览器参数
export function GetQueryString(variable) {
  var query = window.location.search.substring(1)
  var vars = query.split('&')
  for (var i = 0; i < vars.length; i++) {
    var pair = vars[i].split('=')
    if (pair[0] === variable) { return pair[1] }
  }
  return (false)
}

var requestAnimFrame = (function() {
  return window.requestAnimationFrame || window.webkitRequestAnimationFrame || window.mozRequestAnimationFrame || function(callback) { window.setTimeout(callback, 1000 / 60) }
})()
/**
 * @param {number} to
 * @param {number} duration
 * @param {Function} callback
 */
export function scrollTo(target, to, duration, callback) {
  Math.easeInOutQuad = function(t, b, c, d) {
    t /= d / 2
    if (t < 1) {
      return c / 2 * t * t + b
    }
    t--
    return -c / 2 * (t * (t - 2) - 1) + b
  }
  const start = target.scrollTop
  const change = to - start
  const increment = 20
  let currentTime = 0
  duration = (typeof (duration) === 'undefined') ? 500 : duration
  // requestAnimationFrame for Smart Animating http://goo.gl/sx5sts
  var animateScroll = function() {
    // increment the time
    currentTime += increment
    // find the value with the quadratic in-out easing function
    var val = Math.easeInOutQuad(currentTime, start, change, duration)
    // move the document.body
    target.scrollTop = val
    // do the animation unless its over
    if (currentTime < duration) {
      requestAnimFrame(animateScroll)
    } else {
      if (callback && typeof (callback) === 'function') {
        // the animation is done so lets callback
        callback()
      }
    }
  }
  animateScroll()
}
/**
 *
 * @param {*} number 37
 * @returns 三十七
 */
export function numberToWords(number) {
  const ones = ['', '一', '二', '三', '四', '五', '六', '七', '八', '九', '十']
  const tens = ['', '十', '二十', '三十', '四十', '五十', '六十', '七十', '八十', '九十']

  if (number === 0) {
    return '零'
  }

  let result = ''
  let num = number

  if (num >= 10 && num <= 99) {
    result += tens[Math.floor(num / 10)]
    num %= 10
  }

  if (num >= 1 && num <= 9) {
    result += ones[num]
  }

  return result
}

// 扁平化树形结构数组
export function flattenTree(tree, key = 'children') {
  const result = []
  function recurse(node) {
    if (node) {
      // 将当前节点添加到结果数组
      result.push({ ...node })

      // 如果当前节点有子节点，递归处理子节点
      if (node[key] && node[key].length) {
        node[key].forEach(child => recurse(child))
      }
    }
  }
  tree.forEach(node => recurse(node))
  return result
}

/**
 * 提取文件名和扩展名
 * @param {string} url - 包含文件名和扩展名的 URL 地址
 * @returns {object} - 返回一个包含文件名和文件类型的对象
 */
export function extractFileNameAndType(url) {
  const regex = /([^/]+)\.([^/]+)$/ // 匹配最后的文件名和扩展名
  const match = url.match(regex)

  if (match) {
    return {
      fileName: match[1], // 文件名
      fileType: match[2] // 文件类型
    }
  } else {
    console.log('URL 格式不正确，无法提取文件名和类型')
    return null
  }
}
