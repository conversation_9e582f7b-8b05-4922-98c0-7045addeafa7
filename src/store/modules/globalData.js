import { pageExportList } from '@/api/common'
const state = {
  exportShow: false,
  loading: false,
  computedActiveRoute: false, // 重新计算当前高亮的路由
  exportList: [], // 到处任务中的数据
  isAsyncCloseView: false // 当前关闭的页面，是否有组织页面关闭动作，如问卷编辑页面
}

const mutations = {
  SET_EXPORT_LIST: (state, list) => {
    state.exportList = list
  },
  SET_EXPORT_SHOW: (state, show) => {
    state.exportShow = show
  },
  SET_LOADING: (state, loading) => {
    state.loading = loading
  },
  SET_COMPUTED_ACTIVE_ROUTE: (state, flag) => {
    state.computedActiveRoute = flag
  },
  SET_IS_ASYNC_CLOSE_VIEW(state, flag) {
    state.isAsyncCloseView = flag
  }
}

const actions = {
  GET_EXPORT_LIST({ commit }, page = {
    pageNum: 1,
    pageSize: 20
  }) {
    commit('SET_LOADING', true)
    pageExportList({ page }).then(res => {
      commit('SET_EXPORT_LIST', res.data.list || [])
    }).finally(() => commit('SET_LOADING', false))
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}

