import { getUserInfo, getUserPosition, getUserPermission, getTreeStage, gufenAuth } from '@/api/common'
import {
  setToken,
  removeToken
} from '@/utils/auth'
import Storage from '@/utils/storage'
import { flattenTree } from '@/utils'
import router from '@/router/index'
// 全局变量，方便组件使用
const state = {
  token: null,
  userInfo: {}, // 用户信息 userID name userName
  positions: [], // 用户岗位信息
  roles: [], // 操作按钮权限信息
  authOrganTree: [], // 带有权限的区域、城市、项目
  organTree: [], // 所有的区域、城市、项目
  hasGuFenAuth: false // 是否有股份权限
}

const mutations = {
  SET_TOKEN: (state, token) => {
    state.token = token
  },
  SET_USERINFO: (state, userInfo) => {
    const cacheUserInfo = Storage.getItem('userInfo')
    state.userInfo = {
      ...cacheUserInfo,
      ...state.userInfo,
      ...userInfo
    }
    Storage.setItem('userInfo', state.userInfo)
  },
  SET_POSITIONS: (state, positions) => {
    state.positions = positions
  },
  SET_ROLES: (state, roles) => {
    state.roles = roles
  },
  SET_MENUS: (state, menus) => {
    state.menus = menus
  },
  SET_AUTH_ORGAN_TREE(state, tree) {
    state.authOrganTree = tree
  },
  SET_ORGAN_TREE(state, tree) {
    state.organTree = tree
  },
  SET_HAS_GUFEN_AUTH(state, auth) {
    state.hasGuFenAuth = auth
  }
}

const actions = {
  // 获取用户信息
  getInfo({ commit, dispatch }) {
    return new Promise((resolve, reject) => {
      getUserInfo()
        .then(async res => {
          commit('SET_USERINFO', { ...res.data })
          await dispatch('getUserPosition')
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 获取用户岗位信息
  getUserPosition({ commit, dispatch, state }) {
    return new Promise((resolve, reject) => {
      getUserPosition({ userId: state.userInfo.userId, newest: true })
        .then(async res => {
          const arr = res.data || []
          const cacheUserInfo = Storage.getItem('userInfo')
          if (arr.every(item => item.disabled)) {
            router.push({ path: '/404', replace: true })
            return reject('暂无任何权限')
          }
          commit('SET_POSITIONS', arr)
          // 过滤被禁用的岗位
          const hasAuthPositions = arr.filter(item => !item.disabled)
          // 去缓存中查找用户信息，是否存在岗位信息，存在岗位信息的话，是否岗位在数组中，在就继续使用，不在就使用岗位数组中的第一项
          if (!state.userInfo.positionId || hasAuthPositions.findIndex(item => item.positionId === cacheUserInfo.positionId) === -1) {
            commit('SET_USERINFO', {
              positionId: hasAuthPositions[0].positionId
            })
          } else {
            commit('SET_USERINFO', {
              positionId: cacheUserInfo.positionId
            })
          }
          await dispatch('getUserPermission')
          resolve(res.data)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // 获取用户菜单目录，和按钮权限
  getUserPermission({ commit }) {
    return new Promise((resolve, reject) => {
      getUserPermission({ userId: state.userInfo.userId, newest: true, positionId: state.userInfo.positionId })
        .then(res => {
          const { buttonList = [], menuList = [] } = res.data
          const roles = buttonList.map(item => item.fieldId).concat(flattenTree(menuList).map(item => item.menuId))
          // 扁平化tree数组，取出其中的menuId，放入到用户的权限集合中
          commit('SET_ROLES', roles)
          resolve(roles)
        })
        .catch(error => {
          reject(error)
        })
    })
  },
  // set token
  setToken({ commit }, token) {
    return new Promise(resolve => {
      commit('SET_TOKEN', token)
      setToken(token)
      resolve()
    })
  },
  // remove token
  resetToken({ commit }) {
    return new Promise(resolve => {
      commit('SET_TOKEN', '')
      commit('SET_ROLES', [])
      removeToken()
      resolve()
    })
  },
  getAuthOrganTree({ commit }) {
    return new Promise((resolve, reject) => {
      getTreeStage().then(res => {
        const allFlatTree = flattenTree(res.data || [])
        commit('SET_AUTH_ORGAN_TREE', allFlatTree.filter(item => !item.disabled))
        commit('SET_ORGAN_TREE', allFlatTree)
        resolve()
      }).catch((error) => {
        reject(error)
      })
    })
  },
  getGuFenAuth({ commit }) {
    return new Promise((resolve, reject) => {
      gufenAuth().then(res => {
        commit('SET_HAS_GUFEN_AUTH', res.data)
        resolve()
      }).catch((error) => {
        reject(error)
      })
    })
  }
}

export default {
  namespaced: true,
  state,
  mutations,
  actions
}
