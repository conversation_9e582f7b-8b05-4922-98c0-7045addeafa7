<template>
  <el-select
    v-model="id"
    v-loading="loading"
    :clearable="clearable"
    filterable
    style="width: 100%;"
    placeholder="请选择"
    :disabled="disabled"
    @change="change"
  >
    <el-option
      v-for="item in options"
      :key="item.code"
      :label="item.name"
      :value="item.code"
    />
  </el-select>
</template>

<script>
import { getBuilding, getHouse, getUnit } from '@/api/common'
export default {
  name: 'UmBusHouse',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 回显的项目id
    value: {
      type: Object,
      default: null
    },
    // 字段key，区分 区域【buildingId】/城市【unitId】/项目【houseId】
    valueKey: {
      type: String,
      default: null,
      require: true
    },
    buildingId: {
      type: [String, Number],
      default: null
    },
    unitId: {
      type: [String, Number],
      default: null
    },
    projDto: {
      type: Object,
      default: () => ({})
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      searchParams: {},
      options: [],
      loading: false
    }
  },
  computed: {
    id: {
      get() {
        return this.value[this.valueKey]
      },
      set() {

      }
    },
    disabled() {
      let flag = false
      switch (this.valueKey) {
        case 'buildingId': flag = !this.projDto.stageId; break
        case 'unitId': flag = !this.value.buildingId; break
        case 'houseId': flag = !this.value.buildingId || !this.value.unitId; break
      }
      return flag
    }
  },
  watch: {
    projDto: {
      immediate: true,
      deep: true,
      handler(v) {
        this.$emit('change', {
          buildingId: null,
          unitId: null,
          houseId: null
        })
        switch (this.valueKey) {
          case 'buildingId':
            if (v.stageId) {
              this.searchParams = {
                stageId: v.stageId,
                projectId: v.projectId
              }

              this.getData()
            }
            break
        }
      }
    },
    value: {
      immediate: true,
      deep: true,
      handler(v) {
        switch (this.valueKey) {
          case 'unitId':
            if (v.buildingId) {
              this.searchParams = {
                stageId: this.projDto.stageId,
                projectId: this.projDto.projectId,
                buildingId: v.buildingId
              }
              this.getData()
            }
            break
          case 'houseId':
            if (v.unitId) {
              this.searchParams = {
                stageId: this.projDto.stageId,
                projectId: this.projDto.projectId,
                buildingId: v.buildingId,
                unitId: v.unitId
              }
              this.getData()
            }
            break
        }
      }
    }
  },
  created() {
  },
  methods: {
    getData() {
      this.loading = true
      let api = null
      switch (this.valueKey) {
        case 'buildingId': api = getBuilding; break
        case 'unitId': api = getUnit; break
        case 'houseId': api = getHouse; break
      }
      return api({ ...this.searchParams }).then(async res => {
        const arr = res.data || []
        this.options = arr.map(item => {
          return {
            code: item[this.valueKey],
            name: this.valueKey === 'houseId' ? item.houseNo : item[this.valueKey.replace('Id', 'Name')]
          }
        })
      }).finally(() => {
        this.loading = false
      })
    },
    change(value) {
      switch (this.valueKey) {
        case 'buildingId':
          this.$emit('change', {
            buildingId: value,
            unitId: null,
            houseId: null
          })
          break
        case 'unitId':
          this.$emit('change', {
            ...this.value,
            unitId: value,
            houseId: null
          })
          break
        case 'houseId':
          this.$emit('change', {
            ...this.value,
            houseId: value
          })
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
