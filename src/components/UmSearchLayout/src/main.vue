<script>

export default {
  name: 'UmSearchLayout',
  functional: true,
  render(createElement, ctx) {
    const { default: _default, suffix, prefix, topfix } = ctx.scopedSlots
    let slot_default = []
    let slot_suffix = null
    let slot_prefix = null
    let slot_topfix = null
    if (_default) slot_default = _default().filter(i => i.tag)
    if (suffix) slot_suffix = suffix() // 后置插槽
    if (prefix) slot_prefix = prefix() // 前置插槽
    if (topfix) slot_topfix = topfix() // top插槽
    const { 'label-width': labelWidth } = ctx.data.attrs
    return (
      <el-card>
        {slot_topfix}
        <el-form label-width={labelWidth}>
          <div style="margin-bottom: -20px;">
            <el-row gutter={30} type="flex" style="flex-wrap:wrap;">
              {
                slot_default.map(item => {
                  return (
                    <el-col span={12} md={8} xl={6}>
                      {item}
                    </el-col>
                  )
                })
              }
              <el-col span={12} md={8} xl={6} style="flex:1;text-align:right;">
                {slot_suffix}
              </el-col>
            </el-row>
          </div>
        </el-form>
      </el-card>
    )
  }
}
</script>
