```vue
 <UmSearchLayout label-width="75px">
      <template #default>
        <el-form-item label="公司/项目">
          <um-bus-project
            v-model="searchForm.organProjectUuid"
            placeholder="请选择公司/项目"
            :only-project="false"
            :is-organ="true"
            style="width: 100%"
            clearable
          />
        </el-form-item>
        <el-form-item label="提升指标">
          <el-select v-model="searchForm.quesTargetId" style="width: 100%;" placeholder="请选择提升指标">
            <el-option
              v-for="item in options"
              :key="item.value"
              :label="item.label"
              :value="item.value"
            />
          </el-select>
        </el-form-item>
      </template>
      <template #suffix>
        <el-button type="primary" icon="el-icon-search" :loading="tableLoading" @click="getList">搜索</el-button>
      </template>
    </UmSearchLayout>
```