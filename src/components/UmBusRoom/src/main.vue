<template>
  <el-cascader
    ref="cascader"
    v-model="id"
    v-loading="loading"
    :disabled="!projectId && !stageId"
    :props="{
      label: 'name',
      value: 'code',
      checkStrictly: !checkStrictly && !multiple,
      emitPath,
      multiple
    }"
    collapse-tags
    filterable
    clearable
    style="width: 100%;"
    :options="options"
    @change="change"
  />
</template>

<script>
import { getRoomTree } from '@/api/common'
import { flattenTree } from '@/utils'
export default {
  name: 'UmBusRoom',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 回显的房间id
    value: {
      type: [Number, String, Array],
      default: null
    },
    projectId: {
      type: [String, Number],
      default: null
    },
    stageId: {
      type: [String, Number],
      default: null
    },
    multiple: {
      type: Boolean,
      default: false
    },
    checkStrictly: {
      type: Boolean,
      default: false
    },
    emitPath: {
      type: Boolean,
      default: true
    },
    initCb: {
      type: Function,
      default: null
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  computed: {
    id: {
      get() {
        return this.value
      },
      set() { }
    },
    // 根据项目和分期id，计算一个新的值，用于查询房间接口
    projectStageId() {
      return {
        projectId: this.projectId,
        stageId: this.stageId
      }
    }
  },
  watch: {
    projectStageId: {
      immediate: true,
      deep: true,
      handler(v) {
        if (!v.projectId && !v.stageId) {
          this.$emit('change', this.multiple ? [] : null)
          this.options = []
          return
        }
        this.getRoomTree()
      }
    }
  },
  methods: {
    getRoomTree() {
      this.$emit('change', this.multiple ? [] : null)
      this.options = []
      this.loading = true
      getRoomTree({ ...this.projectStageId }).then(res => {
        this.options = res.data || []
        this.initCb && this.initCb(this.options) // 特殊场景需要用到这个数组
      }).finally(() => {
        this.loading = false
      })
    },
    change(value) {
      if (this.multiple) {
        this.$emit('change', value)
        return
      }
      this.$emit('change', value)
    },
    // 统计所有最末级叶子节点/选中的叶子结点数量
    getRoomNums(onlyChecked) {
      const flatArr = onlyChecked ? this.getSelectNodes() : flattenTree(this.options)
      return flatArr.filter(item => !item.children || (!item.hasChildren && item.hasChildren !== undefined && item.hasChildren !== null)).length
    },
    getSelectNodes() {
      return this.$refs.cascader.getCheckedNodes()
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
