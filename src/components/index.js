import ElImageViewer from 'element-ui/packages/image/src/image-viewer'

import UmUploadFile from '@/components/UmUploadFile'
import UmBusProject from '@/components/UmBusProject'
import UmBusCommon from '@/components/UmBusCommon'
import UmBusHouse from '@/components/UmBusHouse'
import Um<PERSON><PERSON><PERSON>rgan from '@/components/UmBusOrgan'
import UmBusRoom from '@/components/UmBusRoom'
import UmBusScene from '@/components/UmBusScene'
import UmUploadImg from '@/components/UmUploadImg'
import Pagination from '@/components/Pagination'
import UmSearchLayout from '@/components/UmSearchLayout'
import ElSelectTree from '@/components/ElSelectTree'
import UmToolTips from '@/components/UmToolTips/index.vue'

// table 全屏组件
import UmTableFull from '@/components/UmTableFull'

import UmInlineEdit from './UmInlineEdit'
import UmSQ from './UmSQ'
// import UmSatisfiedSQ from './UmSatisfiedSQ'
import UmConfigTree from './UmConfigTree'
import UmEmpty from './UmEmpty'

const Components = [
  UmInlineEdit,
  UmSQ,
  // UmSatisfiedSQ,
  UmConfigTree,
  ElImageViewer,
  UmUploadFile,
  UmTableFull,
  Pagination,
  UmBusProject,
  UmBusCommon,
  UmBusHouse,
  UmBusOrgan,
  UmBusRoom,
  UmBusScene,
  UmUploadImg,
  UmSearchLayout,
  ElSelectTree,
  UmEmpty,
  UmToolTips
]
// 定义 install 方法
const install = function(Vue) {
  if (install.installed) return
  install.installed = true

  // 遍历并注册全局组件
  Components.forEach(component => {
    Vue.component(component.name, component)
  })
}
if (typeof window !== 'undefined' && window.Vue) {
  install(window.Vue)
}
export default {
  install,
  // 组件列表
  ...Components
}
