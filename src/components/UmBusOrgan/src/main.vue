<template>
  <el-select-tree
    ref="elSelectTree"
    v-model="ids"
    :options="options"
    :filterable="true"
    separator="-"
    :check-strictly="checkStrictly"
    :show-all-levels="true"
    :clearable="true"
    :props="{
      emitPath: true,
      label: 'name',
      value: 'code'
    }"
    :placeholder="placeholder"
    style="width: 100%;"
    v-bind="$attrs"
    @change="change"
  />
</template>

<script>
// getTreeStage 查询带权限的数据
import { getTwoLevelOrgan, getTreeStage, getCityCompany } from '@/api/common'
export default {
  name: 'UmBusOrgan',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: Object,
      default: () => ({})
    },
    placeholder: {
      type: String,
      default: '请选择'
    },
    // 是否任选一级， 默认不可以
    checkStrictly: {
      type: Boolean,
      default: true
    },
    // 是否查询带有数据权限的区域。城市
    isAuth: {
      type: Boolean,
      default: false
    },
    // 是否查询带权限的区域公司至分期4级树
    isStage: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      options: [],
      id: null
    }
  },
  computed: {
    ids: {
      get() {
        if (this.isStage) {
          return Object.values(this.value).filter(item => item)
        }
        const obj = {
          areaCompanyId: this.value.areaCompanyId,
          cityCompanyId: this.value.cityCompanyId
        }
        return Object.values(obj).filter(item => item)
      },
      set() { }
    }
  },
  created() {
    this.getTwoLevelOrgan()
  },
  methods: {
    getTwoLevelOrgan() {
      const API = this.isAuth ? getCityCompany : (this.isStage ? getTreeStage : getTwoLevelOrgan)
      API().then(res => {
        this.options = res.data || []
      })
    },
    change(value) {
      this.$emit('change', this.isStage ? {
        areaCompanyId: value[0],
        cityCompanyId: value[1],
        projectId: value[2],
        stageId: value[3]
      } : {
        ...this.value,
        areaCompanyId: value[0],
        cityCompanyId: value[1]
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
