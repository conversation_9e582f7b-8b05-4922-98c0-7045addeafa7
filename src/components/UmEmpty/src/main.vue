<template>
  <el-empty
    :style="{
      height: '100%',
      width: '100%'
    }"
    :description="title"
    :image="image"
  />
</template>

<script>
export default {
  name: 'UmEmpty',
  props: {
    title: {
      type: String,
      default: '暂无数据'
    },
    image: {
      type: String,
      default: require('../../../assets/common/empty.png')
    }
  },
  data() {
    return {
    }
  },
  created() {
  },
  methods: {

  }
}
</script>

<style lang="scss" scoped>
</style>

