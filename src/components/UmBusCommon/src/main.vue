<template>
  <el-select
    v-model="id"
    v-loading="loading"
    :clearable="clearable"
    filterable
    style="width: 100%;"
    placeholder="请选择"
    :disabled="disabled"
    @change="change"
  >
    <el-option
      v-for="item in options"
      :key="item.code"
      :label="item.name"
      :value="item.code"
    />
  </el-select>
</template>

<script>
import { getAreaList, getProjectList, getCityList } from '@/api/common'
import { mapGetters, mapActions } from 'vuex'
export default {
  name: 'UmBusCommon',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 回显的项目id
    value: {
      type: Object,
      default: null
    },
    // 字段key，区分 区域【areaCompanyId】/城市【cityCompanyId】/项目【projectId】
    valueKey: {
      type: String,
      default: null,
      require: true
    },
    areaCompanyId: {
      type: [String, Number],
      default: null
    },
    cityCompanyId: {
      type: [String, Number],
      default: null
    },
    // 是否过滤带有权限的数据
    isAuth: {
      type: Boolean,
      default: false
    },
    clearable: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  computed: {
    ...mapGetters(['authOrganTree']),
    id: {
      get() {
        return this.value[this.valueKey]
      },
      set() {

      }
    },
    // 根据城市和区域id，计算一个新的值，用于查询项目接口
    areaCityId() {
      return {
        areaCompanyId: this.areaCompanyId,
        cityCompanyId: this.cityCompanyId
      }
    },
    disabled() {
      let flag = false
      switch (this.valueKey) {
        case 'areaCompanyId': flag = false; break
        case 'cityCompanyId': flag = !this.areaCompanyId; break
        case 'projectId': flag = !this.areaCompanyId || !this.cityCompanyId; break
      }
      return flag
    }
  },
  watch: {
    areaCityId: {
      immediate: true,
      deep: true,
      handler(v) {
        if (!v.areaCompanyId && !v.cityCompanyId) return
        this.getData()
      }
    }
  },
  created() {
    if (this.valueKey === 'areaCompanyId') {
      this.getData()
    }
  },
  methods: {
    ...mapActions(['user/getAuthOrganTree']),
    getData() {
      this.loading = true
      let api = null
      switch (this.valueKey) {
        case 'areaCompanyId': api = getAreaList; break
        case 'cityCompanyId': api = getCityList; break
        case 'projectId': api = getProjectList; break
      }
      return api({ ...this.areaCityId }).then(async res => {
        const arr = res.data || []
        if (this.isAuth) {
          try {
            if (!this.authOrganTree.length) {
              await this['user/getAuthOrganTree']()
            }
            this.options = arr.filter(item => {
              if (this.valueKey === 'cityCompanyId' && this.value.projectId) { // 如果有项目， 则当前的城市没权限也展示
                return this.authOrganTree.findIndex(_item => _item.code === item.code) !== -1 || item.code === this.value.cityCompanyId
              }
              if (this.valueKey === 'areaCompanyId' && this.value.cityCompanyId) { // 如果有城市， 则当前的区域没权限也显示
                return this.authOrganTree.findIndex(_item => _item.code === item.code) !== -1 || item.code === this.value.areaCompanyId
              }
              return this.authOrganTree.findIndex(_item => _item.code === item.code) !== -1
            })
          } catch (error) {
            console.log(error, '获取带有权限的项目树失败')
          }
        } else {
          this.options = arr
        }
      }).finally(() => {
        this.loading = false
      })
    },
    change(value) {
      switch (this.valueKey) {
        case 'areaCompanyId':
          this.$emit('change', {
            areaCompanyId: value,
            cityCompanyId: null,
            projectId: null
          })
          break
        case 'cityCompanyId':
          this.$emit('change', {
            ...this.value,
            cityCompanyId: value,
            projectId: null
          })
          break
        case 'projectId':
          this.$emit('change', {
            ...this.value,
            projectId: value
          })
          break
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
