<template>
  <div ref="tabs" class="u-tabs">
    <div v-for="(item, index) in tabs" :key="item[value]" class="u-tabs__item" :class="{ active: index === currentTabIndex }" :attr-dashed="index < tabs.length - 1 && (currentTabIndex - 1) !== index" @click="tabClick(index)">
      <slot :name="`btn_${index}`" :data="item">
        {{ item[label] }}
        <span v-if="item.count !== null && item.count !== undefined">({{ item.count }})</span>
      </slot>
    </div>
    <div ref="tabBg" class="u-tabs__bg" />
  </div>
</template>

<script>
export default {
  props: {
    tabs: {
      required: true,
      type: Array,
      default: () => []
    },
    label: {
      type: String,
      default: 'label'
    },
    value: {
      type: String,
      default: 'value'
    }
  },
  data() {
    return {
      currentTabIndex: 0
    }
  },
  watch: {
    tabs: {
      immediate: true,
      deep: true,
      handler() {
        this.computedStyle()
      }
    }
  },
  methods: {
    tabClick(index) {
      if (index === this.currentTabIndex) return
      this.currentTabIndex = index
      this.computedStyle()
      this.$nextTick(() => {
        this.$emit('changeTab', this.tabs[index][this.value])
      })
    },
    computedStyle() {
      this.$nextTick(() => {
        const dom = this.$refs.tabs?.querySelectorAll('.u-tabs__item')
        if (!dom) return
        this.$refs.tabBg.style.width = dom[this.currentTabIndex].offsetWidth + 'px'
        this.$refs.tabBg.style.left = dom[this.currentTabIndex].offsetLeft + 'px'
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.u-tabs {
  display: flex;
  line-height: 32px;
  border-radius: 18px;
  position: relative;
  padding: 0 4px;
  &__item {
    color: $--color-text-primary;
    text-align: center;
    font-size: 14px;
    cursor: pointer;
    padding: 0 20px;
    transform: translate(0);
    z-index: 1;
    transition: all linear .3s;
    display: flex;
    align-items: center;
    justify-content: center;
    &[attr-dashed="true"]::after {
      content: '';
      height: 14px;
      width: 0px;
      position: absolute;
      border-left: 1px dashed #D8DCE6;
      right: 0;
      top: 50%;
      transform: translateY(-50%);
    }
    &.active {
      color: #fff;
      &::after {
        visibility: hidden;
      }
    }
  }
  &__bg {
    position: absolute;
    top: 0px;
    left: 0px;
    height: 100%;
    background-color: $--color-primary;
    transition: all linear .3s;
    z-index: 0;
    border-radius: 4px;
  }
}
</style>
