<template>
  <el-upload
    ref="files"
    :disabled="disabled"
    name="file"
    :action="uploadUrl"
    :accept="accept"
    :multiple="multiple"
    :limit="limit"
    :file-list="fileList"
    :auto-upload="autoUpload"
    :before-upload="beforeUpload"
    :on-success="handleSuccess"
    :on-change="handleChange"
    :on-error="handleError"
    :on-remove="handleRemove"
    :on-exceed="handleExceed"
    :headers="{
      Authorization: token,
      PositionId: userInfo.positionId
    }"
    :class="{
      nofile:fileList.length===0
    }"
  >
    <template slot="file" slot-scope="{file:file2}">
      <slot name="file" :file="file2">
        <img
          v-if="file2.status !== 'uploading' && ['picture-card', 'picture'].indexOf(listType) > -1"
          class="el-upload-list__item-thumbnail"
          :src="file2.url"
          alt=""
        >

        <a class="el-upload-list__item-name" @click="handlePreview(file2)">
          <i class="el-icon-document" />{{ file2.fileName || file2.name }}
        </a>
        <label class="el-upload-list__item-status-label">
          <i
            :class="{
              'el-icon-upload-success': true,
              'el-icon-circle-check': listType === 'text',
              'el-icon-check': ['picture-card', 'picture'].indexOf(listType) > -1
            }"
          />
        </label>
        <i v-if="!disabled" class="el-icon-close" @click="handleRemove( file2)" />
        <i v-if="!disabled" class="el-icon-close-tip">{{ t('el.upload.deleteTip') }}</i> <!--因为close按钮只在li:focus的时候 display, li blur后就不存在了，所以键盘导航时永远无法 focus到 close按钮上-->
        <el-progress
          v-if="file2.status === 'uploading'"
          :type="listType === 'picture-card' ? 'circle' : 'line'"
          :stroke-width="listType === 'picture-card' ? 6 : 2"
          :percentage="parsePercentage(file2.percentage)"
        />
        <span v-if="listType === 'picture-card'" class="el-upload-list__item-actions">
          <!--          <span-->
          <!--            v-if="handlePreview && listType === 'picture-card'"-->
          <!--            class="el-upload-list__item-preview"-->
          <!--            @click="handlePreview(file2)"-->
          <!--          >-->
          <!--            <i class="el-icon-zoom-in" />-->
          <!--          </span>-->
          <span
            v-if="!disabled"
            class="el-upload-list__item-delete"
            @click="handleRemove( file2)"
          >
            <i class="el-icon-delete" />
          </span>
        </span>
      </slot>
    </template>

    <div v-if="!disabled" slot="tip" class="el-upload__tip">最多上传{{ limit|filterLimit }}附件，{{ size|filterSize }}</div>
    <slot v-if="!disabled" name="user">
      <div class="m-upload">
        <div class="m-upload__btn">
          <i class="el-icon-upload2" />
          <span>上传附件</span>
        </div>
        <div v-if="!disabled" class="m-upload__tip">{{ tip }}</div>
      </div>
    </slot>
  </el-upload>
</template>

<script>
import { getToken } from '@/utils/auth'
import Locale from 'element-ui/lib/mixins/locale'
import ElProgress from 'element-ui/packages/progress'
import emitter from 'element-ui/lib/mixins/emitter'
import { mapGetters } from 'vuex'
export default {
  name: 'UmUploadFile',
  components: { ElProgress },
  filters: {
    filterLimit(val) {
      if (val === 0) {
        return '不限制附件数量'
      }
      return `${val}个`
    },
    filterSize(val) {
      if (val === 0) {
        return '不限制附件大小'
      }
      if (val < 1000) {
        return `单个附件大小限制${val}KB`
      }
      if (val < 1000 * 1000) {
        return `单个附件大小限制${val / 1024}M`
      }
      return '服务器受不了啊！'
    }
  },
  mixins: [emitter, Locale],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    listType: {
      type: String,
      default: 'text'
    },
    value: {
      type: Array,
      default: null
    },
    multiple: {
      type: Boolean,
      default: true
    },
    limit: {
      type: Number,
      default: 0
    },
    // size 单位kb
    size: {
      type: Number,
      default: 2048
    },
    disabled: {
      type: Boolean,
      default: false
    },
    validateEvent: {
      type: Boolean,
      default: true
    },
    accept: {
      type: String,
      default: '*.*'// .xls,.xlsx
    },
    baseUrl: {
      type: String,
      default: '/api/base/pc/file/upload'
    },
    customSuccess: {
      type: Function,
      default: null
    },
    tip: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      token: getToken(),
      fileList: [], // 文件列表
      source: null // 取消接口方法
    }
  },
  computed: {
    ...mapGetters(['userInfo']),
    uploadUrl() {
      const BASE_URL = process.env.NODE_ENV === 'localdev' ? '/dev-api' : ''
      return BASE_URL + this.baseUrl
    },
    autoUpload() {
      return true
    }
  },
  watch: {
    fileList(val) {
      const value = this.value || []
      if (value.length !== val.length) {
        this.$emit('change', val)
        this.dispatch('ElFormItem', 'el.form.change', val)
      } else {
        if (value.every((item, idx) => {
          return val[idx].url === item.url
        })) {
        } else {
          this.$emit('change', val)
          this.dispatch('ElFormItem', 'el.form.change', val)
          console.log('提交change事件2----', val)
        }
      }
    },
    value: { // 数据格式 {name,url,type}
      handler(val) {
        const value = val || []
        if (value.length !== this.fileList.length) {
          value.forEach(item => {
            if (!item.filePath && item.fileHttpPath) {
              item.filePath = item.fileHttpPath
            }
          })
          this.fileList = value
        } else {
          if (value.every((item, idx) => {
            return this.fileList[idx].url === item.url
          })) {
          } else {
            this.fileList = value
          }
        }
      },
      immediate: true
    }
  },
  created() {},
  mounted() {},
  methods: {
    // 监听文件变化
    handleChange(file, fileList) {
    },
    parsePercentage(val) {
      console.log(val, '==val==')
      return parseInt(val, 10)
    },
    handleClick(file) {
    },
    handlePreview(file) {
      window.open(file.filePath || file.url)
    },
    handleRemove(file) {
      if (file.status === 'uploading') {
        const el = this.$refs.files
        el.abort(file)
        const index = el.uploadFiles.findIndex(item => item.uid === file.uid)
        el.uploadFiles.splice(index, 1)
        return
      }
      this.fileList.forEach((item, index) => {
        if (item.url === file.url) {
          this.fileList.splice(index, 1) // 删除
        }
      })
    },
    beforeUpload(file) {
      if (this.size === 0) {
        return true
      }
      const isSize = file.size / 1024 < this.size
      if (!isSize) {
        this.$message.error(`上传的文件大小不能超过 ${this.size / 1024}mb!`)
      }
      this.customSuccess && this.customSuccess({
        type: 'start'
      })
      return isSize
    },
    handleSuccess(response, file, fileList) {
      if (this.customSuccess) {
        this.customSuccess({
          type: 'success',
          data: response
        })
        return
      }
      if (response.state !== 'success' && response.code !== 3001) {
        this.$message.error('上传失败，请重新上传!')
        // 上传失败之后移除图片
        this.fileList.splice(this.fileList.indexOf(file), 1)
        return false
      }
      this.$set(file, 'url', file.response.data)
      this.$set(file, 'filePath', file.response.data)
      this.$set(file, 'fileName', file.name)
      this.fileList = fileList // 直接赋值
      // 抛出上传成功的列表以及文件
      this.$emit('success', file, fileList)
    },
    handleError(err, file, fileList) {
      console.error(err, file, fileList)
      this.$message.error('上传失败!')
    },
    handleExceed(files, fileList) {
      this.$message.warning(
        `当前限制选择 ${this.limit} 个文件，本次选择了 ${
          files.length
        } 个文件，共选择了 ${files.length + fileList.length} 个文件`
      )
    }
  }
}
</script>
<style lang="scss"  scoped>
::v-deep .el-upload{
  display: block;
}
.el-upload-list__item-name{
  position: relative;
  .el-icon-document{
    width: 20px;
    &:before{
      content: '';
      position: absolute;
      top: 50%;
      left: 0;
      transform: translate(0,-50%);
      width: 14px;
      height: 14px;
      background: url("~@/assets/images/icon-link-fj.png");
      background-size:100% ;

    }
  }
}

.m-upload{
  display: flex;
  color: #999;
  margin-bottom: 10px;
  &__btn {
    height: 32px;
    width: 106px;
    background: #FFFFFF;
    border: 1px dashed rgba(173,179,191,1);
    border-radius: 4px;
    color: #ADB3BF;
    margin-right: 12px;
    display: flex;
    align-items: center;
    justify-content: center;
    &:hover {
      border-color: $--color-primary;
      color: $--color-primary;
    }
  }
}
.nofile ::v-deep .el-upload-list{border: 0}
::v-deep .el-upload-list__item:first-child{
  margin-top: 0;
}
</style>
