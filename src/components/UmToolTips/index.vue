<template>
  <div class="um-tooltip">
    <el-tooltip
      v-if="hasContent"
      :disabled="tooltipDisabled"
      class="item"
      effect="dark"
      :content="content"
      placement="top"
      :open-delay="200"
    >
      <div class="remark" @mouseover="handleMouseOver">
        <p ref="fullText" class="hidden-text">{{ content }}</p>
        <p
          ref="displayText"
          :style="computedStyle"
          :class="rowClass"
          class="f-row-ellipsis"
        >
          {{ content }}
        </p>
      </div>
    </el-tooltip>
    <p v-else>--</p>
  </div>
</template>

<script>
export default {
  name: 'UmToolTips',
  props: {
    content: { // 显示内容
      type: [String, Number],
      default: ''
    },
    row: {
      // 显示行数：1 或 2
      type: [String, Number],
      default: 1
    },
    color: { // 字体颜色
      type: String,
      default: '#666666'
    },
    textDecoration: { // 字体装饰
      type: String,
      default: 'none'
    },
    fontSize: { // 字体大小
      type: String,
      default: '14px'
    }
  },
  data() {
    return {
      tooltipDisabled: false // 是否禁用 Tooltip
    }
  },
  computed: {
    // 判断 content 是否存在
    hasContent() {
      return !!this.content
    },
    // 计算样式
    computedStyle() {
      return {
        fontSize: this.fontSize,
        color: this.color,
        textDecoration: this.textDecoration
      }
    },
    // 计算行数
    rowClass() {
      return `m-row_${this.row}`
    }
  },
  watch: {
    content: {
      handler() {
        this.updateTooltipState()
      },
      immediate: true
    }
  },
  mounted() {
    this.updateTooltipState()
  },
  methods: {
    updateTooltipState() {
      this.$nextTick(() => {
        const fullTextWidth = this.$refs.fullText?.offsetWidth || 0
        const displayTextWidth = this.$refs.displayText?.offsetWidth || 0
        this.tooltipDisabled = displayTextWidth >= fullTextWidth
      })
    },
    handleMouseOver() {
      // 鼠标悬停的逻辑扩展
    }
  }
}
</script>

<style scoped lang="scss">
.um-tooltip {
  .remark {
    position: relative;
    overflow: hidden;
    cursor: pointer;
  }

  .hidden-text {
    opacity: 0;
    position: absolute;
    z-index: -99;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  .f-row-ellipsis {
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  // 一行文本样式
  .m-row_1 {
    width: 100%;
    white-space: nowrap;
    text-overflow: ellipsis;
    overflow: hidden;
  }

  // 两行文本样式
  .m-row_2 {
    display: -webkit-box;
    -webkit-box-orient: vertical;
    -webkit-line-clamp: 2;
    overflow: hidden;
    white-space: normal;
  }
}
</style>
