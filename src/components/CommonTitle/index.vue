<template>
  <div
    class="common-title"
    :class="{'showBg' : showBg }"
    :style="{
      height: parseInt(height) + 'px',
      lineHeight: parseInt(height) + 'px',
      paddingLeft: parseInt(paddingLeft) + 'px',
      marginBottom: parseInt(marginBottom) + 'px'
    }"
  >
    <div class="common-title__title">
      <svg-icon v-if="showIcon" class="icon-header" icon-class="icon-title" />
      <slot name="title">
        <div class="head-title">{{ title }}</div>
      </slot>
      <slot name="status" />
    </div>
    <slot />
  </div>
</template>

<script>
export default {
  name: 'CommonHeader',
  props: {
    height: {
      type: [String, Number],
      default: 56
    },
    paddingLeft: {
      type: [String, Number],
      default: 20
    },
    marginBottom: {
      type: [String, Number],
      default: 20
    },
    title: {
      type: String,
      default: ''
    },
    showIcon: {
      type: Boolean,
      default: true
    },
    showBg: {
      type: Boolean,
      default: false
    }
  }
}
</script>

  <style scoped lang="scss">
  .common-title {
    width: 100%;
    display: flex;
    align-items: center;
    justify-content: space-between;
    position: relative;
    padding-right: 20px;
    &__title {
        display: flex;
        align-items: center;
    }
    .icon-header {
      font-size: 16px;
    }
    .head-title {
      font-size: 16px;
      font-weight: 600;
      color: #333;
    }
  }
  .common-title.showBg {
    background: #EDF1FB;
  }
  </style>

