<template>
  <selectTree
    ref="selectTree"
    v-model="innerValue"
    v-bind="$attrs"
    :options="options"
    :props="config"
    v-on="$listeners"
  />
</template>

<script>
import { kebabCase, isEqual } from 'element-ui/src/utils/util'
import { isDef } from 'element-ui/src/utils/shared'

import selectTree from './index'
const MigratingProps = {
  multiple: {
    newProp: 'multiple',
    type: Boolean
  },
  checkStrictly: {
    newProp: 'checkStrictly',
    type: Boolean
  }
}
export default {
  name: 'ElSelectTree',
  components: { selectTree },
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {},
    options: {
      type: Array,
      default: () => []
    },
    props: Object,
    defaultProps: Object
  },
  data() {
    return {
      innerValue: null // 单选是一维数组，多选是二维数组
    }
  },
  computed: {
    multiple() {
      return this.config.multiple
    },
    config() {
      const config = this.props || {}
      const { $attrs } = this
      Object
        .keys(MigratingProps)
        .forEach(oldProp => {
          const { newProp, type } = MigratingProps[oldProp]
          let oldValue = $attrs[oldProp] || $attrs[kebabCase(oldProp)]
          if (isDef(oldProp) && !isDef(config[newProp])) {
            if (type === Boolean && oldValue === '') {
              oldValue = true
            }
            config[newProp] = oldValue
          }
        })
      return Object.assign({}, config, this.defaultProps)
    }
  },
  watch: {
    value: { // 单选是基本数据类型，多选是一维数组
      handler(val) {
        if (!isEqual(val, this.innerValue)) {
          this.innerValue = val
        }
      },
      immediate: true
    },
    options: {
      handler(val) {
        this.initData()
      },
      immediate: true
    }
  },
  methods: {
    // 获取下拉树的组件
    getSelectTree() {
      return this.$refs.selectTree
    },
    removeEmpty(list) {
      if (!Array.isArray(list)) return false
      list.forEach(item => {
        if (item.children && item.children.length === 0) {
          delete item.children
        } else {
          this.removeEmpty(item.children)
        }
      })
    },
    initData() { // 初始化数据
      const { options } = this
      if (options.length === 0) return false
      // 初始化数据的时候要删除 空children
      this.removeEmpty(options)
    }
  }
}
</script>

<style scoped>

</style>
