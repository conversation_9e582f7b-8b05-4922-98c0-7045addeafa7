<template>
  <el-scrollbar
    tag="ul"
    role="menu"
    class="el-cascader-menu"
    wrap-class="el-cascader-menu__wrap"
    :view-class="{
      'el-cascader-menu__list': true,
      'is-empty': isEmpty
    }"
  >
    <div v-show="isEmpty" class="el-cascader-menu__empty-text">暂无数据</div>
    <el-tree
      v-show="!isEmpty"
      ref="tree"
      :data="menus[0]"
      :show-checkbox="multiple"
      :indent="10"
      :check-strictly="checkStrictly"
      node-key="value"
      :default-expanded-keys="expandedKeys"
      :default-checked-keys="checkedKeys"
      :filter-node-method="filterNode"
      :accordion="accordion"
      :highlight-current="true"
      :expand-on-click-node="expandOnClickNode"
      :check-on-click-node="checkOnClickNode"
      :default-expand-all="expandAll"
      :props="{
        disabled:'isDisabled'
      }"
      @node-click="handleNodeClick"
      @check="handleCheck"
      @check-change="handleCheckChange"
    >
      <span :ref="`node_${data.value}`" slot-scope="{ node, data }" class="custom-tree-node">
        <!--        {{ node.checked }}-->
        <!--        {{ node.isCurrent }}-->
        <!--        {{ node.disabled }}-->
        <span v-if="data.isDisabled" :style="{color:'rgba(0,0,0,0.25)',cursor:'not-allowed'}">{{ node.label }}</span>
        <span
          v-else
          :style="{
            color:node.checked || checkedValue===data.value || checkedValue&&checkedValue[checkedValue.length-1]===data.value?'#2B6FEF':'inherit'
          }"
        >{{ node.label }}</span>
      </span>
    </el-tree>
  </el-scrollbar>
</template>

<script>
import merge from 'element-ui/src/utils/merge'
import scrollIntoView from 'element-ui/src/utils/scroll-into-view'
import Store from './store'
import {
  noop,
  coerceTruthyValueToArray,
  isEqual,
  isEmpty,
  valueEquals
} from 'element-ui/src/utils/util'
const DefaultProps = {
  expandTrigger: 'click', // or hover
  multiple: false,
  checkStrictly: false, // whether all nodes can be selected
  emitPath: false, // wether to emit an array of all levels value in which node is located
  lazy: false,
  lazyLoad: noop,
  value: 'value',
  label: 'label',
  children: 'children',
  leaf: 'leaf',
  disabled: 'disabled',
  hoverThreshold: 500
}

export default {
  name: 'TreePanel',
  provide() {
    return {
      panel: this
    }
  },
  props: {
    value: {},
    options: Array,
    props: Object,
    border: {
      type: Boolean,
      default: true
    },
    dropDownVisible: Boolean, // 是否显示弹框
    renderLabel: Function
  },
  data() {
    return {
      checkedValue: null,
      checkedNodePaths: [],
      store: [],
      menus: [],
      activePath: [],
      loadCount: 0,
      expandedKeys: [], // tree 参数 默认展开的数组
      checkedKeys: [],
      expandAll: false,
      // expandOnClickNode: false,
      checkOnClickNode: false,
      accordion: false // 手风琴
    }
  },
  computed: {
    expandOnClickNode() { // 是否在点击节点的时候展开或者收缩节点， 默认值为 true，如果为 false，则只有点箭头图标的时候才会展开或者收缩节点。
      return !this.checkStrictly
    },
    config() {
      return merge({ ...DefaultProps }, this.props || {})
    },
    multiple() {
      return this.config.multiple
    },
    checkStrictly() {
      return this.config.checkStrictly
    },
    leafOnly() {
      return !this.checkStrictly
    },
    isHoverMenu() {
      return this.config.expandTrigger === 'hover'
    },
    renderLabelFn() {
      return this.renderLabel || this.$scopedSlots.default
    },
    isEmpty() {
      return !this.options.length
    }
  },
  watch: {
    dropDownVisible(val) {
      const { multiple, value, options, config } = this
      if (val) {
        // 处理tree的回显问题，不管数据是否相同
        if (multiple) {
          if (!value || value.length === 0) {
            this.$refs.tree.setCheckedKeys([])
          } else {
            this.$refs.tree.setCheckedKeys(value)
            this.expandedKeys = value
          }
        } else {
          if (isEmpty(value)) {
            this.$refs.tree.setCurrentKey(null)
          } else {
            if (Array.isArray(value)) {
              this.$refs.tree.setCurrentKey(value[value.length - 1])
              this.expandedKeys = value
            } else {
              this.$refs.tree.setCurrentKey(value)
              this.expandedKeys = [value]
            }
          }
        }
        // 如果默认没有展开的。默认展开第一级
        // if (!this.expandedKeys || this.expandedKeys.length === 0) {
        //   let list = []
        //   options.forEach(item => {
        //     if (item.children && item.children.length < 50) {
        //       list = list.concat(item.children.map(item => item[config.value || 'value']))
        //     }
        //   })
        //   this.expandedKeys = list
        // }
        this.$nextTick(this.scrollIntoView)
      }
    },
    options: {
      handler: function() {
        this.initStore()
      },
      immediate: true,
      deep: true
    },
    value() {
      this.syncCheckedValue()
      this.checkStrictly && this.calculateCheckedNodePaths()
    },
    checkedValue(val) {
      if (!isEqual(val, this.value)) {
        this.checkStrictly && this.calculateCheckedNodePaths()
        this.$emit('input', val)
        this.$emit('change', val)
      }
    }
  },
  mounted() {
    if (!isEmpty(this.value)) {
      this.syncCheckedValue()
    }
  },
  methods: {
    initStore() {
      const { config, options } = this

      if (config.lazy && isEmpty(options)) {
        this.lazyLoad()
      } else {
        this.store = new Store(options, config)
        this.menus = [this.store.getNodes()]
        // console.log(this.store)
        // console.log(this.menus)
        this.syncMenuState()
      }
    },
    syncCheckedValue() {
      const { value, checkedValue } = this
      if (!isEqual(value, checkedValue)) {
        this.activePath = []
        this.checkedValue = value
        this.syncMenuState()
      }
    },
    syncMenuState() {
      const { multiple, checkStrictly } = this
      this.syncActivePath()
      multiple && this.syncMultiCheckState()
      checkStrictly && this.calculateCheckedNodePaths()
      // this.$nextTick(this.scrollIntoView);
    },
    syncMultiCheckState() {
      // 同步状态的时候 禁用的 不能被选中，但是只能选择项目中，只是父级不能被选中
      const nodes = this.getFlattedNodes(this.leafOnly)
      nodes.forEach(node => {
        node.syncCheckState(this.checkedValue)
      })
    },
    syncActivePath() {
      const { store, multiple, activePath, checkedValue } = this
      if (!isEmpty(activePath)) {
        const nodes = activePath.map(node => this.getNodeByValue(node.getValue()))
        this.expandNodes(nodes)
      } else if (!isEmpty(checkedValue)) {
        const value = multiple ? checkedValue[0] : checkedValue
        const checkedNode = this.getNodeByValue(value) || {}
        const nodes = (checkedNode.pathNodes || []).slice(0, -1)
        this.expandNodes(nodes)
      } else {
        this.activePath = []
        this.menus = [store.getNodes()]
      }
    },
    expandNodes(nodes) {
      nodes.forEach(node => this.handleExpand(node, true /* silent */))
    },
    calculateCheckedNodePaths() {
      const { checkedValue, multiple } = this
      const checkedValues = multiple
        ? coerceTruthyValueToArray(checkedValue)
        : [checkedValue]
      this.checkedNodePaths = checkedValues.map(v => {
        const checkedNode = this.getNodeByValue(v)
        return checkedNode ? checkedNode.pathNodes : []
      })
    },
    handleExpand(node, silent) {
      const { activePath } = this
      const { level } = node
      const path = activePath.slice(0, level - 1)
      const menus = this.menus.slice(0, level)
      if (!node.isLeaf) {
        path.push(node)
        menus.push(node.children)
      }

      this.activePath = path
      this.menus = menus

      if (!silent) {
        const pathValues = path.map(node => node.getValue())
        const activePathValues = activePath.map(node => node.getValue())
        if (!valueEquals(pathValues, activePathValues)) {
          this.$emit('active-item-change', pathValues) // Deprecated
          this.$emit('expand-change', pathValues)
        }
      }
    },
    lazyLoad(node, onFullfiled) {}, // 异步加载
    filterNode() {}, // 节点过滤
    handleNodeClick(node) {
      const { isLeaf, isDisabled } = node
      // 当节点禁用的时候不可点击
      if (this.multiple || isDisabled) return false
      // 判断是否父子关联/是否只能选择叶子节点  checkStrictly
      if (this.checkStrictly) {
        this.checkedValue = node.getValueByOption()
        console.log(node.getValueByOption())
        this.handleExpand(node)
        // node.doCheck(true)
        this.$emit('close')
      } else {
        if (isLeaf) {
          this.checkedValue = node.getValueByOption()
          this.handleExpand(node)
          // node.doCheck(true)
          this.$emit('close')
        }
      }
      // console.log('点击节点', node)
    },
    handleCheck(node, { checkedKeys, checkedNodes, halfCheckedKeys, halfCheckedNodes }) {
      const { value } = node
      const checked = checkedKeys.includes(value)
      console.log(node, checked)

      node.doCheck(checked)
      this.calculateMultiCheckedValue()
    },
    // 多选
    handleCheckChange(node, checked, leaf) {
      // console.log(node)
      // node.doCheck(checked)
      // this.calculateMultiCheckedValue()
      // this.checkedValue = value
    },
    /**
     * public methods
     */
    calculateMultiCheckedValue() {
      this.checkedValue = this.getCheckedNodes(this.leafOnly)
        .map(node => node.getValueByOption())
    },
    scrollIntoView() {
      if (this.$isServer) return
      const container = this.$el.querySelector('.el-scrollbar__wrap')
      const activeNode = this.$el.querySelector('.el-tree-node.is-current') ||
        this.$el.querySelector('.el-tree-node.is-checked')
      // 修改原有滚动逻辑
      setTimeout(() => {
        // container.scrollTop = activeNode ? activeNode.offsetTop : 0
        scrollIntoView(container, activeNode)
      }, 300)
    },
    getNodeByValue(val) {
      return this.store.getNodeByValue(val)
    },
    getFlattedNodes(leafOnly) {
      const cached = !this.config.lazy
      return this.store.getFlattedNodes(leafOnly, cached)
    },
    getNodes() {
      return this.store.getNodes()
    },
    getCheckedNodes(leafOnly) {
      const { checkedValue, multiple } = this
      if (multiple) {
        const nodes = this.getFlattedNodes(leafOnly)
        return nodes.filter(node => node.checked)
      } else {
        return isEmpty(checkedValue)
          ? []
          : [this.getNodeByValue(checkedValue)]
      }
    },
    clearCheckedNodes() {
      const { config, leafOnly } = this
      const { multiple, emitPath } = config
      if (multiple) {
        this.getCheckedNodes(leafOnly)
          .filter(node => !node.isDisabled)
          .forEach(node => node.doCheck(false))
        this.calculateMultiCheckedValue()
      } else {
        this.checkedValue = emitPath ? [] : null
      }
    }
  }
}
</script>
