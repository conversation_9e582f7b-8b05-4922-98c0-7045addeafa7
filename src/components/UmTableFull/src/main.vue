<script>
export default {
  name: 'UmTableFull',
  props: {
    scroll: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      offsetHeight: null,
      resizeObserver: null,
      el: null
    }
  },
  mounted() {
    // 如果传入高度，则不动态计算高度
    if (this.$attrs.height) {
      this.offsetHeight = parseInt(this.$attrs.height)
      return
    }
    if (this.scroll) {
      this.computedStyle()
    }
    this.resizeObserver = new ResizeObserver(_ => {
      // 修改dom尺寸
      if (this.scroll) {
        this.$el.style.flex = 1
        this.computedStyle()
      }
    })
    this.resizeObserver.observe(this.$el)
  },
  beforeDestroy() {
    !this.$attrs.height && this.resizeObserver.unobserve(this.$el)
  },
  methods: {
    computedStyle() {
      if (this.$el.offsetHeight < 400) {
        this.offsetHeight = 399
        this.$el.style.flex = 'none'
      } else {
        this.offsetHeight = this.$el.offsetHeight
        this.$el.style.flex = 1
      }
    }
  },
  render(createElement, context) {
    const slots = this.$slots
    const attrs = this.$attrs
    const listeners = this.$listeners
    let events = {}
    events = listeners
    this.el = this.$refs.table
    return (
      <el-table ref="table" attrs={attrs} on={events} stripe={attrs.stripe === undefined} height={this.offsetHeight} >
        {slots.default}
        {slots.header}
        <UmEmpty slot="empty" />
      </el-table>
    )
  }
}
</script>
