<template>
  <el-select-tree
    ref="elTree"
    v-model="ids"
    v-loading="loading"
    :disabled="!areaCompanyId && !cityCompanyId"
    v-bind="$attrs"
    :options="options"
    :filterable="true"
    separator="-"
    :check-strictly="checkStrictly"
    :show-all-levels="true"
    :clearable="true"
    :props="{
      emitPath: true,
      label: 'name',
      value: 'code'
    }"
    style="width: 100%;"
    @change="change"
  />
</template>

<script>
// getProjectStage 【用户-数据权限】项目和分期2层层级树
import { getTwoLevelProject, getProjectStage } from '@/api/common'
export default {
  name: 'UmBusProject',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    // 回显的项目id
    value: {
      type: Object,
      default: () => ({})
    },
    // 是否任选一级， 默认可以
    checkStrictly: {
      type: Boolean,
      default: true
    },
    // 是否查询带有数据权限的区域。城市
    isAuth: {
      type: Boolean,
      default: false
    },
    areaCompanyId: {
      type: [String, Number],
      default: null
    },
    cityCompanyId: {
      type: [String, Number],
      default: null
    },
    choosedInfo: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      options: [],
      loading: false
    }
  },
  computed: {
    ids: {
      get() {
        const obj = {
          projectId: this.value.projectId,
          stageId: this.value.stageId
        }
        return Object.values(obj).filter(item => item)
      },
      set() { }
    },
    // 根据城市和区域id，计算一个新的值，用于查询项目接口
    areaCityId() {
      return {
        areaCompanyId: this.areaCompanyId,
        cityCompanyId: this.cityCompanyId
      }
    }
  },
  watch: {
    areaCityId: {
      deep: true,
      handler(v) {
        if (!v.areaCompanyId && !v.cityCompanyId) {
          this.$emit('change', {
            ...this.value,
            projectId: null,
            stageId: null
          })
          this.options = []
          return
        }
        this.getTwoLevelProject()
      }
    }
  },
  methods: {
    getTwoLevelProject(emitChange = true) {
      emitChange && this.$emit('change', {
        ...this.value,
        projectId: null,
        stageId: null
      })
      this.loading = true
      const API = this.isAuth ? getProjectStage : getTwoLevelProject
      return API({ ...this.areaCityId }).then(res => {
        const arr = res.data || []
        if (this.choosedInfo.length) { // 部分场景下，对选中过的进行禁用操作
          this.formatData(arr)
        }
        this.options = arr
      }).finally(() => {
        this.loading = false
      })
    },
    change(value) {
      const params = {
        projectId: value[0],
        stageId: value[1]
      }
      this.$emit('change', {
        ...this.value,
        ...params
      })
    },
    formatData(arr) {
      console.log(this.checkStrictly)
      arr.forEach(item => {
        if (this.checkStrictly) { // 如果任选一级, 父级也要禁用
          item.disabled = this.choosedInfo.includes(item.code)
        }
        if (item.children?.length) {
          item.children.forEach(_item => {
            _item.disabled = this.choosedInfo.includes(_item.code)
          })
        }
      })
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
