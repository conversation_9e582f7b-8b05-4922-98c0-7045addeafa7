<template>
  <el-cascader
    ref="scene"
    v-model="pointId"
    v-bind="$attrs"
    style="width: 100%"
    :placeholder="placeholder"
    clearable
    :options="pointOptions"
    @change="change"
  />
</template>

<script>
import getPointList from '@/mixins/getPointList'
export default {
  name: 'UmBusScene',
  mixins: [getPointList],
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {
      type: [String, Number, Array],
      default: null
    },
    options: {
      type: [Array, Object],
      default: () => []
    },
    placeholder: {
      type: String,
      default: '请选择适用场景'
    },
    needData: { // 是否需要组件内部请求接口数据
      type: Boolean,
      default: true
    },
    // 是否查询全部 默认只查询启用, true 查询全部
    all: {
      type: Boolean,
      default: false
    },
    // 是否需要专项调研
    needSpecial: {
      type: Boolean,
      default: false
    },
    // 是否需要深访调研，默认需要
    needDeep: {
      type: <PERSON><PERSON><PERSON>,
      default: false
    },
    disabledData: { // 需要禁用的数据
      type: Array,
      default: () => []
    },
    ruleCode: {
      type: [Number, String],
      default: undefined
    }
  },
  data() {
    return {
    }
  },
  computed: {
    pointId: {
      get() {
        return this.value
      },
      set() {
      }
    }
  },
  watch: {
    options(v) {
      if (v.length) {
        this.pointOptions = v
        this.checkDisabled()
      }
    },
    disabledData(v) {
      this.checkDisabled()
    }
  },
  async created() {
    if (this.needData) {
      await this.getPointOption(this.all, this.ruleCode)
      this.checkDisabled()
    }
  },
  methods: {
    change(data) {
      this.$emit('change', data[1])
      this.$emit('nodeChange', this.$refs.scene.getCheckedNodes())
    },
    checkDisabled() {
      if (this.disabledData && this.disabledData.length) {
        this.pointOptions.forEach(item => {
          item.children.forEach(_item => {
            _item.disabled = false
            if (this.disabledData.includes(_item.value)) {
              _item.disabled = true
            }
          })
        })
      }
    }
  }
}
</script>

<style lang="scss" scoped>
</style>
