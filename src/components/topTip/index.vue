<template>
  <div class="topTip" :class="'topTip_row_' + row">
    {{ msg }}
    <slot name="sq" />
    <div ref="copy" class="copy">{{ msg }}</div>
    <template v-if="overflow">
      <slot name="overflow" />
    </template>
  </div>
</template>

<script>
export default {
  name: 'TopTip',
  props: {
    row: {
      type: Number,
      default: 1
    },
    msg: {
      type: String,
      default: ''
    }
  },
  data() {
    return {
      overflow: false
    }
  },
  watch: {
    msg(val) {
      if (val) {
        this.$nextTick(() => {
          this.init()
        })
      }
    }
  },
  mounted() {
  },
  methods: {
    init() {
      console.log('init')
      const h1 = this.$el.clientHeight
      const h2 = this.$refs.copy.clientHeight
      console.log(h1, h2)
      if (h2 > h1) {
        this.$emit('overflow', true)
        this.overflow = true
        this.$el.style.paddingRight = 80 + 'px'
      }
    }
  }
}
</script>

<style scoped>
.topTip{
  position: relative;
  word-break: break-all;
}
.topTip_row_1{
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 1;
  overflow: hidden;
  white-space: normal;
}
.topTip_row_2 {

  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 2;
  overflow: hidden;
  white-space: normal;
}

.topTip_row_3 {
  display: -webkit-box;
  -webkit-box-orient: vertical;
  -webkit-line-clamp: 3;
  overflow: hidden;
  white-space: normal;
}
.copy{
  position: absolute;
  left:0;
  top:0;
  width: 100%;
  opacity: 0;
  pointer-events: none;
}
</style>
