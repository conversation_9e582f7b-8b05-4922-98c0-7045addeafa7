<template>
  <div>
    <el-select-tree
      ref="tree"
      v-model="innerValue"
      style="width: 100%"
      :options="pointList"
      :props="config"
      :node-key="config.value"
      clearable
      v-bind="$attrs"
      placeholder="请选择满意度指标"
      v-on="$listeners"
      @change="getVal"
    />
  </div>
</template>
<script>
import { getQuesTargetTree } from '@/api/shortboard'
export default {
  name: 'SatisfactionIndicators',
  model: {
    prop: 'value',
    event: 'change'
  },
  props: {
    value: {},
    props: {}
  },
  data() {
    return {
      pointList: [],
      innerValue: null
    }
  },
  computed: {
    config() {
      return Object.assign({ value: 'quesTargetId', label: 'targetName', children: 'children' }, this.props)
    }
  },
  watch: {
    value: {
      handler(val) {
        this.innerValue = val
      },
      immediate: true
    }
  },
  created() {
    this.getInfo()
  },
  methods: {
    getVal(val) {
      this.$emit('input', val)
      this.$emit('change')
    },
    getInfo() {
      getQuesTargetTree().then(res => {
        this.pointList = res.data
      })
    }
  }
}
</script>
<style lang="scss" scoped></style>
