<template>
  <div class="u-calendar">
    <div class="u-calendar__head">
      <i class="el-icon-arrow-left" @click="changeMonth('prev')" />
      <span>{{ currentDate.year }}年{{ currentDate.month.toString().padStart(2, '0') }}月</span>
      <i class="el-icon-arrow-right" @click="changeMonth('next')" />
    </div>
    <div class="u-calendar__label">
      <span v-for="item in days" :key="item">{{ item }}</span>
    </div>
    <div class="u-calendar__body">
      <div
        v-for="(item, index) in monthDays"
        :key="index"
        class="u-calendar__body--item"
        :class="{'in-ange': item.inRange, 'start-range': item.startRange, 'end-range': item.endRange}"
        @mouseenter="mouseEnter(item, index)"
      >
        <span :class="{disabled: item.disabled, active: item.active }" @click="chooseDate(item, index)">{{ item.date.toString().padStart(2, '0') }}</span>
      </div>
    </div>
  </div>
</template>
<script>
const DATE = new Date()
const DATE_TYPE = {
  BEFORE_MONTH: 0, // 上月
  CURRENT_MONTH: 1, // 本月
  AFTER_MONTH: 2 // 下月
}
export default {
  data() {
    return {
      days: ['日', '一', '二', '三', '四', '五', '六'],
      monthDays: [],
      currentDate: {
        year: DATE.getFullYear(),
        month: DATE.getMonth() + 1
      },
      DATE_TYPE,
      chooseDateRange: []
    }
  },
  computed: {
    // 获取当月第一天是周几
    monthFirstDay() {
      return new Date(this.currentDate.year, this.currentDate.month - 1, 1).getDay()
    }
  },
  created() {
    this.getMonthDays()
  },
  methods: {
    // 获取当月天数
    getMonthDays() {
      const arr = []
      const currentDays = new Date(this.currentDate.year, this.currentDate.month, 0).getDate()
      const beforeDays = new Date(this.currentDate.year, this.currentDate.month - 1, 0).getDate()
      let tempBeforeDays = this.monthFirstDay
      for (let i = 1; i <= 35; i++) {
        if (i <= this.monthFirstDay) { // 上月的数据
          arr.push({
            date: beforeDays - --tempBeforeDays,
            type: DATE_TYPE.BEFORE_MONTH,
            disabled: true,
            inRange: false,
            startRange: false,
            endRange: false
          })
        } else if (i <= currentDays + this.monthFirstDay) { // 本月的数据
          arr.push({
            date: i - this.monthFirstDay,
            type: DATE_TYPE.CURRENT_MONTH,
            disabled: false,
            inRange: false,
            startRange: false,
            endRange: false
          })
        } else { // 下个月的数据
          arr.push({
            date: i - this.monthFirstDay - currentDays,
            type: DATE_TYPE.AFTER_MONTH,
            disabled: true,
            inRange: false,
            startRange: false,
            endRange: false
          })
        }
        arr[i - 1].active = this.checkDateActive(arr[i - 1])
      }
      this.monthDays = arr

      this.monthDays.forEach(item => {
        !item.disabled && this.checkInRange(item, true)
      })
    },
    // 左右切换月份
    changeMonth(type) {
      switch (type) {
        case 'prev':
          if (this.currentDate.month <= 1) {
            this.currentDate.month = 12
            this.currentDate.year--
          } else {
            this.currentDate.month--
          }
          break
        case 'next':
          if (this.currentDate.month >= 12) {
            this.currentDate.month = 1
            this.currentDate.year++
          } else {
            this.currentDate.month++
          }
          break
      }
      this.getMonthDays()
    },
    // 检测是否选中当前日期
    checkDateActive(data) {
      if (data.type !== DATE_TYPE.CURRENT_MONTH) return false
      if (this.chooseDateRange.length) {
        const [firstDate, secondDate] = this.chooseDateRange
        if (firstDate && firstDate.year === this.currentDate.year && firstDate.month === this.currentDate.month && firstDate.date === data.date) {
          return true
        }
        if (secondDate && secondDate.year === this.currentDate.year && secondDate.month === this.currentDate.month && secondDate.date === data.date) {
          return true
        }
      }
      return false
    },
    /**
     * 检测是否处于两个日期之间
     * @param {Object} data 每一天的日期数据
     * @param {bool} changeCheck 是否时初始化数据，比如切换月份了，重新检测
     */
    checkInRange(data, changeCheck = false) {
      if (!changeCheck && (!this.chooseDateRange[0] || this.chooseDateRange.length === 2)) return
      if (changeCheck && this.chooseDateRange.length < 2) return

      const startDate = this.chooseDateRange[0]
      this.monthDays.forEach(item => { item.inRange = item.startRange = item.endRange = false })
      const startTime = this.getDateTime(startDate.year, startDate.month - 1, startDate.date)
      const endDate = changeCheck ? this.chooseDateRange[1] : this.currentDate
      const endTime = this.getDateTime(endDate.year, endDate.month - 1, changeCheck ? endDate.date : data.date)

      if (startTime > endTime) { // 如果开始时间大于结束时间，即选择的第一个日期大于当前鼠标滑动到的日期
        if (startDate.year === this.currentDate.year && startDate.month === this.currentDate.month) {
          this.monthDays[startDate.index].startRange = false
          this.monthDays[startDate.index].endRange = true
        }
        this.monthDays.forEach((item, _index) => {
          const itemTime = !item.disabled && this.getDateTime(this.currentDate.year, this.currentDate.month - 1, item.date)
          if (itemTime >= endTime && itemTime <= startTime) {
            item.inRange = true
          }
        })
      } else {
        if (startDate.year === this.currentDate.year && startDate.month === this.currentDate.month) {
          this.monthDays[startDate.index].startRange = true
          this.monthDays[startDate.index].endRange = false
        }
        if (endDate.year === this.currentDate.year && endDate.month === this.currentDate.month && this.monthDays[endDate.index]) { // 结束日期的年月和当前日期相同时才操作
          this.monthDays[endDate.index].startRange = false
          this.monthDays[endDate.index].endRange = true
        }
        this.monthDays.forEach((item, _index) => {
          const itemTime = !item.disabled && this.getDateTime(this.currentDate.year, this.currentDate.month - 1, item.date)
          if (itemTime >= startTime && itemTime <= endTime) {
            item.inRange = true
          }
        })
      }
    },
    // 鼠标移动到日期上面
    mouseEnter(data) {
      if (data.disabled) return
      this.checkInRange(data, false)
    },
    // 选择日期
    chooseDate(data, index) {
      if (data.disabled) return
      if (this.chooseDateRange.length === 2) {
        this.initData()
      }
      this.monthDays[index].active = true
      if (!this.chooseDateRange[0]) { // 如果第一个都没有，则没有开始日期
        this.chooseDateRange[0] = {
          year: this.currentDate.year,
          month: this.currentDate.month,
          date: data.date,
          index
        }
        this.monthDays[index].startRange = true // 设置为选择范围的开始节点
        this.monthDays[index].endRange = false
      } else if (!this.chooseDateRange[1]) {
        const curTime = this.getDateTime(this.currentDate.year, this.currentDate.month, data.date)
        const { year, month, date } = this.chooseDateRange[0]
        const beforeTime = this.getDateTime(year, month, date)
        if (curTime < beforeTime) { // 比较后选的日期跟已经选择的日期，如果小于，则表示是开始日期
          this.chooseDateRange[1] = {
            year, month, date, index: this.chooseDateRange[0].index
          }
          if (year === this.currentDate.year && month === this.currentDate.month) { // 如果已经选择的和当前的年月相同，才修改，避免修改不同月份下同一天的数据状态
            this.monthDays[this.chooseDateRange[0].index].startRange = false
            this.monthDays[this.chooseDateRange[0].index].endRange = true
          }
          this.chooseDateRange[0] = {
            year: this.currentDate.year,
            month: this.currentDate.month,
            date: data.date,
            index
          }
          this.monthDays[index].startRange = true
          this.monthDays[index].endRange = false
        } else if (curTime === beforeTime) { // 如果相等，等开始和结束都是一个范围
          this.monthDays[index].startRange = true
          this.monthDays[index].endRange = true
          this.chooseDateRange[1] = {
            year: this.currentDate.year,
            month: this.currentDate.month,
            date: data.date,
            index
          }
        } else { // 否则就是正常的结束日期在第一个选择的日期之后
          this.chooseDateRange[1] = {
            year: this.currentDate.year,
            month: this.currentDate.month,
            date: data.date,
            index
          }
          this.monthDays[index].startRange = false
          this.monthDays[index].endRange = true
        }
      }
    },
    // 获取日期对应的时间戳
    getDateTime(year, month, date) {
      return new Date(year, month, date).getTime()
    },
    initData() {
      this.chooseDateRange = []
      this.monthDays.forEach(item => {
        item.active = item.inRange = item.startRange = item.endRange = false
      })
    },
    // 格式化对外的数据
    formatDate() {
      if (this.chooseDateRange.length < 2) return false
      const [startDate, endDate] = this.chooseDateRange
      return {
        startTime: startDate.year + '-' + startDate.month.toString().padStart(2, '0') + '-' + startDate.date.toString().padStart(2, '0'),
        endTime: endDate.year + '-' + endDate.month.toString().padStart(2, '0') + '-' + endDate.date.toString().padStart(2, '0')
      }
    }
  }
}
</script>
<style lang="scss" scoped>
.u-calendar {
    width: 294px;
    padding: 10px;
    box-sizing: content-box;
    user-select: none;
    &__head {
        display: flex;
        justify-content: space-between;
        align-items: center;
        padding: 0 10px;
        > i {
            cursor: pointer;
            &:hover {
                color: $--color-primary;
            }
        }
    }
    &__label {
        display: flex;
        text-align: center;
        span {
            width: 40px;
            color: $--color-info;
            margin-top: 20px;
            margin-bottom: 5px;
        }
    }
    &__body {
        display: flex;
        flex-wrap: wrap;
        &--item {
            width: 40px;
            height: 30px;
            padding: 4px 0;
            display: flex;
            align-items: center;
            justify-content: center;
            margin-bottom: 6px;
            &.in-ange, &.start-range.end-range {
                background-color: rgba($--color-primary, 0.1);
            }
            &.start-range {
                border-top-left-radius: 50%;
                border-bottom-left-radius: 50%;
            }
            &.end-range {
                border-top-right-radius: 50%;
                border-bottom-right-radius: 50%;
            }
            > span {
                width: 24px;
                height: 24px;
                border-radius: 50%;
                text-align: center;
                line-height: 24px;
                cursor: pointer;
                &.disabled {
                    color: $--color-text-tip;
                    cursor: not-allowed;
                }
                &.active {
                    background-color: $--color-primary;
                    color: #fff;
                }
                &:hover:not(.disabled):not(.active) {
                    color: $--color-primary;
                }
            }
        }
    }
}
</style>
