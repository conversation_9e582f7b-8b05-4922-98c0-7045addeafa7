<template>
  <div class="um-inline_edit" @mouseenter="mouseHandler">
    <div
      :id="'UmInlineEdit_'+_uid"
      class="mce-content-body"
      :contentEditable="!readonly"
      :class="{'um-inline_edit--error': showError, 'um-inline_edit--readonly': readonly }"
      v-html="value"
    />
    <div v-if="showError" class="error-tip">内容长度不可超过{{ maxLength }}个字符！</div>
  </div>
</template>

<script>
const tinymce = window.tinymce

const dynamicData = ['楼盘名称', '楼栋-房号', '姓名', '业务标识', '业务时间']
export default {
  name: 'UmInlineEdit',
  props: {
    value: {
      type: String,
      default: ''
    },
    isTitle: {
      type: Boolean,
      default: false
    },
    readonly: {
      type: Boolean,
      default: false
    },
    maxLength: {
      type: Number,
      default: 200
    }
  },
  data() {
    return {
      tinymce: null,
      showError: false,
      status: 0
    }
  },
  created() {
  },
  mounted() {
  },
  methods: {
    mouseHandler() {
      if (this.status || this.readonly) return false
      this.status = 1
      this.init()
    },
    init() {
      // https://www.tiny.cloud/docs-4x/demo/inline/
      tinymce.init({
        selector: `#UmInlineEdit_${this._uid}`,
        menubar: false,
        inline: true,
        plugins: ['textcolor'],
        fontsize_formats: '12px 14px 16px 18px 20px 22px 24px 26px 28px 36px 48px 72px',
        toolbar: 'fontsizeselect bold forecolor underline btn1 btn2 btn3 btn4 btn5',
        init_instance_callback: editor => {
          editor.on('blur', this.getEditorContent)
        },
        setup: (editor) => {
          if (!this.isTitle) return
          dynamicData.forEach((item, index) => {
            editor.addButton('btn' + ++index, {
              text: '#' + item,
              onclick: function() {
                editor.insertContent(` #${item} `)
              }
            })
          })
        }
      }).then(res => {
        this.tinymce = res[0]
      })
    },
    getEditorContent() {
      const text = this.tinymce.getContent({ 'format': 'text' }).trim()
      if (text.length > this.maxLength) {
        this.showError = true
        return
      } else {
        this.showError = false
      }
      const html = this.tinymce.getContent()
      this.$emit('input', html)
    }
  }
}
</script>
<style lang="scss">
.um-inline_edit{
  *[contentEditable="true"]{
    padding: 8px 12px;
    word-break:break-all;
  }
  *[contentEditable="true"]:hover {
    outline: 1px dashed #ADB3BF;
    border-radius: 4px;
  }
  *[contentEditable="true"]:focus{
    outline:none;
    background: rgba(173,179,191,0.3);
    border-radius: 4px;
  }
  &--error {
    border: 1px dashed #FF6161;
    outline: none!important;
    border-radius: 4px;
  }
  &--readonly {
    padding: 8px 12px;
  }
  .error-tip {
    color: #FF6161;
    font-size: 12px;
    text-align: left;
  }
}
.mce-content-body{
  line-height: 1.3;
}
</style>
