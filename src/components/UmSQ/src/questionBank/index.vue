<template>
  <div v-loading="loading" class="g-container">
    <UmConfigTree
      show-menu
      :tree-data="treeData"
      :default-props="{
        children: 'categoryChildren',
        label: 'name',
        value: 'quesQuestionCategoryId'
      }"
      @node-click="appendQuestion"
    >
      <template slot="menu" slot-scope="{data}">
        <el-popover
          v-if="!data.isQuestion"
          placement="right-start"
          popper-class="custom-tree-popover"
          width="104"
          :visible-arrow="false"
          trigger="hover"
        >
          <div class="u-tree__menu">
            <div v-if="data.rootDepth < 2" @click.stop="addCate(data)"><i class="el-icon-circle-plus-outline" />添加</div>
            <div @click.stop="editCate(data)"><i class="el-icon-edit-outline" />编辑</div>
            <div v-if="data.rootDepth > 0" @click.stop="remove(data)"><i class="el-icon-delete" />删除</div>
          </div>
          <svg-icon slot="reference" class="tree-more" icon-class="more" />
        </el-popover>
        <i v-else class="el-icon-delete danger" @click.stop="remove(data)" />
      </template>
    </UmConfigTree>
    <el-dialog
      :visible.sync="dialogVisible"
      destory-on-close
      :close-on-click-modal="false"
      :title="`${mode === 'edit' ? '编辑' : '新增'}题库分类`"
      append-top-body
      width="400px"
      @close="$refs.editForm.resetFields();"
    >
      <el-form ref="editForm" :model="editForm" label-width="80px" label-suffix="：">
        <el-form-item label="分类名称" prop="name" :rules="{ required: true, message: '请输入分类名称' }">
          <el-input v-model="editForm.name" placeholder="请输入分类名称" maxlength="20" clearable />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { getCategoryTree, addCategory, editCategory, deleteCategory, deleteQuestionBank } from '@/api/wenjuan'
export default {
  data() {
    return {
      treeData: [], // 处理后的，包含题目信息
      dialogVisible: false,
      editForm: {
        quesQuestionCategoryId: null,
        name: null
      },
      mode: 'add',
      testData: {
        'xh': null,
        'question_id': '38b40683-1beb-4f08-a3be-217aedc7a16b',
        'title': '<p>请选择一个选项</p>',
        'required': true,
        'type': 1,
        'option_list': [{ 'title': '<p>选项1</p>', 'option_id': 'c9a83cd4-4e09-421d-b380-96965bf757c6', 'score': 1 }, { 'title': '<p>选项2</p>', 'option_id': 'b08e5d93-a545-4cd5-9724-78a942b71386', 'score': 1 }],
        'kpi': { 'id': null, 'type': 1, 'score': null },
        'idx': 1
      },
      loading: false
    }
  },
  created() {
    if (this.treeData.length) return
    this.getTreeData()
  },
  methods: {
    getTreeData() {
      this.loading = true
      getCategoryTree().then(res => {
        const arr = res.data || []
        this.formatData(arr)
        this.treeData = arr
      }).finally(() => { this.loading = false })
    },
    // 将题目数据放到目录层级中，加isQuestion标识
    formatData(arr) {
      arr.forEach(item => {
        if (item.questionBankList?.length) {
          item.categoryChildren.push(...item.questionBankList.map(_item => ({
            ..._item,
            name: _item.questionName,
            quesQuestionCategoryId: _item.quesQuestionBankId,
            isQuestion: true
          })))
        }
        if (item.categoryChildren?.length) {
          this.formatData(item.categoryChildren)
        }
      })
    },
    remove(data) {
      this.$confirm(data.isQuestion ? `确定删除该题目：${data.name} ？` : `确定删除该题库分类：${data.name} ？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        const API = data.isQuestion ? deleteQuestionBank : deleteCategory
        API(data.isQuestion ? { quesQuestionBankId: data.quesQuestionBankId } : { quesQuestionCategoryId: data.quesQuestionCategoryId }).then(res => {
          this.$message.success(res.msg)
          this.getTreeData()
        }).finally(() => load.close())
      })
    },
    // 新增分类
    addCate(data) {
      this.mode = 'add'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.editForm.quesQuestionCategoryId = data.quesQuestionCategoryId
      })
    },
    // 编辑分类
    editCate(data) {
      this.mode = 'edit'
      this.dialogVisible = true
      this.$nextTick(() => {
        this.editForm.name = data.name
        this.editForm.quesQuestionCategoryId = data.quesQuestionCategoryId
      })
    },
    confirm() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          const API = this.mode === 'edit' ? editCategory : addCategory
          const load = this.$load()
          API({ ...this.editForm }).then(res => {
            this.$message.success(res.msg)
            this.dialogVisible = false
            this.getTreeData()
          }).finally(() => load.close())
        }
      })
    },
    // 将问题添加到中间编辑器中
    appendQuestion(data, path) {
      if (!data.isQuestion) return
      this.$emit('success', JSON.parse(data.content))
    }
  }
}
</script>

<style lang="scss" scoped>
.g-container {
  flex: 1;
  overflow-y: auto;
  ::v-deep {
    .el-tree-node.is-current  {
      > .el-tree-node__content {
        background-color: #F5F7FA;
      }
    }
  }
  .c-node {
    display: flex;
    justify-content: space-between;
    align-items: center;
    flex: 1;
    overflow: hidden;
    height: 100%;
    &__label {
      flex: 1;
      overflow: hidden;
      white-space: nowrap;
      text-overflow: ellipsis;
    }
    &__operate {
      height: 32px;
      font-weight: 400;
      line-height: 32px;
    }
  }
  .m-icon {
    color: $--color-primary;
    margin-left: 5px;
    font-size: 14px;
    &.el-icon-delete {
      color: #FF6161;
    }
  }
}
</style>

<style lang="scss">
.poper-question__menu {
  padding: 0;
  width: 120px;
  min-width: 120px;
  margin-left: 2px!important;
  .m-question__menu {
    display: flex;
    flex-direction: column;
    > div {
      line-height: 28px;
      padding: 0 10px;
      cursor: pointer;
      i {
        margin-right: 10px;
      }
      &:hover {
        background-color: #F5F7FA;
        color: $--color-primary;
      }
    }
  }
}
</style>
