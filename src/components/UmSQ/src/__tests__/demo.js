import { Config } from '../core/factory'
const demo = new Config({
  'version': '2.0',
  'config': { 'title': '', 'desc': '', 'footer': '', 'hasIndex': false, 'pageMode': 0 },
  'pages': [
    {
      'question_id': '1', // 单选题1
      'type': 1,
      'option_list': [
        { 'option_id': '11' }, // 选项一
        { 'option_id': '12' }, // 选项二
      ]
    },
    {
      'question_id': '2', // 多选题2
      'type': 2,
      'option_list': [
        { 'option_id': '21' }, // 选项一
        { 'option_id': '22' }, // 选项二
      ]
    },
    { 'question_id': '3', 'type': 3 }, // 填空题3
    {
      'question_id': '4', // 多选题4
      'type': 2,
      'option_list': [
        { 'option_id': '41' }, // 非常不满意
        { 'option_id': '42' }, // 不满意
        { 'option_id': '43' }, // 一般
        { 'option_id': '44' }, // 满意
        { 'option_id': '45' }, // 非常满意
      ]
    },
    {
      'question_id': '5', // 多选题5
      'type': 2,
      'option_list': [
        { 'option_id': '51' }, // 非常不满意
        { 'option_id': '52' }, // 不满意
        { 'option_id': '53' }, // 一般
        { 'option_id': '54' }, // 满意
        { 'option_id': '55' }, // 非常满意
      ]
    },
    { 'question_id': '6', 'type': 5 }, // 文本题6
    { 'question_id': '7', 'type': 3 }, // 填空题7
    { 'question_id': '8', 'type': 3 }, // 填空题8
  ],
  'conditionConfig': []
})
// describe('测试条件输出结果', () => {
//   it('单选题"1"选择"选项一" 那么隐藏7,8, "多选题第二题选择第一项,跳转第五题', () => {
//     demo.conditionConfig = [
//       {
//         'condition_id': '1',
//         'question_id': '1',
//         'type': 1,
//         'question_value': ['11'],
//         'target': 3,
//         'target_question_id': ['7', '8'],
//         'status': 1
//       },
//       {
//         'condition_id': '2',
//         'question_id': '2',
//         'type': 1,
//         'question_value': ['21'],
//         'target': 1,
//         'target_question_id': ['5'],
//         'status': 1
//       }
//     ]
//     // 当用户什么都没选
//     demo.answers = {
//       '1': ['11'],
//       '2': ['21']
//     }
//     let pages = demo.getAllQuestion()
//     let xhs = pages.sortList.map(i => i.xh)
//     expect(xhs).toEqual([1,2,3, null])
//   })
// })

describe('测试条件输出结果', () => {
    it('单选题"1"选择选项一，那么显示2,4', () => { // 2,4 默认进来不显示
    demo.conditionConfig = [
      {
        'condition_id': '1',
        'question_id': '1',
        'type': 1,
        'question_value': ['21'],
        'target': 2, // 显示
        'target_question_id': ['2', '4'],
        'status': 1
      }
    ]
    // 当用户什么都没选
    demo.answers = {
      '1': ['21'],
      '1': []
    }
    let pages = demo.getAllQuestion()
    let xhs = pages.sortList.map(i => i.question_id)
    expect(xhs).toEqual(['1','3','5','6','7','8']) // 7，8默认不显示
  })
})
