import { htmlToText } from '../utils'

describe('测试公共方法', () => {
  it('测试 html转换text', () => {
    const html = '<p>这是一个<span>测试</span></p>'
    const result = htmlToText(html)
    expect(result).toBe('这是一个测试')
  })
})

describe('Jest 常见断言场景', () => {
  // tobe
  expect(1 + 1).toBe(2)
  // not
  expect(1 + 1).not.toBe(3)
  // boolean
  expect(true).toBeTruthy()
  expect(false).toBeFalsy()
  // undefined
  expect(undefined).not.toBeDefined()
  expect(undefined).toBeUndefined()
  // 浮点数
  expect(0.2 + 0.1).toBeCloseTo(0.3)

  // 引用类型的比较	toEqual(value)
  // >
  expect(3).toBeGreaterThan(2)
  // <
  expect(3).toBeLessThan(4)
  // >=
  expect(3).toBeGreaterThanOrEqual(3)
  expect(3).toBeGreaterThanOrEqual(2)
  // <=
  expect(3).toBeLessThanOrEqual(3)
  expect(3).toBeLessThanOrEqual(4)
  // 正则
  expect('This is a regexp validation').toMatch(/regexp/)
  const obj = { prop1: 'test', prop2: 'regexp validation' }
  const childObj = { prop1: 'test' }
  expect(obj).toMatchObject(childObj)
  /*
  toContain(value) ：判定某个值是否存在在数组中。
  arrayContaining(value)：匹配接收到的数组，与 toEqual 结合使用可以用于判定某个数组是否是另一个数组的子集。
  toContainEqual(value) ：用于判定某个对象元素是否在数组中。
  toHaveLength(value)：断言数组的长度 。
  toHaveProperty(value)：断言对象中是否包含某个属性，针对多层级的对象可以通过 xx.yy 的方式进行传参断言。
  */
  // 错误抛出	toThrow() toThrowError()
})
