import { Config } from '../core/factory'
// 初始化数据
const demo = new Config({
  'version': '1.0',
  'config': { 'title': '', 'desc': '', 'footer': '', 'hasIndex': false, 'pageMode': 0 },
  'pages': [
    { 'question_id': '2b0c9dce-eba3-496f-91f2-271ad0b78ee4', 'type': 3 }, // 填空题1
    { 'question_id': '2b0c9dce-eba3-496f-91f2-271ad0b78ee0', 'type': 5 }, // 文本1-1
    {
      'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22', // 单选题2
      'type': 1,
      'option_list': [
        { 'option_id': 'b471bd08-8647-488e-9137-8a2bf9c52617' }, // 6点
        { 'option_id': 'ec501ad0-e199-466f-9b88-a3b729d4593d' }, // 7点
        { 'option_id': '1c4dbdf8-726e-4e77-99c9-5b0482ee3b25' } // 8点
      ]
    },
    {
      'question_id': '304f335a-4eb3-42d1-b60d-c4e8e45621fa', // 多选题3
      'type': 2,
      'option_list': [
        { 'option_id': 'fc9720aa-b3ce-4336-9c3f-49f96ba656e2' }, // 前端
        { 'option_id': 'c9fe90f7-cbb5-48e0-bc60-d512f570a121' }, // 后端
        { 'option_id': '43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea' } // UI
      ]
    },
    { 'question_id': '30f0132b-076e-4251-9082-7fb8e4d5f110', 'type': 3 }, // 填空题4
    {
      'question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59', // 评分题5
      'type': 4,
      'option_list': [
        { 'option_id': '58e2b93e-d030-4b3e-9378-cf8f00832acd' }, // 非常不满意
        { 'option_id': '1186ef44-422f-4d10-98fe-8ec9c489c8d1' }, // 不满意
        { 'option_id': '741203cf-fd86-4e16-adba-091b930648fc' } // 一般
      ]
    },
    { 'question_id': '67ae2c23-121d-4a8b-b9cc-7a1171613e0d', 'type': 5 } // 文本题6
  ],
  'conditionConfig': []
})

describe('测试公共方法', () => {
  it('Jest 常见断言场景', () => {
    expect(1 + 1).toBe(2)
  })
})

it('测试序号', () => {
  demo.conditionConfig = [
    { // 单选题 选择 6点 则 评分题5 显示 否则不显示
      'condition_id': '1b925129-8c5c-4dd9-b06b-2d1ff6e4c06a',
      'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
      'type': 1,
      'question_value': ['b471bd08-8647-488e-9137-8a2bf9c52617'],
      'target': 2,
      'target_question_id': ['cc5eafbe-7148-4d14-8c75-8b4cbb701f59'],
      'status': 1
    },
    { // 多选题选择了UI 则 评分题5 隐藏 否则 显示
      'condition_id': 'f19b6cab-203f-4036-b84c-c659d32b040a',
      'question_id': '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
      'type': 1,
      'question_value': [
        '43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea'
      ],
      'target': 3,
      'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
      'status': 1
    }
  ]
  // 当用户什么都没选
  demo.answers = {
    '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea']
  }
  const { sortList, status } = demo.getAllQuestion()
  const xhs = sortList.map(i => i.xh)
  expect(xhs).toEqual([1, null, 2, 3, 4, null])
  expect(status).toBeNull()
})

