import { Config } from '../core/factory'
import { htmlToText } from '@/components/UmSQ/src/utils'
// 初始化数据
const demo = new Config({
  'version': '1.0',
  'config': { 'title': '', 'desc': '', 'footer': '', 'hasIndex': false, 'pageMode': 0 },
  'pages': [
    { 'question_id': '2b0c9dce-eba3-496f-91f2-271ad0b78ee4', 'type': 3 }, // 填空题1
    {
      'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22', // 单选题2
      'type': 1,
      'option_list': [
        { 'option_id': 'b471bd08-8647-488e-9137-8a2bf9c52617' }, // 6点
        { 'option_id': 'ec501ad0-e199-466f-9b88-a3b729d4593d' }, // 7点
        { 'option_id': '1c4dbdf8-726e-4e77-99c9-5b0482ee3b25' } // 8点
      ]
    },
    {
      'question_id': '304f335a-4eb3-42d1-b60d-c4e8e45621fa', // 多选题3
      'type': 2,
      'option_list': [
        { 'option_id': 'fc9720aa-b3ce-4336-9c3f-49f96ba656e2' }, // 前端
        { 'option_id': 'c9fe90f7-cbb5-48e0-bc60-d512f570a121' }, // 后端
        { 'option_id': '43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea' } // UI
      ]
    },
    { 'question_id': '30f0132b-076e-4251-9082-7fb8e4d5f110', 'type': 3 }, // 填空题4
    {
      'question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59', // 评分题5
      'type': 4,
      'option_list': [
        { 'option_id': '58e2b93e-d030-4b3e-9378-cf8f00832acd' }, // 非常不满意
        { 'option_id': '1186ef44-422f-4d10-98fe-8ec9c489c8d1' }, // 不满意
        { 'option_id': '741203cf-fd86-4e16-adba-091b930648fc' } // 一般
      ]
    },
    { 'question_id': '67ae2c23-121d-4a8b-b9cc-7a1171613e0d', 'type': 5 } // 文本题6
  ],
  'conditionConfig': []
})

describe('测试公共方法', () => {
  it('Jest 常见断言场景', () => {
    expect(1 + 1).toBe(2)
  })
})

describe('测试条件输出结果', () => {
  it('单选题"2"选择"6点" 那么跳转评分5', () => {
    demo.conditionConfig = [
      {
        'condition_id': '8f5e0685-1ffa-47f7-be73-7ffe402c2b07',
        'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
        'type': 1,
        'question_value': ['b471bd08-8647-488e-9137-8a2bf9c52617'],
        'target': 1,
        'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
        'status': 1
      }
    ]
    // 当用户什么都没选
    demo.answers = {}
    let pages = demo.getAllQuestion()
    let questions = pages.sortList.map(i => i.question_id)
    expect(questions).toEqual([
      '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
      '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
      '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
      '30f0132b-076e-4251-9082-7fb8e4d5f110',
      'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
      '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
    ])
    // 当用户选择了其他值
    demo.answers = {
      '148bc831-d0ce-44d2-82c4-43c5f6f47b22': '123'
    }
    pages = demo.getAllQuestion()
    questions = pages.sortList.map(i => i.question_id)
    expect(questions).toEqual([
      '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
      '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
      '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
      '30f0132b-076e-4251-9082-7fb8e4d5f110',
      'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
      '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
    ])
    // 当用户选择了条件
    demo.answers = {
      '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617'
    }
    pages = demo.getAllQuestion()
    const nextQa = demo.getNextQuestion('148bc831-d0ce-44d2-82c4-43c5f6f47b22')
    questions = pages.sortList.map(i => i.question_id)
    expect(questions).toEqual([
      '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
      '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
      'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
      '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
    ])
    expect(nextQa.question_id).toBe('cc5eafbe-7148-4d14-8c75-8b4cbb701f59')
  })
})

// describe('测试条件输出结果', () => {
//
//   it('单选题"2"选择"6点" 那么隐藏"文本题6"，多选题"3"选中"前端、后端"跳转"题目5"', () => {
//     demo.conditionConfig = [
//       {
//         'condition_id': '75ea0004-7901-43f3-b898-34f6af68e60e',
//         'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//         'type': 1,
//         'question_value': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//         'target': 3,
//         'target_question_id': '67ae2c23-121d-4a8b-b9cc-7a1171613e0d',
//         'status': 1
//       },
//       {
//         'condition_id': '283b2f1b-0f0e-4c28-bc35-940be2756c01',
//         'question_id': '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//         'type': 1,
//         'question_value': [
//           'fc9720aa-b3ce-4336-9c3f-49f96ba656e2',
//           'c9fe90f7-cbb5-48e0-bc60-d512f570a121'
//         ],
//         'target': 1,
//         'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//         'status': 1
//       }
//     ]
//     // 当用户什么都没选
//     demo.answers = {}
//     let pages = demo.getAllQuestion()
//     let questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//     // 当用户选择了其他值
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': '123' // 第二题
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//     // 当用户选择了条件
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617'
//     }
//     pages = demo.getAllQuestion()
//     const nextQa = demo.getNextQuestion('148bc831-d0ce-44d2-82c4-43c5f6f47b22')
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59'
//     ])
//     expect(nextQa.question_id).toBe('304f335a-4eb3-42d1-b60d-c4e8e45621fa') // 下一题应该是第三题
//
//     // 当用户选择了条件
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['fc9720aa-b3ce-4336-9c3f-49f96ba656e2', 'c9fe90f7-cbb5-48e0-bc60-d512f570a121']
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59'
//     ])
//
//     // 当用户选择了条件
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['fc9720aa-b3ce-4336-9c3f-49f96ba656e666', 'c9fe90f7-cbb5-48e0-bc60-d512f570a121']
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59'
//     ])
//
//     // 当用户选择了子集
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['c9fe90f7-cbb5-48e0-bc60-d512f570a121']
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59'
//     ])
//   })
//   it('单选题"2"选择"6点" 那么显示"打分题5"，多选题"3"选中"UI"则隐藏"题目5"', () => {
//     demo.conditionConfig = [
//       {
//         'condition_id': '1b925129-8c5c-4dd9-b06b-2d1ff6e4c06a',
//         'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//         'type': 1,
//         'question_value': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//         'target': 2,
//         'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//         'status': 1
//       },
//       {
//         'condition_id': 'f19b6cab-203f-4036-b84c-c659d32b040a',
//         'question_id': '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//         'type': 1,
//         'question_value': [
//           '43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea'
//         ],
//         'target': 3,
//         'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//         'status': 1
//       }
//     ]
//     // 当用户什么都没选
//     demo.answers = {}
//     let pages = demo.getAllQuestion()
//     let questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//     // 当用户选择了条件
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['43803d9b-fb5a-4cd4-88fb-fc0a1ca682ea']
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//
//     // 当用户选择了条件
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa': ['43803d9b-fb5a-4cd4-88fb-fc0a1ca682999']
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//
//     // '单选题"2" 没有选择"6点" 那么隐藏"打分题5"
//     demo.answers = {
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22': 'b471bd08-8647-488e-9137-8a2bf9c52617999'
//     }
//     pages = demo.getAllQuestion()
//     questions = pages.map(i => i.question_id)
//     expect(questions).toEqual([
//       '2b0c9dce-eba3-496f-91f2-271ad0b78ee4',
//       '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
//       '304f335a-4eb3-42d1-b60d-c4e8e45621fa',
//       '30f0132b-076e-4251-9082-7fb8e4d5f110',
//       '67ae2c23-121d-4a8b-b9cc-7a1171613e0d'
//     ])
//   })
// })
//
