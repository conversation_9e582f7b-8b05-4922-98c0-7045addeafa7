import { Config } from '../core/factory'
const demo = new Config({
  'version': '2.0',
  'config': { 'title': '', 'desc': '', 'footer': '', 'hasIndex': false, 'pageMode': 0 },
  'pages': [
    {
      'question_id': "0a77cab6-c18d-440a-bb42-fba31b2bcdf1", // 单选一
      'type': 1,
      'option_list': [
        { 'option_id': "0a77cab6-c18d-440a-bb42-fba31b2bcdf1" }, // 选项一
        { 'option_id': "2b1dd362-1e8e-45a6-9a56-bbd489e96b45" }, // 选项二
      ]
    },
    {
      'question_id': "3ea2df54-e6b7-4dc7-b5b7-000a5a251426", // 多选二
      'type': 2,
      'option_list': [
        { 'option_id': "e0a4cd63-0de7-446b-b10f-f0f4a65f9892" }, // 选项一
        { 'option_id': "3ea2df54-e6b7-4dc7-b5b7-000a5a251429" }, // 选项二
      ]
    }, // 填空题3
    {
      'question_id': "408da706-c913-4706-89b2-51aadb903448", // 多选三
      'type': 2,
      'option_list': [
        { 'option_id': "3818f9bb-2044-4bf2-8b3e-fb99d4511312" }, // 选项一
        { 'option_id': "7951a685-aefc-441b-809c-e3efa2a8dfba" }, // 选项二
      ]
    },
    {
      'question_id': "ed7bf8a8-3532-42f1-8625-f0534bc565b0", // 文本题四
      'type': 3
    },
    // 打分5
    { 'question_id': "3078fe51-fe6a-401c-b166-8f96a2785061",
      'type': 4,
      'option_list': [
        { 'option_id': "ee300f8e-5578-4385-8528-589c06c8d806" },
        { 'option_id': "a9004e77-cb62-4238-90e4-78e23bbc9100" },
        { 'option_id': "ad965218-68e7-41ff-8ee6-e66d88846348" },
        { 'option_id': "ecf52f39-693e-4386-9c1e-42e9df342667" },
        { 'option_id': "cdf28afc-4492-40fe-b9c9-19282aa3e126" },
      ]
    },
    // 单选6
    {
      'question_id': "9968f411-dc51-4037-a75e-0a2db336d78e",
      'type': 1,
      'option_list': [
        { 'option_id': "e0b3b560-6745-444e-97b2-c25dad92b205" },
        { 'option_id': "f936226d-2e93-4797-9809-9f355916592c" },
      ]
    },
    // 多选7
    { 'question_id': "8a4d2cd7-6ff6-4696-ab04-d8077c63f689",
      'type': 2,
      'option_list': [
        { 'option_id': "596753b5-9d7d-4438-884f-cde72e6590c0" },
        { 'option_id': "052efada-8677-4807-bb22-220a2e3bebb3" },
      ]
    },
  ],
  'conditionConfig': []
})
// describe('测试条件输出结果', () => {
//   it('单选题"1"选择"选项一" 那么隐藏7,8, "多选题第二题选择第一项,跳转第五题', () => {
//     demo.conditionConfig = [
//       {
//         'condition_id': '1',
//         'question_id': '1',
//         'type': 1,
//         'question_value': ['11'],
//         'target': 3,
//         'target_question_id': ['7', '8'],
//         'status': 1
//       },
//       {
//         'condition_id': '2',
//         'question_id': '2',
//         'type': 1,
//         'question_value': ['21'],
//         'target': 1,
//         'target_question_id': ['5'],
//         'status': 1
//       }
//     ]
//     // 当用户什么都没选
//     demo.answers = {
//       '1': ['11'],
//       '2': ['21']
//     }
//     let pages = demo.getAllQuestion()
//     let xhs = pages.sortList.map(i => i.xh)
//     expect(xhs).toEqual([1,2,3, null])
//   })
// })

describe('测试条件输出结果', () => {
  it('第一题选择了房屋装修精装隐藏单选一和多选三和多选7,内置一题,总共7题, 隐藏三个剩四个',  () => {
    demo.conditionConfig = [
      {
        'condition_id': 'decorationType',
        'question_id': 'decorationType',
        'type': 1,
        'question_value': [2200031],
        'target': 3,
        'target_question_id': ['0a77cab6-c18d-440a-bb42-fba31b2bcdf1', '408da706-c913-4706-89b2-51aadb903448', '8a4d2cd7-6ff6-4696-ab04-d8077c63f689'],
        'status': 1
      },
      {
        'condition_id': '1',
        'question_id': '408da706-c913-4706-89b2-51aadb903448',
        'type': 1,
        'question_value': ['3818f9bb-2044-4bf2-8b3e-fb99d4511312'],
        'target': 1,
        'target_question_id': ['9968f411-dc51-4037-a75e-0a2db336d78e'],
        'status': 1
      }
    ]
    // 当用户什么都没选
    demo.answers = {
      'decorationType': [2200031],
    }
    let pages = demo.getAllQuestion()
    let xhs = pages.sortList.map(i => i.question_id)
    //  单选1,填空3,
    expect(xhs).toEqual([1,3,4])
  })
})
