// const reg = /<[^>]+>/gi // 匹配所有的html标签。但不包括html标签内的内容
// const reg2 = /<(?!img).*?>/gi // 匹配除img标签外的html标签  不包括html标签内的内容
// const reg3 = /<(?!img|p|\/p).*?>/gi // 匹配除img、p标签外的html标签  不包括html标签内的内容
// const reg4 = /<(img|br|hr|input)[^>]*>/gi // 只匹配img、br、hr、input标签
// const reg1 = /<(div|\/div).*?>/gi // 匹配所有的div标签。不包括div标签内的内容
/**
 * html转text方法
 * @param html 需要解析的html
 * @returns {string} 去除标签之后的text代码
 */
export const htmlToText = (html) => {
  if (typeof html !== 'string') {
    return ''
  }
  return html.replace(/<[^>]+>/gi, '')
}
/**
 * 判断用户的回答和条件是否一致
 * @param answer {String | Array} 用户回答的值
 * @param question_value {Array} 逻辑条件变成，只能是数组
 * @param target {Number} 1 跳转 2 显示 3 隐藏
 * @returns {*|boolean}
 */
export const isContainAnswer = (answer, question_value, target) => {
  if (typeof answer === 'string') { // 单选题
    return question_value.includes(answer)
  }
  if (Array.isArray(answer) && answer.length) { // 多选题
    return answer.every(val => question_value.includes(val))
  }

  return false
}
