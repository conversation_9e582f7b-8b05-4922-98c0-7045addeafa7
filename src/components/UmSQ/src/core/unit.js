// 定义组件

export const QUESTION_TYPE = {
  RADIO: 1,
  CHECKBOX: 2,
  INPUT: 3,
  GRADE: 4,
  TEXT: 5,
  UPLOAD: 6
}
// 对应后端定义的题目类型
export const QUESTION_TYPE_CODE = {
  [QUESTION_TYPE.RADIO]: 2200081,
  [QUESTION_TYPE.CHECKBOX]: 2200082,
  [QUESTION_TYPE.GRADE]: 2200083,
  [QUESTION_TYPE.INPUT]: 2200084,
  [QUESTION_TYPE.TEXT]: 2200085,
  [QUESTION_TYPE.UPLOAD]: 2200086
}

export const MAX_QUESTION_LENGTH = 100 // 限制最大的题目数量
export const MAX_OPTIONS_LENGTH = 150 // 限制最大的选项数量
export const SCORE_LIST = [1, 2, 3, 4, 5] // 分数枚举

export const CONDIONS = [ // 不满意条件
  {
    label: '小于',
    value: 1
  },
  {
    label: '小于等于',
    value: 2
  }
]

export const SATISFIED_CONDIONS = [ // 不满意条件
  {
    label: '大于',
    value: 1
  },
  {
    label: '大于等于',
    value: 2
  }
]

export const ENUM_GRADE_ICON = {
  STAR: 'e995', // 星星
  POINT: 'e642', // 大拇指
  HEART: 'e688' // 爱心
}

// 选项类型（题目勾选启用拒答之后，加入不知道和拒绝回答选项，问卷模块不展示，主要用于答题端）
export const OPTION_TYPE = {
  NORMAL: 2200121, // 普通
  UNKNOW: 2200122, // 不知道
  REFUSE: 2200123, // 拒答
  OTHER: 2200124 // 其他
}

export const GRADE_ICON = [ // 样式选择下拉
  {
    iconUnicode: ENUM_GRADE_ICON.STAR
  },
  {
    iconUnicode: ENUM_GRADE_ICON.POINT
  },
  {
    iconUnicode: ENUM_GRADE_ICON.HEART
  }
]
export const INPUT_SIZE = { // 输入框大小
  SMALL: 2200361,
  NORMAL: 2200362,
  BIG: 2200363
}
// 问卷动态信息
export const dynamicData = {
  '#楼盘名称': 2200101,
  '#楼栋-房号': 2200103,
  '#姓名': 2200102,
  '#业务标识': 2200104,
  '#业务时间': 2200105
}

export default [
  {
    name: '单选题',
    type: QUESTION_TYPE.RADIO
  }, {
    name: '多选题',
    type: QUESTION_TYPE.CHECKBOX
  }, {
    name: '填空题',
    type: QUESTION_TYPE.INPUT
  }, {
    name: '打分题',
    type: QUESTION_TYPE.GRADE
  }, {
    name: '文本描述',
    type: QUESTION_TYPE.TEXT
  }, {
    name: '附件题',
    type: QUESTION_TYPE.UPLOAD
  }
]
