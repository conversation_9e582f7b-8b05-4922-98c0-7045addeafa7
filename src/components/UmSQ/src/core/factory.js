import { v4 as uuidv4 } from 'uuid'
import _ from 'lodash'
import { ENUM_GRADE_ICON, QUESTION_TYPE, MAX_OPTIONS_LENGTH, INPUT_SIZE, OPTION_TYPE } from './unit'
import { isContainAnswer } from '../utils'
import { Message } from 'element-ui'
/**
 * 基础定义
 */
class Base {
  xh = null // 序号
  question_id = null // 组件唯一uuid
  title = null
  targetName = null // 编辑时，题目所关联的指标是否被删除
  required = true // 默认 true
  type = null // 类型：1单选 2 多选 3填空 4 打分 5文本描述  6 附件题 渲染对应的组件使用
  constructor({ question_id, title = '', type, required = true, xh = null, targetName = null }) {
    this.question_id = question_id || uuidv4()
    this.title = title || null
    this.type = type
    this.required = required
    this.xh = xh
    this.targetName = targetName
  }

  // 添加一个选项, 可以自定义分值，用于拒答和不知道
  // optionType 选项类型 区分是否启用拒答的
  addOption(text, optionType = OPTION_TYPE.NORMAL) {
    if (this.option_list) {
      const params = {
        title: typeof text === 'string' ? text : `<p>选项${this.option_list.length + 1}</p>`,
        option_id: uuidv4(),
        // 普通题目初始1分，不知道8分，拒答9分
        score: optionType === OPTION_TYPE.NORMAL ? 1 : (optionType === OPTION_TYPE.REFUSE ? 9 : 8),
        optionType
      }
      const otherIndex = this.option_list.findIndex(item => item.optionType === OPTION_TYPE.OTHER)
      // 确保其他项一直在末尾
      if (otherIndex !== -1) {
        this.option_list.splice(otherIndex, 0, params)
      } else {
        this.option_list.push(params)
      }
    }
  }

  // 添加其他项
  addOtherOption(oneOhter = true) {
    if (this.option_list) {
      if (this.option_list.findIndex(item => item.optionType === OPTION_TYPE.OTHER) !== -1 && oneOhter) {
        Message.warning('当前题目已经存在“其他”选项！')
        return
      }
      const params = {
        title: '其他',
        option_id: uuidv4(),
        score: 1,
        optionType: OPTION_TYPE.OTHER
      }
      this.option_list.push(params)
    }
  }

  // 批量添加方法
  batAddOption(text, optionType = OPTION_TYPE.NORMAL) {
    return new Promise((resolve, reject) => {
      if (typeof text === 'string') {
        const list = text.split('\n')
        if (list.some(item => item.length > 200)) {
          return reject('每项内容长度不可超过200个字符！')
        }
        if (list.length + this.option_list.length > MAX_OPTIONS_LENGTH) {
          reject(`最多添加${MAX_OPTIONS_LENGTH}个选项！`)
          return
        }
        list.forEach(_text => {
          this.addOption(_text, optionType)
        })
        resolve()
      } else {
        reject('参数类型错误')
      }
    })
  }

  /**
   * 编辑是否拒答选项
   * @param {Boolean} isRefuse
   */
  editRefuseOption(isRefuse) {
    //  && this.option_list.findIndex(item => item.optionType === OPTION_TYPE.REFUSE || item.optionType === OPTION_TYPE.UNKNOW) === -1
    if (isRefuse) {
      this.addOption('拒答', OPTION_TYPE.REFUSE)
      this.addOption('不知道', OPTION_TYPE.UNKNOW)
    } else {
      const index1 = this.option_list.findIndex(item => item.optionType === OPTION_TYPE.REFUSE)
      index1 !== -1 && this.option_list.splice(index1, 1)
      const index2 = this.option_list.findIndex(item => item.optionType === OPTION_TYPE.UNKNOW)
      index2 !== -1 && this.option_list.splice(index2, 1)
    }
  }
}

/**
 * 单选题
 */
export class RadioEle extends Base {
  option_list=[]
  kpi={
    id: null,
    type: 1, // 不满意比较规则 1 小于 2小于等于
    score: null, // 不满意比较的分值
    satisfiedType: 1, // 满意比较规则  1大于 2大于等于
    satisfiedScore: null // 满意比较分值
  }

  isExamine = true // 计入考核
  isCheck = false // 反向抽查
  isRefuse = false // 是否拒答
  constructor(params = null) {
    super({
      title: '<p>请选择一个选项</p>',
      type: 1,
      ...params
    })
    if (params) {
      this.kpi = params.kpi
      console.log(params.option_list)
      this.option_list = params.option_list
      this.isExamine = params.isExamine
      this.isCheck = params.isCheck
      this.isRefuse = params.isRefuse
    } else {
      this.batAddOption('<p>选项1</p>\n<p>选项2</p>')
    }
  }
}

/**
 * 多选题
 */
export class CheckBoxEle extends Base {
  option_list=[]
  maxSelectCount = 0 // 最多选择的选项数量，0不限制
  painFlag = false // 是否设为痛点问题
  optionGroup = [] // 选项分组
  groupOptionIds = [] // 被分组的选项id
  // kpi={
  //   id: null,
  //   type: 1, // 比较规则 1 小于 2小于等于
  //   score: null // 比较的分值
  // }

  constructor(params = null) {
    super({
      title: '<p>请选择以下选项 (多选)</p>',
      type: 2,
      ...params
    })
    if (params) {
      // this.kpi = params.kpi
      this.option_list = params.option_list
      this.maxSelectCount = params.maxSelectCount || 0
      this.painFlag = params.painFlag || false
      this.optionGroup = params.optionGroup || []
      this.groupOptionIds = params.groupOptionIds || []
    } else {
      this.batAddOption('<p>选项1</p>\n<p>选项2</p>')
    }
  }

  delGroupOption(groupIndex, optionIndex) {
    this.optionGroup[groupIndex]?.children.splice(optionIndex, 1)
  }

  delOption(option_id) {
    const index = this.option_list.findIndex(item => item.option_id === option_id)
    this.option_list.splice(index, 1)
  }
}

/**
 * 输入框
 */
export class InputEle extends Base {
  placeholder='请输入' // 提示文字
  size = INPUT_SIZE.SMALL // 文本框大小  1 2 3 //
  constructor(params = null) {
    super({
      title: '<p>请填写本项内容</p>',
      type: 3,
      ...params
    })
    if (params) {
      this.placeholder = params.placeholder
      this.size = params.size
    }
  }
}

/**
 * 评分题
 */
export class GradeEle extends Base {
  option_list = [
    { title: '非常不满意', option_id: uuidv4(), score: 1, optionType: OPTION_TYPE.NORMAL },
    { title: '比较不满意', option_id: uuidv4(), score: 2, optionType: OPTION_TYPE.NORMAL },
    { title: '一般', option_id: uuidv4(), score: 3, optionType: OPTION_TYPE.NORMAL },
    { title: '比较满意', option_id: uuidv4(), score: 4, optionType: OPTION_TYPE.NORMAL },
    { title: '非常满意', option_id: uuidv4(), score: 5, optionType: OPTION_TYPE.NORMAL }
  ] // {score:(1/2/3/4/5) ,option_id:uuid,title:备注（非常满意、满意、不满意）}

  isExamine = true // 计入考核
  isCheck = false // 反向抽查
  isRefuse = false // 是否拒答
  style = ENUM_GRADE_ICON.STAR // 样式展现形式 1 2 3
  kpi={
    id: null,
    type: 1, // 不满意比较规则 1 小于 2小于等于
    score: null, // 不满意比较的分值
    satisfiedType: 1, // 满意比较规则  1大于 2大于等于
    satisfiedScore: null // 满意比较分值
  }

  constructor(params = null) {
    super({
      title: '<p>请您对我们的服务进行评价</p>',
      type: 4,
      ...params
    })
    if (params) {
      this.option_list = params.option_list
      this.style = params.style
      this.kpi = params.kpi
      this.isExamine = params.isExamine
      this.isCheck = params.isCheck
      this.isRefuse = params.isRefuse
    }
  }

  // 更新选项
  updateOption(option_id, score) {
    const item = this.option_list.find(i => i.option_id === option_id)
    if (item) {
      item.score = score
    }
  }

  // 更新选项根据下标
  updateOptionByIndex(index, score) {
    const item = this.option_list[index]
    if (item) {
      item.score = score
    }
  }
}

/**
 * 文本描述 不能当作题目
 */
export class TextEle extends Base {
  align = 'left' // 对齐方式 left center right
  content = '<p>请阅读本项说明，然后回答问题</p>' // 内容
  constructor(params = null) {
    super({
      type: 5,
      ...params,
      required: false
    })
    if (params) {
      this.content = params.content
      this.align = params.align
    }
  }
}
/**
 * 附件题
 */
export class UploadEle extends Base {
  placeholder='请输入' // 提示文字
  fileList = []
  limit = 20
  fileRequired = true // 附件是否必填
  prMode = false // 点赞 true  吐槽  false
  constructor(params = null) {
    super({
      title: '<p>请填写本项内容</p>',
      type: 6,
      ...params
    })
    if (params) {
      this.placeholder = params.placeholder
      this.fileList = params.fileList
      this.limit = params.limit
      this.fileRequired = params.fileRequired
      this.prMode = params.prMode || false
    }
  }
}

/**
 * 公共配置
 */
export class Config {
  version = '1.0' // 版本
  config = {
    title: '<p>问卷标题</p>', // 内容
    desc: '<p>感谢您能抽出几分钟时间来参加本次答题，现在我们就马上开始吧！</p>',
    footer: '<p>您已完成本次问卷，感谢您的帮助与支持</p>', // 正常完成
    earlyFooter: '<p>您已完成本次问卷，感谢您的帮助与支持</p>', // 提前结束
    hasIndex: true, // 题目序号
    pageMode: 0, // 0 否 1 是 一页一题
    completeMode: 0, // 0 正常结束 1 提前结束
    painPointInfo: null // 痛点问题信息
  }

  pages = [ // 测试逻辑
    // new RadioEle(),
    // new CheckBoxEle(),
    // new InputEle(),
    // new CheckBoxEle(),
    // new GradeEle(),
    // new TextEle(),
    // new InputEle()
  ] // 题目配置

  conditionConfig = [] // 逻辑设置
  answers = {} // 用户回答的答案 { uuid:value }
  disagree = {} // 用户选择的不满意原因
  created=null // 创建时间
  updated=null // 更新时间
  constructor(params = null) {
    if (params) {
      this.editInit(params)
    }
    this.created = new Date()
    // this.addCondition(1)
    // this.addCondition(2)
    // this.addCondition(3)
    // demo测试

    // this.conditionConfig = [
    //   {
    //     'condition_id': '2821389a-80c8-4c62-a52e-0ac7e82b1e6a',
    //     'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
    //     'type': 1,
    //     'question_value': 'b471bd08-8647-488e-9137-8a2bf9c52617',
    //     'target': 1,
    //     'target_question_id': '30f0132b-076e-4251-9082-7fb8e4d5f110',
    //     'status': 1
    //   },
    //   {
    //     'condition_id': '83ea1447-9148-4bb6-851d-3b5acf60c6e9',
    //     'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
    //     'type': 1,
    //     'question_value': 'ec501ad0-e199-466f-9b88-a3b729d4593d',
    //     'target': 2,
    //     'target_question_id': 'cc5eafbe-7148-4d14-8c75-8b4cbb701f59',
    //     'status': 1
    //   },
    //   {
    //     'condition_id': '6fe74874-c31d-4336-8a15-e751ccb093bb',
    //     'question_id': '148bc831-d0ce-44d2-82c4-43c5f6f47b22',
    //     'type': 1,
    //     'question_value': '1c4dbdf8-726e-4e77-99c9-5b0482ee3b25',
    //     'target': 1,
    //     'target_question_id': '67ae2c23-121d-4a8b-b9cc-7a1171613e0d',
    //     'status': 1
    //   }
    // ]
  }

  // 如果是编辑，
  editInit({ conditionConfig, config, created, pages, updated, version }) {
    this.conditionConfig = conditionConfig
    this.config = config
    this.version = version
    this.created = created
    this.updated = updated
    this.pages = pages.map(item => {
      switch (item.type) {
        case QUESTION_TYPE.RADIO: return new RadioEle(item)
        case QUESTION_TYPE.CHECKBOX: return new CheckBoxEle(item)
        case QUESTION_TYPE.TEXT: return new TextEle(item)
        case QUESTION_TYPE.GRADE: return new GradeEle(item)
        case QUESTION_TYPE.INPUT: return new InputEle(item)
        case QUESTION_TYPE.UPLOAD: return new UploadEle(item)
      }
    })
  }

  /**
   * 问卷保存方法
   * @returns {string} 问卷序列化之后的结果
   */
  saveQuestion() {
    console.log(this, '你好')
    const result = JSON.parse(JSON.stringify(this))
    result.updated = new Date()
    const pagesIds = this.pages.map(i => i.question_id)
    const conditionConfig = result.conditionConfig.sort((cond, nextCond) => {
      const condIndex = pagesIds.findIndex(i => i === cond.question_id)
      const nextCondIndex = pagesIds.findIndex(i => i === nextCond.question_id)
      console.log(condIndex, nextCondIndex)
      return condIndex - nextCondIndex
    })

    this.sortPages() // 对题目 增加序号～
    // this.conditionConfig = conditionConfig // 为了测试排序效果
    return {
      ...result,
      conditionConfig
    }
    // return JSON.stringify({
    //   ...result,
    //   conditionConfig
    // })
  }

  /**
   * 获取有效的逻辑条件
   */
  getConditionEffect() {
    const conditionConfig = this.conditionConfig
    return conditionConfig.filter(item => {
      return item.status === 1 && item.question_id && !_.isEmpty(item.target_question_id)
    })
  }

  /**
   * 对逻辑条件进行校验
   */
  conditionValidate() {
    const conditionConfig = this.conditionConfig
    const map = {}
    conditionConfig.forEach(item => {
      // 单选逻辑 A => A
      // 多选逻辑 [A,B,C]  => [A,B] [A] [A,D]
      // 多选不允许有交集
      const question_value = item.question_value
      if (Array.isArray(question_value)) {
        if (question_value.length) {
          const has = question_value.some(i => map[item.question_id + '-' + i])
          if (has) {
            item.status = 0 // 标记为失败
          } else {
            item.status = 1 // 标记为校验通过
          }
          question_value.forEach(i => {
            map[item.question_id + '-' + i] = true
          })
        } else {
          item.status = 1 // 标记为校验通过
        }
      }
    })
  }

  /**
   * 对问卷进行排序，不包含文本描述的排序
   */
  sortPages(pages) {
    if (!pages) {
      pages = this.pages
    }
    let xh = 1
    pages.forEach(item => {
      if (item.type !== 5) {
        item.xh = xh++
      } else {
        item.xh = null
      }
    })
    return pages
  }

  /**
   * 逻辑设置：添加逻辑
   */
  addCondition(target = '') {
    this.conditionConfig.push({
      condition_id: uuidv4(),
      question_id: '',
      type: 1,
      question_value: '', // 多选是数组  null []
      target,
      target_question_id: '', // 单选是字符串，多选是数组
      status: 1
    })
  }

  /**
   * 题目：是否可以移动（因为前台是出错后，重新排序，并不是使用sort的方法，导致此处取反）
   * @param current 目标题目
   * @param target 当前拖拽的题目
   * @returns {boolean}
   */
  async checkMove(current, target) { // todo
    const current_index = this.pages.findIndex(i => i.question_id === current)
    const target_index = this.pages.findIndex(i => i.question_id === target)
    if (current_index > target_index) {
      // 认为是从下往上拖动
      // 查询当前题目的所有 前置题目，因为结果不能高于条件
      const list = this.conditionConfig.filter(i => i.target_question_id.includes(target)).map(i => i.question_id)
      const index = list.map(i => this.pages.findIndex(_i => _i.question_id === i))
      const max = Math.max.apply(Math, index)
      if (target_index <= max) {
        throw new Error('该题已设置为逻辑结果，不可移动到逻辑条件题目之前！')
      }
    }
    if (current_index < target_index) {
      // 认为是从上往下拖动
      // 查询当前题目的所有 结果题目，因为结果不能高于条件

      const list = this.conditionConfig.filter(i => i.question_id === target).map(i => i.target_question_id)
      const index = list.map(i => this.pages.findIndex(_i => i.includes(_i.question_id)))
      const min = Math.min.apply(Math, index)
      if (target_index >= min) {
        throw new Error('该题已设置为逻辑结果，不可移动到逻辑条件题目之前！')
      }
    }
  }

  /**
   * 题目：删除题目
   * @param question_id 题目ID
   * @returns {boolean}
   */
  async checkDelete(question_id) {
    const item = this.conditionConfig.find(i => {
      // return i.question_id === question_id || i.target_question_id === question_id || (Array.isArray(i.target_question_id) && i.target_question_id.includes(question_id))
      return i.question_id === question_id || i.target_question_id.includes(question_id) // 字符串和数组都有 includes 方法
    })
    if (item) {
      throw new Error('该题已参与逻辑设置，不能删除！')
    }
  }

  /**
   * 题目：删除选项
   * @param question_id 题目ID
   * @param option_id 选项ID
   * @returns {boolean}
   */
  async checkDeleteOption(question_id, option_id) {
    // console.log(this.conditionConfig, 'this.conditionConfig')
    const list = this.conditionConfig.filter(i => i.question_id === question_id)
    for (let i = 0; i < list.length; i++) {
      const item = list[i]
      if (item.question_value && option_id && item.question_value.includes(option_id)) {
        throw new Error('该选项已参与逻辑设置，不能删除！')
      }
    }
  }

  // 根据逻辑条件和已经回答的题目，返回下一道题
  getNextQuestion(question_id) { // 项目并未使用，且下一题 和 隐藏跳转有一些问题
    const newPages = this.getAllQuestion()
    const item = newPages.sortList.find(i => i.question_id === question_id)
    return item && item.$next
  }

  // 根据逻辑条件和已经回答的题目，返回整个试卷
  getAllQuestion() {
    // 1、处理内置条件（项目是否有会所、是否华发操盘物业、房屋装修类型）
    // 2、处理除内置的其他条件逻辑
    // 2.1、一个题目选择多个条件 跳转一个题目 （跳转逻辑）
    // 2.2、一个题目选择多个条件 显示隐藏 多个题目 （显示隐藏逻辑）
    // 3、提前结束
    const answers = this.answers // 用户已经回答的题目 内置条件也需要放置到 answers 里面， answers.hasClub = 1
    const tmpPages = this.pages // 试卷所有题目

    const tmpConfig = this.conditionConfig // 试卷的逻辑设置 拆解成 内置条件 手动条件
    const internalIds = internalCondition.map(i => i.question_id)
    const internalConditionConfig = tmpConfig.filter(i => internalIds.includes(i.question_id)) // 内置条件
    /* 第一步：处理内置条件 按照条件的顺序判断 */
    internalConditionConfig.forEach(condition => {
      const { question_id, target, target_question_id, question_value } = condition // target_question_id:Array question_value:Array
      const answer = answers[question_id]
      const targetItem = tmpPages.filter(i => target_question_id.includes(i.question_id))
      // 显示逻辑
      if (target === 2) {
        if (isContainAnswer(answer, question_value, target)) {
          targetItem.forEach(i => { i.show = 1 })
        } else {
          targetItem.forEach(i => { i.show = 0 })
        }
      }
      // 隐藏逻辑
      if (target === 3) {
        if (isContainAnswer(answer, question_value, target)) {
          targetItem.forEach(i => { i.show = 1 })
        } else {
          targetItem.forEach(i => { i.show = 0 })
        }
      }
    })

    // 过滤内置条件之后的数据
    const pages = tmpPages.filter(i => i.show !== 0)
    const conditionConfig = tmpConfig.filter(i => !internalIds.includes(i.question_id)) // 内置条件

    const conditionConfigMap = {} // 试卷逻辑的map对象 { uuid :[] }
    /* 第二步：处理非内置条件 按照题目的顺序判断 */
    let allPages = [] // 返回新的可以回答的试卷
    const allPagesMap = {} // 新试卷的map对象
    conditionConfig.forEach(item => {
      const isEmpty = _.isEmpty(item.question_value)
      // 条件题目存在。条件题目值不为空或者空数组 目标题目不能为空 条件验证为1
      if (item.question_id && !isEmpty && !_.isEmpty(item.target_question_id) && item.status === 1) {
        if (conditionConfigMap[item.question_id]) {
          conditionConfigMap[item.question_id].push(item)
        } else {
          conditionConfigMap[item.question_id] = [item]
        }
      }
    })
    // 默认用户可以回答所有题目
    allPages = pages.map((ite, idx) => {
      const item = new NewQuestion({
        question_id: ite.question_id,
        show: 1,
        target_question: pages[idx + 1] && pages[idx + 1].question_id,
        _self: ite
      })
      allPagesMap[item.question_id] = item
      return item
    })
    // const conditionTargets = conditionConfig.filter(item => item.target === 2) // 显示的条件
    // const conditionTargets1 = conditionTargets.map(item => item.target_question_id).flat() // 显示的题目id集合
    // const uniqueArray =  Array.from(new Set(conditionTargets1)) // 去重
    // allPages.forEach(page => {
    //   if (uniqueArray.includes(page.question_id)) {
    //     page.show = 0
    //   }
    // })
    allPages.reduce((prev, current) => {
      current.$prev = prev
      if (prev) prev.$next = current
      return current
    }, null)
    // --------- 上述题目，尚未参与条件运算 ----------

    // 开始逻辑处理
    const newPages = []
    let xh = 1
    let current = allPages[0]
    let status = null // 问卷状态  null 正常问卷结束 AbortEnd 提前结束 NormalEnd 正常结束
    // console.log('-------------分割线-------------')
    while (current) {
      const condition = conditionConfigMap[current.question_id] // 查找当前题目影响的条件
      const answer = answers[current.question_id] // 获取用户回答的值
      const isEmpty = _.isEmpty(answer) // 判断用户回答的是不是空

      if (condition) {
        condition.forEach(item => {
          const { question_id, target, target_question_id, question_value } = item
          // 跳转逻辑
          if (target === 1 && isContainAnswer(answer, question_value, target) && !isEmpty) {
            current.$next = allPagesMap[target_question_id] // 提前结束的题目不存在，所以返回的是undefined
            if (target_question_id === 'AbortEnd' || target_question_id === 'NormalEnd') {
              status = target_question_id
            }
          } else {
            // 无需处理，默认就是下一题
          }

          // 显示逻辑
          if (target === 2) {
            const targetItem = allPages.filter(i => target_question_id.includes(i.question_id))
            if (isContainAnswer(answer, question_value, target)) {
              targetItem.forEach(i => { i.show = 1 })
            } else {
              targetItem.forEach(i => { i.show = 0 })
            }
          }

          // 隐藏逻辑
          if (target === 3) {
            const targetItem = allPages.filter(i => target_question_id.includes(i.question_id))
            if (isContainAnswer(answer, question_value, target)) {
              targetItem.forEach(i => { i.show = 0 })
            } else {
              targetItem.forEach(i => { i.show = 1 })
            }
          }
        })
      }
      newPages.push(current)
      if (current._self.type !== 5) {
        current._self.xh = xh++
      }
      // 执行下一题
      current = current.$next
    }
    const resultList = newPages.filter(i => i.show)
    const sortList = this.sortPages(resultList)
    return {
      sortList,
      status
    } // 过滤只会显示的题目
    // target 1 跳转 或 正常进入下一题
    // target 2 显示 或 不显示
    // target 3 隐藏 或 显示
  }
}

class NewQuestion {
  question_id = '' // 试卷的uuid
  type = 1 // 题目的类型
  show = 1 // 试卷是否显示 0 不显示 1 显示
  target_question='' // 下一题  跳转是字符串  显示/隐藏是数组
  _self = null // 自身题目
  $prev = null // 上个节点
  $next = null // 下个节点
  constructor({ question_id, show, target_question, _self }) {
    this.question_id = question_id
    this.show = show
    this.target_question = target_question
    this._self = _self
    this.type = _self.type
  }
}

// 内置逻辑条件
export const internalCondition = [{
  'question_id': 'decorationType',
  'title': '房屋装修类型',
  'type': 1,
  'option_list': [{
    'title': '毛坯',
    'option_id': 2200031
  }, {
    'title': '精装',
    'option_id': 2200032
  }]
}, {
  'question_id': 'hasClub',
  'title': '项目是否有会所',
  'type': 1,
  'option_list': [{
    'title': '有',
    'option_id': 1
  }, {
    'title': '无',
    'option_id': 0
  }]
}, {
  'question_id': 'isHfOperate',
  'title': '是否华发操盘物业',
  'type': 1,
  'option_list': [{
    'title': '是',
    'option_id': 1
  }, {
    'title': '否',
    'option_id': 0
  }]
}]
// 新增的结果条件
export const endPageList = [
  {
    'question_id': 'AbortEnd',
    'title': '提前结束（不计入结果）'
  },
  {
    'question_id': 'NormalEnd',
    'title': '正常结束（计入结果）'
  }
]
