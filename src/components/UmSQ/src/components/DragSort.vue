<template>
  <!--  :move="onMove" -->
  <draggable v-model="item.option_list" ghost-class="ghost-option" :handle="handle" :animation="200">
    <slot :list="item.option_list" />
  </draggable>
</template>

<script>
import draggable from 'vuedraggable'
import { OPTION_TYPE } from '../core/unit'
export default {
  name: 'DragSort',
  components: { draggable },
  props: {
    ele: {
      type: Object,
      default: null
    },
    handle: {
      type: String,
      default: '.option-r__move'
    }
  },
  data() {
    return {

    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  methods: {
    // onMove(e) {
    //   // 其他选项不参与排序
    //   if (e.relatedContext.element?.optionType === OPTION_TYPE.OTHER) {
    //     return false
    //   }
    //   return true
    // }
  }
}
</script>
