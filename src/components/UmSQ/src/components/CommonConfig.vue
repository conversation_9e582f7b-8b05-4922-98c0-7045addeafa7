<script>
import { SCORE_LIST, CONDIONS, SATISFIED_CONDIONS, GRADE_ICON, OPTION_TYPE } from '../core/unit'
import { htmlToText } from '../utils'
export default {
  name: 'CommonConfig',
  inject: ['getPointList', 'changeTab', 'typeCode'],
  props: {
    ele: {
      type: Object,
      default: null
    },
    showGrade: {
      type: Boolean,
      default: false
    }
  },
  data() {
    return {
      defaultProps: {
        label: 'targetName',
        value: 'tgtTargetId',
        checkStrictly: true,
        children: 'targetChildren'
      },
      myScores: [
        { label: 1, value: 1, disabled: false },
        { label: 2, value: 2, disabled: false },
        { label: 3, value: 3, disabled: false },
        { label: 4, value: 4, disabled: false },
        { label: 5, value: 5, disabled: false }
      ],
      bmyScores: [
        { label: 1, value: 1, disabled: false },
        { label: 2, value: 2, disabled: false },
        { label: 3, value: 3, disabled: false },
        { label: 4, value: 4, disabled: false },
        { label: 5, value: 5, disabled: false }
      ]
    }
  },
  computed: {
    item() {
      return this.ele
    },
    pointList() {
      return this.getPointList()
    }
  },
  watch: {
    item: {
      immediate: true,
      deep: true,
      handler(v) {
        this.showGrade && v && this.$nextTick(() => {
          this.computedUnicodeDom(this.item.style)
        })
        // // 如果满意原因和不满意原因都有分值，判断逻辑是否互斥
        // if (!v.kpi.score || !v.kpi.satisfiedScore) return
        // // 如果不满意原因是小于，满意原因是大于
        // if (v.kpi.type === 1 && v.kpi.satisfiedType === 1 && v.kpi.score > v.kpi.satisfiedScore + 1) {
        //   callback()
        // }
        // // 如果不满意原因是小于，满意原因是大于等于
        // if (v.kpi.type === 1 && v.kpi.satisfiedType === 2 && v.kpi.score >= v.kpi.satisfiedScore + 1) {
        //   callback()
        // }
        // // 如果不满意原因是小于等于，满意原因是大于
        // if (v.kpi.type === 2 && v.kpi.satisfiedType === 1 && v.kpi.score > v.kpi.satisfiedScore) {
        //   callback()
        // }
        // // 如果不满意原因是小于等于，满意原因是大于等于
        // if (v.kpi.type === 2 && v.kpi.satisfiedType === 2 && v.kpi.score >= v.kpi.satisfiedScore) {
        //   callback()
        // }
        // function callback() {
        //   Message.warning('当前题目显示满意与不满意原因逻辑互斥，请重新选择！')
        //   v.kpi.score = null
        //   v.kpi.satisfiedScore = null
        // }
      }
    }
  },
  created() {
    this.checkScoreDisabled()
  },
  methods: {
    styleChange(unicode) {
      this.item.style = unicode
      this.computedUnicodeDom(unicode)
    },
    checkScoreDisabled() {
      // 如果不满意原因是小于。1不可选
      // 如果满意原因是大于，5不可选
      this.bmyScores[0].disabled = this.item.kpi.type === 1
      this.myScores[4].disabled = this.item.kpi.satisfiedType === 1
    },
    // 动态向select选中后插入自定义元素
    computedUnicodeDom(unicode) {
      const dom = this.$refs.styleSelect.$el // 获取select的dom元素
      const input = dom.querySelector('.el-input__inner')
      input.style.display = 'none' // 把input元素隐藏
      const divInput = dom.querySelector('.el-input__inner.um_iconfont')
      const str = `<i class='unicode'>&#x${unicode}</i><i class='unicode'>&#x${unicode}</i><i class='unicode'>&#x${unicode}</i><i class='unicode'>&#x${unicode}</i><i class='unicode'>&#x${unicode}</i>`
      if (divInput) {
        divInput.innerHTML = str
      } else {
        const div = document.createElement('div')
        div.className = 'el-input__inner um_iconfont'
        div.innerHTML = str
        dom.querySelector('.el-input').appendChild(div)
      }
    },
    pointChange(id) {
      this.item.kpi.id = id
      this.item.targetName = null
    }
  },
  render() {
    return <div>
      <div class="m-form">
        <div class="m-label">是否必答</div>
        <div class="m-value">
          <el-switch value={this.item.required} onInput={val => { this.item.required = val }} />
        </div>
      </div>
      <div class="m-form m-form-column">
        <div class="m-label between">
          <span class="m-form__label--l">选项</span>
          <span class="m-form__label--r">分值</span>
        </div>
        {
          // 过滤掉拒答和不知道选项
          this.item.option_list.filter(item => item.optionType !== OPTION_TYPE.REFUSE && item.optionType !== OPTION_TYPE.UNKNOW).map((item, index) => {
            const textTitle = htmlToText(item.title)
            return <div class="m-value between">
              <span class="m-form__label--l row_1" title={textTitle}>{textTitle}</span>
              <div class="m-form__label--r">
                <el-select value={item.score} placeholder="请选择" onChange={score => { item.score = score }}>
                  {
                    SCORE_LIST.map(_item => {
                      return <el-option
                        key={_item}
                        label={_item}
                        value={_item}
                      >
                      </el-option>
                    })
                  }
                </el-select>
              </div>
            </div>
          })
        }
      </div>
      {
        this.showGrade && <div class="m-form m-form-column">
          <div class="m-label">显示样式</div>
          <div class="m-value">
            <el-select ref='styleSelect' value={this.item.style} placeholder="请选择显示样式" style="width: 100%" onChange={unicode => { this.styleChange(unicode) }}>
              {
                GRADE_ICON.map(item => {
                  return <el-option
                    key={item.iconUnicode + Math.random() * 10000}
                    label={item.iconUnicode}
                    value={item.iconUnicode}
                  >
                    <i class='um_iconfont unicode'>{String.fromCharCode(parseInt(item.iconUnicode, 16))}</i>
                    <i class='um_iconfont unicode'>{String.fromCharCode(parseInt(item.iconUnicode, 16))}</i>
                    <i class='um_iconfont unicode'>{String.fromCharCode(parseInt(item.iconUnicode, 16))}</i>
                    <i class='um_iconfont unicode'>{String.fromCharCode(parseInt(item.iconUnicode, 16))}</i>
                    <i class='um_iconfont unicode'>{String.fromCharCode(parseInt(item.iconUnicode, 16))}</i>
                  </el-option>
                })
              }
            </el-select>
          </div>
        </div>
      }
      {/* 调研问卷配置，深访问卷没有 */}
      { this.typeCode === 1 && <div>
        <div class="m-form m-form-column">
          <div class="m-label">关联指标</div>
          <div class="m-value">
            <el-select-tree
              value={this.item.kpi.id}
              style="width: 100%"
              options={this.pointList}
              onChange={ this.pointChange.bind(this) }
              clearable
              props={ {
                props: this.defaultProps
              } }
            />
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">当分值</div>
          <div class="m-value between">
            <el-select value={this.item.kpi.type} placeholder="请选择" style="width: 100%;margin-right: 12px" onChange={type => {
              this.item.kpi.type = type
              this.checkScoreDisabled()
            }}>
              {
                CONDIONS.map(item => {
                  return <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  />
                })
              }
            </el-select>
            <el-select value={this.item.kpi.score} clearable placeholder="请选择" style="width: 100%;margin-right: 16px" onChange={score => {
              this.item.kpi.score = score
            }}>
              {
                this.bmyScores.map(_item => {
                  return <el-option
                    key={_item.value}
                    label={_item.label}
                    value={_item.value}
                    disabled={_item.disabled}
                  />
                })
              }
            </el-select>
            <span>时</span>
          </div>
          <span class="tip">显示不满意原因</span>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">当分值</div>
          <div class="m-value between">
            <el-select value={this.item.kpi.satisfiedType} placeholder="请选择" style="width: 100%;margin-right: 12px" onChange={type => {
              this.item.kpi.satisfiedType = type
              this.checkScoreDisabled()
            }}>
              {
                SATISFIED_CONDIONS.map(item => {
                  return <el-option
                    key={item.value}
                    label={item.label}
                    value={item.value}
                  >
                  </el-option>
                })
              }
            </el-select>
            <el-select value={this.item.kpi.satisfiedScore} clearable placeholder="请选择" style="width: 100%;margin-right: 16px" onChange={score => {
              this.item.kpi.satisfiedScore = score
            }}>
              {
                this.myScores.map(_item => {
                  return <el-option
                    key={_item.label}
                    label={_item.label}
                    value={_item.value}
                    disabled={_item.disabled}
                  >
                  </el-option>
                })
              }
            </el-select>
            <span>时</span>
          </div>
          <span class="tip">显示满意原因</span>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">计入考核</div>
          <div class="m-value">
            <el-select value={this.item.isExamine} placeholder="请选择" style="width: 100%;margin-right: 12px" onChange={isExamine => { this.item.isExamine = isExamine }}>
              <el-option label="是" value={true}></el-option>
              <el-option label="否" value={false}></el-option>
            </el-select>
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">反向抽查</div>
          <div class="m-value">
            <el-select value={this.item.isCheck} placeholder="请选择" style="width: 100%;margin-right: 12px" onChange={isCheck => { this.item.isCheck = isCheck }}>
              <el-option label="是" value={true}></el-option>
              <el-option label="否" value={false}></el-option>
            </el-select>
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">启用拒答</div>
          <div class="m-value">
            <el-select value={this.item.isRefuse} placeholder="请选择" style="width: 100%;margin-right: 12px" onChange={isRefuse => {
              this.item.isRefuse = isRefuse
              this.item.editRefuseOption(isRefuse)
            }}>
              <el-option label="是" value={true}></el-option>
              <el-option label="否" value={false}></el-option>
            </el-select>
          </div>
        </div>
      </div>}
      <div class="m-form between">
        <div class="m-label">跳转逻辑</div>
        <div class="m-value"><el-button type="text" onClick={this.changeTab.bind(this, 1)}><i class="um_iconfont">&#xe690;</i> 添加跳转逻辑</el-button></div>
      </div>
    </div>
  }
}
</script>

<style lang="scss">
.unicode {
  font-size: 16px;
  margin-right: 12px;
  color: #D1D5DC;
  font-style: normal;
  &:nth-child(1),&:nth-child(2),&:nth-child(3) {
    color: #FF9645;
  }
}
</style>
