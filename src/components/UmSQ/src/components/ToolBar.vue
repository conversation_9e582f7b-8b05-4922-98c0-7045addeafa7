<template>
  <div class="u-toobar">
    <el-button type="text" @click="checkLength() && ele.addOption()"> <i class="um_iconfont">&#xe690;</i> 添加选项</el-button>
    <el-button type="text" @click="checkLength() && (dialogVisible = true)"><i class="um_iconfont">&#xe61b;</i> 批量添加</el-button>
    <el-button type="text" @click="checkLength() && ele.addOtherOption(oneOhter)"><i class="um_iconfont">&#xe690;</i> 添加“其他”项</el-button>
    <el-dialog :visible.sync="dialogVisible" append-to-body title="批量添加选项" width="538px" @close="$refs.form.resetFields();">
      <el-form ref="form" :model="form" label-suffix="">
        <el-form-item prop="content" label="每行代表一个选项，可以添加多个选项" :rules="[{ message: '请输入', trigger: 'blur' },]">
          <el-input v-model="form.content" type="textarea" :rows="6" resize="none" placeholder="请输入" />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="batchAdd">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { MAX_OPTIONS_LENGTH } from '../core/unit.js'
export default {
  name: 'ToolBar',
  props: {
    optionsLength: {
      type: Number,
      default: 0
    },
    ele: {
      type: Object,
      default: null
    },
    // 默认只能添加一个其他项
    oneOhter: {
      type: Boolean,
      default: true
    }
  },
  data() {
    return {
      dialogVisible: false,
      form: {
        content: ''
      }
    }
  },
  methods: {
    batchAdd() {
      if (!this.form.content) {
        this.dialogVisible = false
        return
      }
      this.ele.batAddOption(this.form.content.trim()).then(res => {
        this.dialogVisible = false
      }).catch(err => {
        this.$message.warning(err)
      })
    },
    checkLength() {
      if (this.ele.option_list.length >= MAX_OPTIONS_LENGTH) {
        this.$message.warning('最多添加' + MAX_OPTIONS_LENGTH + '个选项！')
        return false
      }
      return true
    }
  }
}
</script>
