<template>
  <el-dialog
    title="添加到题库"
    :visible.sync="dialogVisible"
    :close-on-click-modal="false"
    width="500px"
    append-to-body
  >
    <el-radio-group v-model="quesQuestionCategoryId" style="width: 100%;">
      <UmConfigTree
        show-menu
        :tree-data="treeData"
        :default-props="{
          children: 'categoryChildren',
          label: 'name',
          value: 'quesQuestionCategoryId'
        }"
      >
        <template slot="menu" slot-scope="{data}">
          <!-- <el-button size="mini" type="primary" @click.stop="append(data)">添加</el-button> -->
          <div @click.stop="currentData = data"><el-radio class="hide-raiod__label" :label="data.quesQuestionCategoryId" /></div>
        </template>
      </UmConfigTree>
    </el-radio-group>
    <span slot="footer" class="dialog-footer">
      <el-button @click="dialogVisible = false">取 消</el-button>
      <el-button type="primary" @click="append">确 定</el-button>
    </span>
  </el-dialog>
</template>

<script>
import { htmlToText } from '../utils'
import { getCategoryTree, addQuestionBank } from '@/api/wenjuan'
import { QUESTION_TYPE_CODE } from '../core/unit'
import cloneDeep from 'lodash/cloneDeep'
export default {
  props: {
    show: {
      type: Boolean,
      default: false
    },
    questionData: {
      type: [Object],
      default: null
    }
  },
  data() {
    return {
      dialogVisible: false,
      treeData: [],
      quesQuestionCategoryId: null,
      currentData: {}
    }
  },
  watch: {
    async show(v) {
      if (!v) {
        this.dialogVisible = v
        return
      }
      const load = this.$load()
      try {
        await this.getTreeData()
        this.dialogVisible = v
      } catch (error) {

      } finally {
        load.close()
      }
    },
    dialogVisible(v) {
      this.$emit('update:show', v)
    }
  },
  methods: {
    getTreeData() {
      return getCategoryTree().then(res => {
        const arr = res.data || []
        this.treeData = arr
      })
    },
    append() {
      if (!this.quesQuestionCategoryId) {
        this.$message.warning('请选择题库分类！')
        return
      }
      const questionName = htmlToText(this.questionData.title)
      const questionData = cloneDeep(this.questionData)
      delete questionData.question_id
      this.$confirm(`确定将“${questionName}”题目添加到“${this.currentData.name}”分类中？`, '提示', {
        confirmButtonText: '确定',
        cancelButtonText: '取消',
        type: 'warning'
      }).then(() => {
        const load = this.$load()
        addQuestionBank({
          quesQuestionCategoryId: this.quesQuestionCategoryId,
          questionName,
          questionTypeCode: QUESTION_TYPE_CODE[this.questionData.type],
          content: JSON.stringify(questionData),
          tgtTargetId: ''
        }).then(res => {
          this.$message.success(res.msg)
          this.dialogVisible = false
          this.$emit('success')
        }).finally(() => load.close())
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.hide-raiod__label {
  ::v-deep {
    .el-radio__label {
      display: none;
    }
  }
}
</style>
