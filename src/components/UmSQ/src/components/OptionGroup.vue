<template>
  <div>
    <el-dialog
      title="选项分组"
      :visible.sync="dialogVisible"
      :close-on-click-modal="false"
      width="900px"
      append-to-body
    >
      <el-row type="flex" justify="flex-start">
        <el-row type="flex" align="middle" style="width: 300px;margin-right: 20px;padding-bottom: 10px;">
          待分组选项
        </el-row>
        <el-row type="flex" style="flex: 1;padding-bottom: 10px;" justify="space-between" align="middle">
          分组管理
          <el-button type="primary" size="mini" plain icon="el-icon-plus" @click="editForm.groupId = null;editDialogVisible = true">添加分组</el-button>
        </el-row>
      </el-row>
      <div class="option-group">
        <!-- 左侧选项列表 -->
        <div class="option-list">
          <div class="option-list__header">
            <el-input
              v-model="searchText"
              placeholder="请输入选项名称"
              prefix-icon="el-icon-search"
              clearable
            />
          </div>
          <div class="option-list__content">
            <el-checkbox-group v-model="selectedOptions">
              <el-checkbox
                v-for="item in filteredOptions"
                :key="item.option_id"
                :label="item.option_id"
                :disabled="item.disabled"
              >
                {{ item.text }}
              </el-checkbox>
            </el-checkbox-group>
          </div>
        </div>

        <!-- 右侧分组管理 -->
        <div class="group-manage">
          <UmConfigTree
            show-menu
            :tree-data="treeData"
            draggable
          >
            <template slot="menu" slot-scope="{ data }">
              <el-button v-if="data.level === 1" type="text" size="mini" @click.stop="addOption(data)">加入</el-button>
              <el-popover
                v-if="data.level === 1"
                placement="right-start"
                popper-class="custom-tree-popover"
                width="104"
                :visible-arrow="false"
                trigger="hover"
              >
                <div class="u-tree__menu">
                  <div class="primary" @click.stop="editGroup(data)">编辑</div>
                  <div class="danger" @click.stop="removeGroup(data)">删除</div>
                  <div class="success" @click.stop="move(data, 1)">上移</div>
                  <div class="success" @click.stop="move(data, 2)">下移</div>
                </div>
                <svg-icon slot="reference" class="tree-more" icon-class="more" />
              </el-popover>
              <template v-else>
                <i class="el-icon-rank info" style="cursor: move; margin-right: 10px;" />
                <i class="el-icon-delete danger" @click.stop="removeOption(data, true)" />
              </template>
            </template>
          </UmConfigTree>
        </div>
      </div>
      <div class="tip" style="margin-top: 10px;">提示：请选择左侧选项加入右侧分组</div>
      <span slot="footer" class="dialog-footer">
        <el-button @click="dialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirm">确 定</el-button>
      </span>
    </el-dialog>
    <el-dialog width="500px" :title="!editForm.groupId ? '添加分组' : '编辑分组'" :visible.sync="editDialogVisible" append-to-body @close="$refs.editForm.resetFields();">
      <el-form ref="editForm" :model="editForm">
        <el-form-item label="分组名称" prop="editGroupName" :rules="{ required: true, message: '请输入分组名称' }">
          <el-input v-model="editForm.editGroupName" placeholder="请输入分组名称" clearable maxlength="30" show-word-limit />
        </el-form-item>
      </el-form>
      <span slot="footer" class="dialog-footer">
        <el-button @click="editDialogVisible = false">取 消</el-button>
        <el-button type="primary" @click="confirmEdit">确 定</el-button>
      </span>
    </el-dialog>
  </div>
</template>

<script>
import { cloneDeep } from 'lodash'
import { htmlToText } from '../utils'
import { v4 as uuidv4 } from 'uuid'
export default {
  name: 'OptionGroup',
  props: {
    visible: {
      type: Boolean,
      default: false
    },
    optionList: {
      type: Array,
      default: () => []
    },
    optionGroup: {
      type: Array,
      default: () => []
    },
    groupOptionIds: {
      type: Array,
      default: () => []
    }
  },
  data() {
    return {
      treeData: [],
      dialogVisible: false,
      searchText: '',
      selectedOptions: [],
      copyOptionsList: [],
      editDialogVisible: false,
      editForm: {
        groupId: null,
        editGroupName: ''
      }
    }
  },
  computed: {
    filteredOptions() {
      if (!this.searchText) return this.copyOptionsList
      return this.copyOptionsList.filter(opt =>
        opt.text.includes(this.searchText)
      )
    }
  },
  watch: {
    visible(val) {
      this.dialogVisible = val
    },
    dialogVisible(val) {
      if (!val) {
        this.$emit('close', val)
        this.resetData()
      } else {
        this.treeData = cloneDeep(this.optionGroup)
        this.updateOptionGroup()
        const arr = cloneDeep(this.optionList)
        arr.forEach(item => {
          item.text = htmlToText(item.title)
          item.disabled = this.groupOptionIds.includes(item.option_id)
        })
        this.copyOptionsList = arr
      }
    }
  },
  methods: {
    // 更新分组数据为最新选项数据，因为深拷贝导致数据引用关系会丢失
    updateOptionGroup() {
      this.treeData.forEach(item => {
        item.children.forEach(_item => {
          const index = this.optionList.findIndex(i => i.option_id === _item.option_id)
          if (index !== -1) {
            _item.label = htmlToText(this.optionList[index].title)
          }
        })
      })
    },
    // 加入选项
    addOption(data) {
      if (!this.selectedOptions.length) {
        this.$message.warning('请先选择待分组选项！')
        return
      }
      const selectOptions = []
      this.copyOptionsList.forEach(item => {
        if (this.selectedOptions.findIndex(_ => item.option_id === _) !== -1) {
          item.disabled = true
          item.itemTypeName = data.label
          selectOptions.push({
            ...item,
            label: item.text,
            value: item.option_id,
            level: 2,
            parentLabel: data.label,
            parentValue: data.value
          })
        }
      })
      data.children.push(...selectOptions)
      this.selectedOptions = []
    },
    // 删除选项
    removeOption(data, childrenRemove = true) {
      let index = -1
      this.treeData.forEach(item => {
        index = item.children.findIndex(option => option.value === data.value)
        if (index !== -1 && childrenRemove) {
          item.children.splice(index, 1)
        }
      })
      this.copyOptionsList.forEach(item => {
        if (item.option_id === data.value) {
          item.disabled = false
        }
      })
      this.$forceUpdate()
    },
    // 删除分组
    async removeGroup(data) {
      if (data.children && data.children.length) {
        try {
          await this.$confirm('删除分类后，该分类下关联的选项也将解除关联，确定要删除吗？', '提示', {
            type: 'warning'
          })
        } catch {
          return
        }
      }
      const index = this.treeData.findIndex(d => d.value === data.value)
      this.treeData[index].children?.forEach(item => {
        this.removeOption(item, false)
      })
      this.treeData.splice(index, 1)
    },
    resetData() {
      this.searchText = ''
      this.selectedOptions = []
      this.treeData = []
      this.editDialogVisible = false
      this.editForm = {
        groupId: null,
        editGroupName: ''
      }
    },
    // 编辑分组
    editGroup(data) {
      this.editForm.groupId = data.value
      this.editForm.editGroupName = data.label
      this.$nextTick(() => {
        this.editDialogVisible = true
      })
    },
    // 分组弹窗确认
    confirmEdit() {
      this.$refs.editForm.validate(valid => {
        if (valid) {
          if (this.editForm.groupId) {
            const index = this.treeData.findIndex(d => d.value === this.editForm.groupId)
            this.treeData[index].label = this.editForm.editGroupName
          } else {
            this.treeData.push({
              label: this.editForm.editGroupName,
              value: uuidv4(),
              level: 1,
              isEditing: false,
              children: []
            })
          }
          this.editDialogVisible = false
        }
      })
    },
    // 移动分组
    move(data, type) {
      const index = this.treeData.findIndex(d => d.value === data.value)
      const arr = cloneDeep(this.treeData)
      if (type === 1) {
        arr.splice(index - 1, 0, arr.splice(index, 1)[0])
      } else if (type === 2) {
        arr.splice(index + 1, 0, arr.splice(index, 1)[0])
      }
      this.treeData = arr
    },
    confirm() {
      const load = this.$load()
      // 对原有选项进行排序
      const copyOptionsList = cloneDeep(this.copyOptionsList)
      let sort = 1
      this.treeData.forEach(item => {
        item.children?.forEach(_item => {
          const index = copyOptionsList.findIndex(i => i.option_id === _item.option_id)
          if (index !== -1) {
            copyOptionsList[index].sort = sort++
          }
        })
      })
      copyOptionsList.forEach(item => {
        if (!item.sort) {
          item.sort = sort++
        }
      })
      this.$emit('confirm', {
        optionGroup: cloneDeep(this.treeData),
        groupOptionIds: this.copyOptionsList.filter(item => item.disabled).map(item => item.option_id),
        optionList: copyOptionsList.sort((a, b) => a.sort - b.sort)
      })
      this.dialogVisible = false
      this.$nextTick(() => {
        load.close()
      })
    }
  }
}
</script>

<style lang="scss" scoped>
.option-group {
  display: flex;
  height: 500px;
  gap: 10px;

  .option-list {
    width: 300px;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;

    &__header {
      padding: 10px;
      border-bottom: 1px solid #DCDFE6;
    }

    &__content {
      flex: 1;
      padding: 10px;
      overflow-y: auto;

      .el-checkbox {
        display: block;
        margin-left: 0;
        margin-bottom: 10px;
      }
    }
  }

  .group-manage {
    flex: 1;
    border: 1px solid #DCDFE6;
    border-radius: 4px;
    display: flex;
    flex-direction: column;
    padding: 10px;
    overflow-y: auto;
    .group-node {
      width: 100%;
      display: flex;
      align-items: center;

      .group-title {
        flex: 1;
        display: flex;
        justify-content: space-between;
        align-items: center;
      }

      .group-actions {
        display: flex;
        gap: 10px;
        color: #909399;

        i {
          cursor: pointer;
          &:hover {
            color: #409EFF;
          }
        }
      }
    }

    .add-group {
      margin-top: 10px;
      border-top: 1px solid #DCDFE6;
      padding-top: 10px;
    }
  }
}
</style>
