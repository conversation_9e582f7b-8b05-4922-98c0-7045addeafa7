.um-sq_condition{
  position: relative;
  flex:1 0 0;
  background: #FFFFFF;
  box-shadow: 0 3px 5px 1px rgba(229,229,229,0.43);
  border-radius: 12px;
  padding: 20px 0 ;
  display: flex;
  overflow: hidden;
  &:after{
    content: '';
    display: block;
    border-right: 1px solid #E4E8EB;
    position: absolute;
    right:640px;
    top:20px;
    bottom:20px;
    transform: translateX(-50%);
  }
  .um-sq_condition_left{
    flex:1 0 0;
    display: flex;
    flex-direction: column;
  }
  .um-sq_condition_right{
    flex:640px 0 0;
    display: flex;
    flex-direction: column;
  }
  .m-head{
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: 10px;
    padding:0 20px;
    .u-title{
      font-size: 16px;
      font-weight: 600;
      color: $--color-text-primary;
      display: flex;
      align-items: center;
      &:before{
        content: '';
        display: block;
        width: 16px;
        height: 16px;
        background: url("../images/border.png") center no-repeat;
        background-size: contain;
        margin-right: 4px;
      }
    }
  }
  .m-content{
    flex:1;
    overflow: auto;
    padding:10px 20px;
  }
  .m-none{
    background: url("../images/none.png") center 100px no-repeat;
    background-size: 240px 168px;
    font-size: 14px;
    font-weight: 400;
    color: #31415F;
    text-align: center;
    padding: 100px+168px 0 100px;
    user-select: none;
  }
  .m-cond_list{
    list-style:none;
    .m-cond_item{
      position: relative;
      background: #F5F7FA;
      border-radius: 8px;
      padding:16px 16px;
      margin-bottom: 20px;
      &.active,&:hover{
        outline: 1px solid $--color-primary;
        .u-delete{
          display: block;
        }
      }
      .u-delete{
        display: none;
        position: absolute;
        right: -8px;
        top:-8px;
        font-size: 16px;
        line-height: 1;
        color: #FF6161;
        cursor: pointer;
        background: #fff;
        border-radius: 50%;
      }
      .txt_30{
        width: 30px;
        text-align: right;
      }
      .txt{
        margin-left: 6px;
        margin-right: 6px;
        font-size: 14px;
        &:first-child{
          margin-left: 0;
        }
        &:last-child{
          margin-right: 0;
        }
      }
      .option{
        flex:1;
      }
      .txt-tip{
        width: 130px;
      }
      .m-line1{
        display: flex;
        align-items: center;
        margin-bottom: 10px;
      }
      .error-msg{
        margin: 10px 0;
        font-size: 12px;
        color:$--color-danger;
      }
      .m-line2{
        margin-top: 10px;
        display: flex;
        align-items: center;
      }
    }
  }
}















