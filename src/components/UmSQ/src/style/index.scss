.um-sq{
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;  
}
.um-sq_header{
  width: calc(100% + 20px);
  height: 72px;
  background: #FFFFFF;
  box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
  margin-bottom: 20px;
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding:0 20px;
  > div {
    flex: 1;
  }
  .m-left{
    display: flex;
    align-items: center;
    overflow: hidden;
    &__center {
      height: 32px;
    }
    .u-title{
      font-size: 16px;
      font-weight: 500;
      color: #31415F;
      margin-left: 8px;
      max-width: 100%;
      white-space: nowrap;
      text-overflow: ellipsis;
      overflow: hidden;
    }
    .u-edit{
      font-size: 13px;
      color: $--color-primary;
      margin-left: 8px;
      cursor: pointer;
      line-height: 1;
    }
    .u-back{
      font-size: 16px;
      color: $--color-primary;
      cursor: pointer;
    }
  }
  .m-center{
    height: 100%;
    &.fix-width {
      flex: 0;
      width: 228px;
    }
  }
  .m-tabs{
    display: flex;
    align-items: center;
    height: 100%;
    cursor: pointer;
    position: relative;
    .line{
      position:absolute;
      display: block;
      width: 60px;
      height: 23px;
      bottom:0;
      left:57px;
      transform: translateX(-50%);
      background: url("../images/line.png") center no-repeat;
      background-size: contain;
      transition: left 300ms;
    }
    .u-tab{
      width: 114px;
      height: 100%;
      font-size: 16px;
      font-weight: 400;
      color: #69748A;
      display: flex;
      align-items: center;
      justify-content: center;
      &.on{
        color:$--color-text-primary;
        font-weight: 500;
        &:nth-child(1) ~.line{
          left:57px;
        }
        &:nth-child(2) ~.line{
          left:114px + 57px;
        }
      }
    }
  }
  .m-button_tab{
    display: flex;
    align-items: center;
    width: 272px;
    border-radius: 8px;
    color: #666666;
    font-size: 16px;
    font-weight: 400;
    margin-top: 16px;
    background-color: #F5F7FA;
    height: 40px;
    overflow: hidden;
    cursor: pointer;
    .u-tab{
      flex: 1;
      height: 100%;
      display: flex;
      align-items: center;
      justify-content: center;
      .svg-icon {
        font-size: 24px;
        margin-right: 12px;
        
      }
      &.on{
        background-color: $--color-primary;
        color: #fff;
        .svg-icon {
          color: #fff;
        }
      }
    }
  }
  .m-right {
    display: flex;
    justify-content: flex-end;

  }
}
.um-sq_main{
  flex:1;
  margin-left: 20px;
  display: flex;
  overflow: hidden;
  &.card {
    display: flex;
    flex-direction: column;
    align-items: center;
    overflow-y: auto;
  }
  &.pc {
    width: 660px;
    background-color: #fff;
    margin: 0 auto;
    padding-top: 20px;
    padding-bottom: 20px;
  }
  .u-tips {
    margin-bottom: 20px;
    i {
      color: #FF9645;
      margin-right: 8px;
    }
  }
}
.um-sq_left{
  width: 200px;
  background: #FFFFFF;
  box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
  // border-radius: 12px;
  user-select: none;
  display: flex;
  flex-direction: column;
  .m-caption{
    line-height: 54px;
    font-size: 14px;
    color: $--color-text-primary;
    display: flex;
    align-items: center;
    border-bottom: 1px solid #F0F3F5;
    margin-bottom: 20px;
    display: flex;
    &__item {
      flex: 1;
      text-align: center;
      border-bottom: 2px solid transparent;
      cursor: pointer;
      color: #666666;
      margin: 0 20px;
      &:active {
        opacity: 0.8;
      }
      &.active {
        font-weight: 500;
        color: $--color-primary;
      }
    }
  }
  .m-list{
    flex: 1;
    overflow-y: auto;
    display: flex;
    flex-direction: column;
    align-items: center;
    .m-unit{
      margin-bottom: 16px;
      width: 150px;
      height: 48px;
      background: #FFFFFF;
      border-radius: 4px;
      border: 1px solid #F0F3F5;
      padding-left: 8px;
      padding-right: 8px;
      padding-top: 7px;
      cursor: pointer;
      flex-shrink: 0;
      &:hover{
        border: 1px solid $--color-primary;
      }
      .name{
        font-size: 14px;
        font-weight: 400;
        color: #31415F;
        line-height: 14px;
        margin-bottom: 4px;
      }
      .icon{
        width: 100%;
        height: 14px;
        background-size: contain;
        background-repeat: no-repeat;
        background-color: #F5F7FA;
        border-radius: 2px;
        &.icon-1{
          background-image: url('../images/type1.png');
        }
        &.icon-2{
          background-image: url('../images/type2.png');
        }
        &.icon-3{
          background-image: url('../images/type3.png');
        }
        &.icon-4{
          background-image: url('../images/type4.png');
        }
        &.icon-5{
          background-image: url('../images/type3.png');
        }
        &.icon-6{
          background-image: url('../images/type6.svg');
        }
      }
    }
  }
}
.um-sq_content{
  flex:1;
  margin:0 20px;
  background: #FFFFFF;
  box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
  // border-radius: 12px;
  padding: 20px;
  overflow-y: auto;
  position: relative;
  .m-header{
    background: #F5F7FA;
    border-radius: 8px;
    padding:10px;
    margin-bottom: 10px;
    .title{
      font-size: 20px;
      font-weight: 500;
      color: #31415F;
      text-align: center;
      //margin: 0 40px;
    }
    .content{
      //margin: 10px 40px 0;
      margin-top: 10px;
      font-size: 14px;
      font-weight: 400;
      color: #6C7B96;
      line-height: 20px;
    }
  }
  .m-footer{
    background: #F5F7FA;
    border-radius: 8px;
    padding:10px;
    margin-top: 20px;
    .content{
      font-size: 14px;
      font-weight: 400;
      color: #6C7B96;
      line-height: 20px;
    }
  }
  .m-end{
    margin-top: 20px;
    text-align: center;
    .title{
      font-size: 14px;
      font-weight: 500;
      color: $--color-text-primary;
      line-height: 14px;
      margin-bottom: 10px;
    }
    .content{
      font-size: 14px;
      font-weight: 400;
      color: #6C7B96;
      line-height: 14px;
      margin-top: 16px;
    }
  }
}
.um-sq_right{
  width: 288px;
  background: #FFFFFF;
  box-shadow: 2px 0px 5px 1px rgba(229,229,229,0.4);
  // border-radius: 12px;
  overflow: hidden;
  display: flex;
  flex-direction: column;
  .m-tabs{
    line-height: 56px;
    text-align: center;
    display: flex;
    cursor: pointer;
    .u-tab{
      flex:1;
      background: #f5f7fa;
      font-size: 16px;
      font-weight: 400;
      color: #6C7B96;
      &.none{
        cursor: not-allowed;
      }
      &.on{
        background: #fff;
        font-weight: 500;
        color: $--color-primary;
      }
    }
  }
  &__content {
    flex: 1;
    overflow-y: auto;
  }
  .m-form{
    margin: 0 16px;
    display: flex;
    justify-content: space-between;
    align-items: center;
    border-bottom: 1px dashed #E4E8EB;
    padding: 16px 0;
    &:last-child{
      border-bottom: none;
    }
    &.m-form-column{
      flex-direction: column;
      align-items: flex-start;
      &.flex-row-start {
        justify-content: flex-start;
      }
      .m-label{
        margin-bottom: 12px;
      }
      .m-value{
        width: 100%;
      }
      .tip {
        color: #6C7B96;
      }
    }
    .between {
      display: flex;
      width: 100%;
      align-items:  center;
      margin-bottom: 12px;
      .m-form__label--l {
        flex: 1;
        padding-right: 10px;
      }
      .m-form__label--r {
        width: 80px;
      }
    }
    .m-label{
      font-size: 14px;
      font-weight: 400;
      color: #31415F;
      line-height: 1;
    }
  }
}
.um-sq_components{
  .m-none{
    background: url("../images/none.png") center 100px no-repeat;
    background-size: 240px 168px;
    font-size: 14px;
    font-weight: 400;
    color: #31415F;
    text-align: center;
    padding: 100px+168px 0 100px;
    user-select: none;
  }
  .ghost-item{
    background: $--color-primary;
    height: 10px;
    font-size: 0;
    *{
      font-size: 0;
    }
    
  }
  .ghost-option {
    height: 100%;
    &::after {
      content: '';
      width: 100%;
      height: 100%;
      background-color: #fff;
      position: absolute;
      top: 0;
      left: 0;
    }
  }
  .m-ele{
    position: relative;
    background: #F5F7FA;
    border-radius: 8px;
    padding: 20px 16px;
    margin-top: 20px;
    margin-bottom: 20px;
    cursor: pointer;
    .m-tool{
      position: absolute;
      top:25px;
      right:16px;
      width: 100px;
      display: none;
      justify-content: flex-end;
      margin-bottom: 10px;
      z-index:10;
      user-select: none;
      .u-move{
        font-size: 18px;
        color: $--color-primary;
        margin-right: 14px;
        cursor: pointer;
      }
      .u-delete{
        font-size: 18px;
        color:#FF6161;
        cursor: pointer;
      }
    }
    &.active,&:hover{
      outline: 1px solid $--color-primary;
      .m-tool{
        display: flex;
      }
    }
    .m-top{
      display: flex;
      font-size: 16px;
      font-weight: 400;
      color: $--color-text-primary;
      margin-bottom: 10px;
      margin-right: 90px;
      .idx{
        position: relative;
        //width: 28px;
        flex:20px 0 0 ;
        line-height: 36px;
        &:before{
          content: '*';
          display: block;
          position: absolute;
          right:100%;
          top:0;
          line-height: 36px;
          margin-right: 2px;
          //top:50%;
          //transform: translateY(-50%);
          font-size: 16px;
          font-weight: 400;
          color: #FF6161;
          opacity: 0;
        }
        &.required:before{
          opacity: 1;
        }
      }
      .title{
        flex:1;
      }
    }
    .m-content{
      margin-right: 100px;
      padding:0 12px 0 40px ;
      &.m-content_edit{
        padding:0 0 0 28px ;
        &.dragable {
          margin-right: 0;
        }
        &.grade {
          display: flex;
          .grade-item {
            display: flex;
            flex-direction: column;
            justify-content: center;
            margin-right: 50px;
            color: #D1D5DC;
            font-size: 14px;
            .unicode {
              color: #D1D5DC;
              font-size: 24px;
              line-height: 1;
            }
          }
        }
      }
      .group-title {
        font-size: 16px;
        color: #000;
        margin-bottom: 10px;
        font-weight: 600;
      }
      .option {
        display: flex;
        align-items: center;
        margin-bottom: 8px;
        position: relative;
        &:last-child {
          margin-bottom: 12px;
        }
        &:hover {
          .option-r {
            opacity: 1;
          }
        }
        &-l {
          flex: 1;
          display: flex;
          align-items: center;
          .circle {
            width: 14px;
            height: 14px;
            border: 1px solid #ADB3BF;
            border-radius: 50%;
            margin-right: 8px;
          }
          .square {
            width: 14px;
            height: 14px;
            border: 1px solid #ADB3BF;
            margin-right: 8px;
          }
          .um-inline_edit {
            flex: 1;
            
          }
        }
        &-r {
          opacity: 0;
          transition: all linear .3s;
          padding-left: 8px;
          font-size: 18px;
          &__move {
            margin-right: 14px;
          }
        }
      }
    }
  }
}
.g-content {
  display: flex;
  height: 100%;
  .g-preview__qrcode {
    width: 226px;
    height: 257px;
    background: #EDF1FE;
    border-radius: 14px;
    border: 1px solid $--color-primary;
    display: flex;
    align-items: center;
    justify-content: center;
    flex-direction: column;
    margin-left: 30px;
    .qrcode {
      width: 194px;
      height: 194px;
      background: #FFFFFF;
      box-shadow: 0px 2px 4px 0px #E2E9FF;
      border-radius: 10px;
      .canvas {
        width: 194px!important;
        height: 194px!important;
      }
    }
    > span {
      color: $--color-primary;
      margin-top: 16px;
    }
  }
}
.g-preview {
  flex: 1;
  &.onePage {
    flex: 1;
    display: flex;
    flex-direction: column;
  }
  &.mobile {
    width: 355px;
    height: 667px;
    padding: 20px 22.5px 30px;
    background-image: url('~@/assets/images/manage/mobile-bg.png');
    background-repeat: no-repeat;
    background-size: contain;
    display: flex;
    flex-direction: column;
    overflow: hidden;
    .g-preview__head {
      .m-status {
        line-height: 30px;
        display: flex;
        justify-content: space-between;
        padding: 0 16px;
        i {
          font-style: normal;
          margin-left: 5px;
        }
        i, .svg-icon {
          font-size: 12px;
        }
      }
      .m-title {
        line-height: 44px;
        text-align: center;
        color: #192130;
        font-size: 18px;
      }
    }
    .g-preview__webview {
      border-bottom-left-radius: 30px;
      border-bottom-right-radius: 30px;
      flex: 1;
    }
  }
  &.pc {
    width: 560px;
    .g-preview__head { 
      display: none;
    }
    .g-preview__webview {
      height: 100%;
    }
  }
  &__webview {
    border: none;
    width: 100%;
    overflow: hidden;
  }
}
