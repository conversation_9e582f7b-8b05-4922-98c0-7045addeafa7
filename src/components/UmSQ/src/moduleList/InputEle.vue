<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div class="m-top">
      <div
        class="idx"
        :class="{
          required:item.required
        }"
      >
        {{ idx }}
      </div>
      <div class="title"><UmInlineEdit v-model="item.title" is-title /></div>
    </div>
    <div class="m-content">
      <el-input :rows="ROW_ENUM[item.size]" type="textarea" resize="none" :placeholder="item.placeholder" readonly />
    </div>
  </div>
</template>

<script>
import { INPUT_SIZE } from '../core/unit'
const ROW_ENUM = {
  [INPUT_SIZE.SMALL]: 1,
  [INPUT_SIZE.NORMAL]: 2,
  [INPUT_SIZE.BIG]: 4
}
export default {
  name: 'InputEle',
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      ROW_ENUM
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('InputEle----', this.ele)
  }
}
</script>

