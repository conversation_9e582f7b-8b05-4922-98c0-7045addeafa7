<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div class="m-top">
      <div
        class="idx"
        :class="{
          required:item.required
        }"
      >
        {{ idx }}
      </div>
      <div class="title">
        <UmInlineEdit v-model="item.title" is-title />
      </div>
    </div>
    <div class="m-content m-content_edit dragable">
      <DragSort :ele="item" handle=".option-r__move">
        <div v-for="(_item, index) in item.option_list.filter(item => item.optionType !== OPTION_TYPE.REFUSE && item.optionType !== OPTION_TYPE.UNKNOW && item.optionType !== OPTION_TYPE.OTHER)" :key="_item.option_id" class="option">
          <div class="option-l"><div class="circle" /><UmInlineEdit v-model="_item.title" :readonly="_item.optionType === OPTION_TYPE.OTHER" /></div>
          <div class="option-r">
            <i class="um_iconfont option-r__move">&#xe6bc;</i>
            <i class="um_iconfont" @click="delOption(index, _item.option_id)">&#xe678;</i>
          </div>
        </div>
      </DragSort>
      <div v-for="(_item) in item.option_list.filter(item => item.optionType=== OPTION_TYPE.OTHER)" :key="_item.option_id" class="option">
        <div class="option-l"><div class="circle" /><UmInlineEdit v-model="_item.title" :readonly="_item.optionType === OPTION_TYPE.OTHER" /></div>
        <div class="option-r">
          <i class="um_iconfont" @click="delOption(item.option_list.length - 1, _item.option_id)">&#xe678;</i>
        </div>
      </div>
      <ToolBar :ele="item" />
    </div>
  </div>
</template>

<script>
import ToolBar from '../components/ToolBar'
import DragSort from '../components/DragSort'
import { OPTION_TYPE } from '../core/unit'
export default {
  name: 'RadioEle',
  components: { ToolBar, DragSort },
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    },
    checkDeleteOption: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return { OPTION_TYPE }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('RadioEle----', this.ele)
  },
  methods: {
    async delOption(index, option_id) {
      try {
        await this.checkDeleteOption(this.item.question_id, option_id)
        this.item.option_list.splice(index, 1)
      } catch (error) {
        this.$message.error(error)
      }
    }
  }
}
</script>

<style lang="scss" scoped>

</style>

