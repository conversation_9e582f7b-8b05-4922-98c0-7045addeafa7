<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div class="m-top">
      <div
        class="idx"
        :class="{
          required:item.required
        }"
      >
        {{ idx }}
      </div>
      <div class="title">
        <UmInlineEdit v-model="item.title" is-title />
      </div>
    </div>
    <div class="m-content m-content_edit grade">
      <div v-for="_item in item.option_list.filter(item => item.optionType !== OPTION_TYPE.REFUSE && item.optionType !== OPTION_TYPE.UNKNOW)" :key="_item.option_id" class="grade-item um_iconfont">
        <span class="unicode">{{ String.fromCharCode(parseInt(item.style, 16)) }}</span>
        <span>{{ _item.score }}分</span>
      </div>
    </div>
  </div>
</template>

<script>
import { OPTION_TYPE } from '../core/unit'
export default {
  name: 'GradeEle',
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    }
  },
  data() {
    return { OPTION_TYPE }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('GradeEle----', this.ele)
  }
}
</script>

