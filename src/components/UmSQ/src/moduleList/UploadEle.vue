<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div class="m-top">
      <div
        class="idx"
        :class="{
          required:item.required
        }"
      >
        {{ idx }}
      </div>
      <div class="title"><UmInlineEdit v-model="item.title" is-title /></div>
    </div>
    <div class="m-content">
      <el-input class="m-content__input" type="textarea" :rows="4" maxlength="500" resize="none" :placeholder="item.placeholder" readonly />
      <um-upload-file v-model="item.fileList" style="pointer-events: none;" :size="1024 * 100" :limit="item.limit" />
    </div>
  </div>
</template>

<script>
export default {
  name: 'UploadEle',
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('UploadEle----', this.ele)
  }
}
</script>

<style lang="scss" scoped>
.m-content__input {
  margin-bottom: 10px;
}
</style>

