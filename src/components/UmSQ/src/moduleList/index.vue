<script>
import RadioEle from './RadioEle'
import CheckBoxEle from './CheckBoxEle'
import InputEle from './InputEle'
import GradeEle from './GradeEle'
import TextEle from './TextEle'
import UploadEle from './UploadEle'
import { QUESTION_TYPE } from '../core/unit'
export default {
  name: 'ModuleList',
  functional: true,
  render(createElement, ctx) {
    const { ele } = ctx.props
    switch (ele.type) {
      case QUESTION_TYPE.RADIO:
        return (<RadioEle {...ctx.data} id={ele.question_id}></RadioEle>)
      case QUESTION_TYPE.CHECKBOX:
        return (<CheckBoxEle {...ctx.data} id={ele.question_id}></CheckBoxEle>)
      case QUESTION_TYPE.INPUT:
        return (<InputEle {...ctx.data} id={ele.question_id}></InputEle>)
      case QUESTION_TYPE.GRADE:
        return (<GradeEle {...ctx.data} id={ele.question_id}></GradeEle>)
      case QUESTION_TYPE.TEXT:
        return (<TextEle {...ctx.data} id={ele.question_id}></TextEle>)
      case QUESTION_TYPE.UPLOAD:
        return (<UploadEle {...ctx.data} id={ele.question_id}></UploadEle>)
      default:
        return (<h1>不存在的组件!</h1>)
    }
  }
}
</script>
