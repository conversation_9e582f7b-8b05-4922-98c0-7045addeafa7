<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div
      class="m-content m-content_edit"
      :style="{
        textAlign: ele.align
      }"
    >
      <UmInlineEdit v-model="item.content" is-title />
    </div>
  </div>
</template>

<script>
export default {
  name: 'TextEle',
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('TextEle----', this.ele)
  }
}
</script>

