<template>
  <div class="m-ele" v-on="$listeners">
    <slot name="tool" />
    <div class="m-top">
      <div
        class="idx"
        :class="{
          required:item.required
        }"
      >
        {{ idx }}
      </div>
      <div class="title">
        <UmInlineEdit v-model="item.title" is-title />
      </div>
    </div>
    <!-- 调研问卷 -->
    <div v-if="typeCode === 1" class="m-content m-content_edit dragable">
      <DragSort :ele="item" handle=".option-r__move">
        <div v-for="(_item, index) in item.option_list.filter(_ => _.optionType !== OPTION_TYPE.OTHER)" :key="_item.option_id" class="option">
          <div class="option-l">
            <div class="square" />
            <UmInlineEdit v-model="_item.title" :readonly="_item.optionType === OPTION_TYPE.OTHER" />
          </div>
          <div class="option-r">
            <i class="um_iconfont option-r__move">&#xe6bc;</i>
            <i class="um_iconfont" @click="delOption(index, _item.option_id)">&#xe678;</i>
          </div>
        </div>
      </DragSort>
      <div v-for="(_item) in item.option_list.filter(_ => _.optionType === OPTION_TYPE.OTHER)" :key="_item.option_id" class="option">
        <div class="option-l">
          <div class="square" />
          <UmInlineEdit v-model="_item.title" :readonly="_item.optionType === OPTION_TYPE.OTHER" />
        </div>
        <div class="option-r">
          <i class="um_iconfont" @click="delOption(item.option_list.length - 1, _item.option_id)">&#xe678;</i>
        </div>
      </div>
      <ToolBar :ele="item" />
    </div>
    <!-- 深访问卷 -->
    <div v-else class="m-content m-content_edit dragable">
      <div v-for="(_item, groupIndex) in item.optionGroup" :key="_item.value">
        <div v-if="_item.children.length" class="group-title">{{ _item.label }}</div>
        <div v-for="(option, optionIndex) in getOptionChildren(_item.children)" :key="option.option_id" class="option" style="margin-left: 20px;">
          <div class="option-l">
            <div class="square" />
            <UmInlineEdit v-model="option.title" :readonly="option.optionType === OPTION_TYPE.OTHER" />
          </div>
          <div class="option-r">
            <i class="um_iconfont" @click="delGroupOption(groupIndex, optionIndex, option.option_id)">&#xe678;</i>
          </div>
        </div>
      </div>
      <div v-for="(_item) in item.option_list.filter(item => !item.disabled)" :key="_item.option_id" class="option">
        <div class="option-l">
          <div class="square" />
          <UmInlineEdit v-model="_item.title" :readonly="_item.optionType === OPTION_TYPE.OTHER" />
        </div>
        <div class="option-r">
          <i class="um_iconfont" @click="depthDelOption(_item.option_id)">&#xe678;</i>
        </div>
      </div>
      <ToolBar :ele="item" :one-ohter="false" />
    </div>
  </div>
</template>

<script>
import ToolBar from '../components/ToolBar'
import DragSort from '../components/DragSort'
import { OPTION_TYPE } from '../core/unit'
export default {
  name: 'CheckBoxEle',
  components: { ToolBar, DragSort },
  inject: ['typeCode'],
  props: {
    idx: { type: Number, default: 0 },
    ele: {
      type: Object,
      default: null
    },
    checkDeleteOption: {
      type: Function,
      default: () => {}
    }
  },
  data() {
    return {
      OPTION_TYPE
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  created() {
    console.log('CheckBoxEle----', this.ele)
  },
  methods: {
    getOptionChildren(children) {
      if (!children || !children.length) return []
      const arr = []
      children.forEach(item => {
        const index = this.item.option_list.findIndex(_item => _item.option_id === item.option_id)
        if (index !== -1) {
          arr.push(this.item.option_list[index])
        }
      })
      return arr
    },
    async delGroupOption(groupIndex, optionIndex, option_id) {
      try {
        await this.checkDeleteOption(this.item.question_id, option_id)
        this.item.delOption(option_id)
        this.item.delGroupOption(groupIndex, optionIndex)
      } catch (error) {
        this.$message.error(error)
      }
    },
    async depthDelOption(option_id) {
      try {
        await this.checkDeleteOption(this.item.question_id, option_id)
        this.item.delOption(option_id)
      } catch (error) {
        this.$message.error(error)
      }
    },
    async delOption(index, option_id) {
      try {
        await this.checkDeleteOption(this.item.question_id, option_id)
        this.item.option_list.splice(index, 1)
      } catch (error) {
        this.$message.error(error)
      }
    }
  }
}
</script>

