
<script>
import RadioEle from './RadioEle'
import CheckBoxEle from './CheckBoxEle'
import InputEle from './InputEle'
import GradeEle from './GradeEle'
import TextEle from './TextEle'
import UploadEle from './UploadEle'
import { QUESTION_TYPE } from '../core/unit'
export default {
  name: 'ModuleConfig',
  functional: true,
  render(createElement, ctx) {
    const { ele } = ctx.props
    switch (ele.type) {
      case QUESTION_TYPE.RADIO:
        return (<RadioEle {...ctx.data} ></RadioEle>)
      case QUESTION_TYPE.CHECKBOX:
        return (<CheckBoxEle {...ctx.data} ></CheckBoxEle>)
      case QUESTION_TYPE.INPUT:
        return (<InputEle {...ctx.data} ></InputEle>)
      case QUESTION_TYPE.GRADE:
        return (<GradeEle {...ctx.data} ></GradeEle>)
      case QUESTION_TYPE.TEXT:
        return (<TextEle {...ctx.data} ></TextEle>)
      case QUESTION_TYPE.UPLOAD:
        return (<UploadEle {...ctx.data} ></UploadEle>)
      default:
        return (<h1>不存在的组件!</h1>)
    }
  }
}
</script>
