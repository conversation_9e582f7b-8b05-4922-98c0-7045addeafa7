<script>
import { INPUT_SIZE } from '../core/unit'
export default {
  name: 'InputEle',
  props: {
    ele: {
      type: Object,
      default: null
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  render(createElement, context) {
    return (
      <div>
        <div class="m-form">
          <div class="m-label">是否必答</div>
          <div class="m-value">
            <el-switch value={this.item.required} onInput={val => { this.item.required = val }}/>
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">提示文字</div>
          <div class="m-value">
            <el-input
              placeholder="请输入"
              value={this.item.placeholder}
              onInput={val => { this.item.placeholder = val }}
              maxlength="15"
              clearable
            />
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">输入框大小</div>
          <div class="m-value">
            <el-radio-group
              value={this.item.size}
              onInput={val => { this.item.size = val }}
            >
              <el-radio label={INPUT_SIZE.BIG}>大</el-radio>
              <el-radio label={INPUT_SIZE.NORMAL}>中</el-radio>
              <el-radio label={INPUT_SIZE.SMALL}>小</el-radio>
            </el-radio-group>
          </div>
        </div>
      </div>
    )
  }
}
</script>

