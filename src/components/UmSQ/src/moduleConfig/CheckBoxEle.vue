<script>
import OptionGroup from '../components/OptionGroup'
export default {
  name: 'CheckBoxEle',
  components: { OptionGroup },
  inject: ['changeTab', 'typeCode'],
  props: {
    ele: {
      type: Object,
      default: null
    },
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      optionGroupVisible: false
    }
  },
  computed: {
    item() {
      return this.ele
    },
    // 一份问卷只能有一个痛点问题
    disabledPain() {
      return this.config?.pages.some(item => item.painFlag) && !this.item.painFlag
    }
  },
  methods: {
    handleVisibleChange(val) {
      this.optionGroupVisible = val
    },
    handleGroupConfirm({ optionGroup, groupOptionIds, optionList }) {
      this.item.optionGroup = optionGroup
      this.item.groupOptionIds = groupOptionIds
      this.item.option_list = optionList
    }
  },
  render() {
    return <div>
      <div class="m-form">
        <div class="m-label">是否必答</div>
        <div class="m-value">
          <el-switch value={this.item.required} onInput={val => { this.item.required = val }}/>
        </div>
      </div>
      {/* 深访问卷 */}
      { this.typeCode === 2 && <div>
        <div class="m-form m-form-column">
          <div class="m-value between">最多选择
            <el-input-number value={this.item.maxSelectCount} placeholder="请输入" precision={0} min={0} max={this.ele.option_list.length} controls={false} style="width: 150px;margin: 0 12px" onChange={count => {
              this.item.maxSelectCount = count
            }}>
            </el-input-number>
            <span>项</span>
          </div>
          <span class="tip">输入0为不限制选项数量</span>
        </div>
        <div class="m-form">
          <div class="m-label">是否设为痛点问题</div>
          <div class="m-value">
            <el-switch disabled={this.disabledPain} value={this.item.painFlag} onInput={val => {
              this.item.painFlag = val
            }} />
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">选项设置</div>
          <div class="m-value between" style="justify-content: space-between;">
            <span>选项分组</span>
            <el-button type="text" onClick={() => {
              this.optionGroupVisible = true
            }}>设置</el-button>
          </div>
        </div>
        <div class="m-form m-form-column" style="padding: 0;margin: 0"></div>
      </div>}
      <div class="m-form between">
        <div class="m-label">跳转逻辑</div>
        <div class="m-value"><el-button type="text" onClick={this.changeTab.bind(this, 1)}><i class="um_iconfont">&#xe690;</i> 添加跳转逻辑</el-button></div>
      </div>
      <OptionGroup
        visible={this.optionGroupVisible}
        optionList={this.ele.option_list}
        optionGroup={this.ele.optionGroup}
        groupOptionIds={this.ele.groupOptionIds}
        onClose={this.handleVisibleChange}
        onConfirm={this.handleGroupConfirm}
      />
    </div>
  }
}
</script>

