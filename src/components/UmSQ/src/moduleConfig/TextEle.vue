<script>
export default {
  name: 'TextEle',
  props: {
    ele: {
      type: Object,
      default: null
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  render() {
    return <div class="m-form m-form-column flex-row-start">
      <div class="m-label">位置</div>
      <div class="m-value">
        <el-radio-group
          value={this.item.align}
          onInput={val => { this.item.align = val }}
        >
          <el-radio label="left">局左</el-radio>
          <el-radio label="center">局中</el-radio>
          <el-radio label="right">局右</el-radio>
        </el-radio-group>
      </div>
    </div>
  }
}
</script>

