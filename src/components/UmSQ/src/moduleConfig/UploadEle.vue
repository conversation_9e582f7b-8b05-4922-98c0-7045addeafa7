<script>
export default {
  name: 'UploadEle',
  props: {
    ele: {
      type: Object,
      default: null
    }
  },
  computed: {
    item() {
      return this.ele
    }
  },
  render(createElement, context) {
    return (
      <div>
        <div class="m-form">
          <div class="m-label">是否必答</div>
          <div class="m-value">
            <el-switch value={this.item.required} onInput={val => { this.item.required = val }}/>
          </div>
        </div>
        <div class="m-form">
          <div class="m-label">是否附件必传</div>
          <div class="m-value">
            <el-switch value={this.item.fileRequired} onInput={val => { this.item.fileRequired = val }}/>
          </div>
        </div>
        <div class="m-form m-form-column">
          <div class="m-label">允许上传数量</div>
          <div class="m-value">
            <el-input-number value={this.item.limit} onChange={val => { this.item.limit = val }} controls={false} min={1} max={20}/> 个
          </div>
        </div>
        <div class="m-form">
          <div class="m-label">点赞和吐槽模式</div>
          <div class="m-value">
            <el-switch value={this.item.prMode} onInput={val => { this.item.prMode = val }}/>
          </div>
        </div>
      </div>
    )
  }
}
</script>

