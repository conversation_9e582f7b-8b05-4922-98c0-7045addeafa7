<template>
  <div v-if="config" class="um-sq">
    <div v-show="!isPreview" class="um-sq_header">
      <div class="m-left">
        <div class="u-back" @click="$router.back()">
          <i class="um_iconfont">&#xe64b;</i>
        </div>
        <div v-if="!isEditTitle" class="u-title" :title="title">{{ title }}</div>
        <el-row v-else class="m-left__center" :gutter="20">
          <el-col :span="24">
            <el-input ref="titleInput" v-model="editTitle" autofocus class="u-title input" clearable @blur="editTitleBlur" />
          </el-col>
          <!-- <el-col :span="8"><el-button type="primary" @click="editTitleBlur">确认</el-button></el-col> -->
        </el-row>
        <div v-if="!isEditTitle" class="u-edit" @click="handleEditTitle">
          <i class="um_iconfont">&#xe63c;</i>
        </div>
      </div>
      <div class="m-center">
        <div class="m-tabs">
          <div class="u-tab" :class="{on:tab===0}" @click="changeHeadTab(0)">编辑问卷</div>
          <div class="u-tab" :class="{on:tab===1}" @click="changeHeadTab(1)">逻辑设置</div>
          <div class="line" />
        </div>
      </div>
      <div class="m-right">
        <el-button type="primary" @click="save(2200091)">存草稿</el-button>
        <el-button type="primary" @click="preview">预览</el-button>
        <el-button type="primary" @click="save(2200092)">发布</el-button>
      </div>
    </div>
    <!--逻辑设置-->
    <conditionConfig v-if="tab===1 && !isPreview" :config="config" />
    <!--编辑问卷-->
    <div v-show="tab===0" class="um-sq_main">
      <div class="um-sq_left">
        <div class="m-caption">
          <div class="m-caption__item" :class="{ active: leftType === 0 }" @click="leftType = 0">组件</div>
          <div class="m-caption__item" :class="{ active: leftType === 1 }" @click="leftType = 1">题库</div>
        </div>
        <draggable
          v-show="leftType === 0"
          class="m-list"
          :list="units"
          :group="{ name: 'unit', pull: 'clone', put: false }"
          :clone="cloneDog"
          :move="throttle(checkMove, 10000)"
          :sort="false"
        >
          <div v-for="(item,idx) in units" :key="idx" class="m-unit">
            <div class="name">{{ item.name }}</div>
            <div :class="['icon','icon-'+item.type]" />
          </div>
        </draggable>
        <QuestionBank v-show="leftType === 1" ref="questionBank" @success="appendQuestion" />
      </div>
      <div ref="umSqContent" class="um-sq_content" @scroll="setEditorBlur">
        <div class="m-header">
          <div class="title">
            <UmInlineEdit v-model="config.config.title" is-title />
          </div>
          <div class="content">
            <UmInlineEdit v-model="config.config.desc" is-title :max-length="1000" />
          </div>
        </div>
        <div ref="umSQComponents" class="um-sq_components">
          <draggable
            class="um-sq_pages"
            group="unit"
            ghost-class="ghost-item"
            :animation="200"
            :list="config.pages"
            handle=".u-move"
            :disabled="pages.length >= 6"
            @add="addHandler"
            @start="startHandler"
            @end="endHandler"
            @remove="removeHandler"
            @update="updateHandler"
            @choose="chooseHandler"
            @unchoose="unchooseHandler"
            @sort="sortHandler"
            @filter="filterHandler"
            @clone="cloneHandler"
          >
            <moduleList
              v-for="(item,idx) in config.pages"
              :key="item.question_id"
              :idx="pages[item.question_id]&&pages[item.question_id].idx"
              :pages="pages"
              :ele="item"
              :class="{
                active:currentItem===item
              }"
              :check-delete-option="config.checkDeleteOption.bind(config)"
              @click="currentItem=item"
            >
              <template #tool>
                <div class="m-tool">
                  <div v-if="item.type !== QUESTION_TYPE.TEXT" class="u-move">
                    <el-tooltip effect="dark" content="添加到题库" placement="top">
                      <i class="el-icon-document-add" @click="questionData = item;addQuestionShow = true;" />
                    </el-tooltip>
                  </div>
                  <div class="u-move"><i class="um_iconfont">&#xe6bc;</i></div>
                  <div class="u-delete" @click="delQuestion(idx, item)"><i class="um_iconfont">&#xe678;</i></div>
                </div>
                <div v-if="item.targetName" class="u-move">
                  <el-tooltip v-model="item.showTip" effect="dark" :content="`指标“${item.targetName}已删除，请重新编辑问卷。`" placement="right">
                    <i class="el-icon-warning danger" @click="questionData = item;addQuestionShow = true;" />
                  </el-tooltip>
                </div>
              </template>
            </moduleList>
            <div v-if="config.pages.length === 0" key="none" class="m-none">
              请拖入左侧题型
            </div>
          </draggable>
        </div>
        <!-- <div class="m-footer">
            <div class="content">
              <UmInlineEdit v-model="config.config.footer" />
            </div>
          </div> -->
        <div v-if="config.pages.length!==0" class="m-end">
          <el-row class="title" type="flex" justify="center" align="middle">
            <el-select v-model="config.config.completeMode" placeholder="请选择">
              <el-option
                v-for="item in footOptions"
                :key="item.value"
                :label="item.label"
                :value="item.value"
              />
            </el-select>
            <div class="ml12">时显示</div>
          </el-row>
          <!-- <div class="content">您已完成本次问卷，感谢您的帮助与支持</div> -->
          <UmInlineEdit v-if="config.config.completeMode === 0" v-model="config.config.footer" />
          <UmInlineEdit v-else v-model="config.config.earlyFooter" />
        </div>
      </div>
      <div class="um-sq_right">
        <div class="m-tabs">
          <div
            class="u-tab"
            :class="{
              on:activeTab===0
            }"
            @click="changeTab(0)"
          >
            全局设置
          </div>
          <div
            class="u-tab"
            :class="{
              on:activeTab===1,
              none:!currentItem
            }"
            @click="changeTab(1)"
          >
            {{ tabTitle }}设置
          </div>
        </div>
        <template v-if="activeTab===0">
          <div class="m-form">
            <div class="m-label">题目序号</div>
            <div class="m-value">
              <el-switch v-model="config.config.hasIndex" />
            </div>
          </div>
          <div class="m-form">
            <div class="m-label">一页一题</div>
            <div class="m-value">
              <el-switch v-model="config.config.pageMode" :active-value="1" :inactive-value="0" />
            </div>
          </div>
        </template>
        <template v-if="activeTab===1">
          <moduleConfig :ele="currentItem" :config="config" class="um-sq_right__content" />
        </template>
      </div>
    </div>
    <Preview v-if="isPreview" :preview-uuid="previewUuid" :config="config" :type-code="typeCode" :title="title" class="isEditor" @goConfig="isPreview = false; config.answers = {}" />
    <AddQuestion :show.sync="addQuestionShow" :question-data="questionData" @success="$refs.questionBank && $refs.questionBank.getTreeData()" />
    <EditScene :visible.sync="showEditScene" @success="addSceneSuccess" />
  </div>
</template>

<script>
import units, { QUESTION_TYPE, MAX_QUESTION_LENGTH, QUESTION_TYPE_CODE, dynamicData } from './core/unit'
import draggable from 'vuedraggable'
import { RadioEle, CheckBoxEle, InputEle, GradeEle, TextEle, UploadEle, Config } from './core/factory'
import moduleList from './moduleList'
import moduleConfig from './moduleConfig'
import conditionConfig from './conditionConfig'
import { getTargetTree } from '@/api/base/indicator/index'
import { saveQuesTemplate, editQuesTemplate, getQuesTemplateDetail, savePreviewToRedis, getQuestionListByQuesTemplateId } from '@/api/wenjuan'
import { htmlToText } from './utils'
// import TestJSON from './core/test.json'
import lodash from 'lodash'
import Preview from '../../../views/wenjuan/preview/index'
import { scrollTo, getScroller } from '@/utils/scroll-to'
import QuestionBank from './questionBank'
import AddQuestion from './components/AddQuestion'
import EditScene from '../../../views/wenjuan/components/EditScene'
const SPECIAL_BYTE = {
  '&amp;': '＆',
  '&lt;': '<',
  '&gt;': '>',
  '&ldquo': '“',
  '&lsquo;': '‘',
  '&nbsp;': ' ',
  '&hellip;': '…',
  '&middot;': '·'
}
export default {
  name: 'UmSQ',
  components: { draggable, moduleList, moduleConfig, conditionConfig, Preview, QuestionBank, AddQuestion, EditScene },
  provide() {
    return {
      getPointList: () => this.pointList, // 绑定的指标数据
      changeTab: (index) => {
        this.changeHeadTab(index)
      },
      typeCode: this.typeCode
    }
  },
  props: {
    id: { // 问卷的ID
      type: [String, Number],
      default: null
    },
    typeCode: {
      type: Number,
      default: 1 // 1: 调研问卷 2: 深访问卷
    }
  },
  data() {
    return {
      units,
      QUESTION_TYPE,
      tab: 0, // 0 编辑问卷 1 逻辑设置
      demo: {},
      config: null,
      drag: false, // 是否拖拽中
      currentItem: null, // 当选选中的元素
      activeTab: 0, // 0 全局 1 打分题
      pointList: [],
      title: '',
      editTitle: '',
      isEditTitle: false,
      canEditTitle: true, // 是否修改问卷标题联动修改title，默认可以修改
      addQuestionShow: false,
      questionData: null,
      throttle: lodash.throttle,
      isPreview: false,
      quesTemplateId: null,
      previewUuid: null,
      initConfig: null,
      footOptions: [{
        value: 0,
        label: '正常完成'
      }, {
        value: 1,
        label: '提前结束（不计入结果）'
      }],
      footerValue: 0,
      leftType: 0,
      isOperateSucces: false, // 操作成功返回页面，此时返回上一页，不做弹窗提示
      showEditScene: false // 没有绑定触点需要带上触点
    }
  },
  computed: {
    pages() { // 除去文本描述的题目集合，主要用来显示序号
      if (this.config) {
        const obj = {}
        this.config.pages.filter(i => i.type !== 5).forEach((item, idx) => {
          obj[item.question_id] = item
          obj[item.question_id].idx = idx + 1
        })
        return obj
      }
      return {}
    },
    tabTitle() {
      return this.currentItem ? units.filter(item => item.type === this.currentItem.type)[0].name : '打分题'
    }
  },
  watch: {
    currentItem(val) {
      if (val) {
        this.activeTab = 1
      }
    },
    'config.config.title'(v) {
      if (!this.canEditTitle || !v) return
      let text = htmlToText(v)
      Object.keys(SPECIAL_BYTE).forEach(key => {
        text = text.replaceAll(key, SPECIAL_BYTE[key]).trim()
      })
      this.title = text
    },
    id: {
      immediate: true,
      handler(v) {
        this.quesTemplateId = v
      }
    }
  },
  created() {
    window.addEventListener('beforeunload', this.beforeUnload)
  },
  beforeDestroy() {
    window.removeEventListener('beforeunload', this.beforeUnload)
  },
  async mounted() {
    if (this.quesTemplateId) {
      const load = this.$load()
      try {
        const res = await this.getQuesTemplateDetail()
        this.getQuestionListByQuesTemplateId()
        const templateJSON = JSON.parse(res.data.templateJson)
        this.config = new Config(templateJSON)
        const text = htmlToText(this.config.config.title)
        const templateName = res.data.templateName || text
        this.canEditTitle = text === templateName // 如果标题和模版JSON里面的一样，表示可以联动修改
        this.title = templateName
      } catch (error) {
        this.config = new Config()
      } finally {
        load.close()
      }
    } else {
      // 初始化默认数据
      this.config = new Config()
    }
    this.initConfig = JSON.stringify(this.config)
    this.getTargetTree()
  },
  methods: {
    beforeUnload(event) {
      const flag = this.initConfig !== JSON.stringify(this.config)
      if (flag && event) {
        event.preventDefault()
        event.returnValue = ''
      }
      return flag
    },
    // 中间滚动，取消编辑器聚焦
    setEditorBlur() {
      return this.throttle(() => {
        document.querySelectorAll('.mce-content-body').forEach(item => {
          item.blur()
        })
      }, 1000)()
    },
    handleEditTitle() {
      this.isEditTitle = true
      this.editTitle = this.title
      this.$nextTick(() => {
        this.$refs.titleInput.focus()
      })
    },
    changeTab(idx) {
      if (idx === 1 && !this.currentItem) {
        return false
      }
      this.activeTab = idx
    },
    checkMove() {
      const { pages } = this.config
      if (pages.length >= MAX_QUESTION_LENGTH) {
        this.$message.warning('最多只能添加' + MAX_QUESTION_LENGTH + '道题！')
        return false
      }
      return true
    },
    cloneDog(item) {
      const { type } = item
      switch (type) {
        case QUESTION_TYPE.RADIO:
          return new RadioEle()
        case QUESTION_TYPE.CHECKBOX:
          // 深访问卷，第一个多选题默认开始痛点选项
          if (this.typeCode === 2 && !this.config.pages.filter(item => item.type === QUESTION_TYPE.CHECKBOX).length) {
            const ele = new CheckBoxEle()
            ele.painFlag = true
            return ele
          }
          return new CheckBoxEle()
        case QUESTION_TYPE.INPUT:
          return new InputEle()
        case QUESTION_TYPE.GRADE:
          return new GradeEle()
        case QUESTION_TYPE.TEXT:
          return new TextEle()
        case QUESTION_TYPE.UPLOAD:
          return new UploadEle()
        default:
          return new InputEle()
      }
    },
    addHandler({ newIndex }) {
      this.currentItem = this.config.pages[newIndex]
    },
    startHandler() { console.log('startHandler'); this.drag = true },
    endHandler() { console.log('endHandler'); this.drag = false },
    removeHandler() { console.log('removeHandler') },
    updateHandler() { console.log('updateHandler') },
    chooseHandler(CustomEvent) {
      console.log('chooseHandler')
      // const { oldIndex } = CustomEvent
      // this.currentItem = this.config.pages[oldIndex]
    },
    // 题库中的题目添加到编辑器中
    appendQuestion(data) {
      let ques = null
      switch (data.type) {
        case QUESTION_TYPE.RADIO: ques = new RadioEle(data); break
        case QUESTION_TYPE.CHECKBOX:
          // 深访问卷，第一个多选题默认开始痛点选项
          if (this.typeCode === 2 && !this.config.pages.filter(item => item.type === QUESTION_TYPE.CHECKBOX).length) {
            data.painFlag = true
          }
          ques = new CheckBoxEle(data); break
        case QUESTION_TYPE.INPUT: ques = new InputEle(data); break
        case QUESTION_TYPE.GRADE: ques = new GradeEle(data); break
        case QUESTION_TYPE.TEXT: ques = new TextEle(data); break
        case QUESTION_TYPE.UPLOAD: ques = new UploadEle(data); break
        default: break
      }
      this.config.pages.push(ques)
      const scollContainer = getScroller(this.$refs.umSqContent)
      const umSQComponents = this.$refs.umSQComponents
      scrollTo(scollContainer, umSQComponents.offsetHeight, 500)
    },
    unchooseHandler() { console.log('unchooseHandler') },
    // 拖拽排序检测
    async sortHandler(event) {
      const currentQuestion = this.config.pages[event.oldIndex]
      const targetQuestion = this.config.pages[event.newIndex]
      if (!targetQuestion || !currentQuestion) return
      const target = targetQuestion.question_id
      const current = currentQuestion.question_id

      try {
        await this.config.checkMove(current, target)
      } catch (error) {
        this.$nextTick(() => {
          [this.config.pages[event.oldIndex], this.config.pages[event.newIndex]] = [this.config.pages[event.newIndex], this.config.pages[event.oldIndex]]
          const oldIdx = this.config.pages[event.oldIndex].idx
          this.config.pages[event.oldIndex].idx = this.config.pages[event.newIndex].idx
          this.config.pages[event.newIndex].idx = oldIdx
          this.$forceUpdate()
          this.$message.error(error)
        })
      }
    },
    filterHandler() { console.log('filterHandler') },
    cloneHandler() { console.log('cloneHandler') },
    addSceneSuccess(data) {
      this.save(2200092, false, data)
    },
    /**
     * 根据题目字符串获取添加的动态信息给后端
     * @param {String} str
     * @param {Set} set
     */
    getDynamicInfo(str, set) {
      if (set.size === 4) return set
      Object.keys(dynamicData).forEach(key => {
        if (str.indexOf(key) !== -1) {
          set.add(dynamicData[key])
        }
      })
      return set
    },
    /**
     *
     * @param {Number} templateStatusCode  2200091：保存为草稿  2200092：发布
     * @param {Boolean} isPreview  是否预览
     */
    save(templateStatusCode, isPreview = false, pointIdList) {
      return new Promise((resolve, reject) => {
        if (!this.title || !this.config.config.title) {
          this.$message.warning('请设置问卷标题！')
          reject()
          return
        }
        if (!this.title || !this.config.config.footer) {
          this.$message.warning('请设置提交时显示内容！')
          reject()
          return
        }
        if (!this.config.pages.length) {
          this.$message.warning(isPreview ? '请添加题目后再预览！' : '请添加题目后再保存草稿或者发布！')
          reject()
          return
        }
        if (!this.checkEmpty()) {
          reject()
          return
        }
        if (document.querySelectorAll('.um-inline_edit--error').length) {
          this.$message.error('内容长度不可超过200个字符！')
          reject()
          return
        }
        const load = this.$load()
        if (isPreview) {
          savePreviewToRedis({
            templateJson: JSON.stringify(this.config),
            previewUuid: this.previewUuid
          }).then(res => {
            this.previewUuid = res.data.previewUuid
            resolve()
          }).catch(() => {
            reject()
          }).finally(() => {
            load.close()
          })
          return
        }
        if (!this.$route.query.hasPoint && templateStatusCode === 2200092 && !pointIdList?.length && this.typeCode === 1) {
          this.showEditScene = true
          load.close()
          return
        }
        this.config.updated = new Date()
        this.config.saveQuestion()
        const API = this.quesTemplateId ? editQuesTemplate : saveQuesTemplate
        // 处理动态信息
        const dynamicCodeSet = new Set()
        // 处理逻辑设置选项被创建操作
        const conditionConfigQuestion_id = this.config.conditionConfig.map(it => it.question_id)
        this.config.pages.filter(item => [QUESTION_TYPE.RADIO, QUESTION_TYPE.CHECKBOX, QUESTION_TYPE.GRADE].includes(item.type)).forEach(page => {
          if (!conditionConfigQuestion_id.includes(page.question_id)) {
            page.option_list.forEach(option => {
              option.target = null
            })
          } else {
            this.config.conditionConfig.forEach(condition => {
              if (page.question_id === condition.question_id) {
                page.option_list.forEach(option => {
                  if (condition.question_value.includes(option.option_id)) {
                    option.target = condition.target
                  } else {
                    option.target = null
                  }
                })
              }
            })
          }
        })
        this.getDynamicInfo(JSON.stringify(this.config), dynamicCodeSet)
        // 处理问题列表
        const templateQuestionList = this.config.pages.map(item => {
          let optionList = null
          if (Array.isArray(item.option_list)) {
            optionList = item.type === QUESTION_TYPE.GRADE ? lodash.cloneDeep(item.option_list).reverse() : item.option_list
          }
          const questionName = htmlToText(item.title || item.content).replace(/&nbsp;/g, '').trim()
          // 处理问题上的动态信息
          this.getDynamicInfo(questionName, dynamicCodeSet)
          return {
            clientQuestionId: item.question_id,
            questionName,
            questionTypeCode: QUESTION_TYPE_CODE[item.type],
            quesTartgetId: item.kpi?.id,
            sort: item.idx,
            examineFlag: item.isExamine,
            checkFlag: item.isCheck,
            inputTypeCode: item.size,
            painFlag: +item.painFlag,
            likeFlag: +item.prMode,
            templateQuestionItemList: optionList ? optionList.map(_item => {
              return {
                clientItemId: _item.option_id,
                itemName: htmlToText(_item.title).replace(/&nbsp;/g, '').trim(),
                itemScore: _item.score,
                itemTypeName: _item.itemTypeName,
                optionTypeCode: _item.optionType,
                sort: _item.sort,
                isCreateFlag: _item.target === 4 ? 1 : 0 // true: 创建逻辑设置选项，false：其他
              }
            }) : null
          }
        })
        API({
          templateJson: JSON.stringify(this.config),
          templateStatusCode,
          typeCode: this.typeCode,
          quesTemplateId: this.quesTemplateId,
          templateName: this.title,
          pointIdList,
          dynamicCodeStr: JSON.stringify(Array.from(dynamicCodeSet)),
          templateQuestionList
        }).then(res => {
          this.$alert('操作成功！', '提示', {
            type: 'success',
            showClose: false
          }).then(d => {
            this.isOperateSucces = true
            this.$router.back()
          })
        }).catch(() => {
          reject()
        }).finally(() => {
          load.close()
        })
      })
    },
    /**
     * @description 检测题目或者选项内容不能为空
     * @returns {Boolean}
     */
    checkEmpty() {
      const pages = this.config.pages
      let id = null
      try {
        pages.forEach((item, index) => {
          if (item.type === QUESTION_TYPE.INPUT || item.type === QUESTION_TYPE.UPLOAD) { // 填空题只有标题
            if (!item.title) {
              id = item.question_id
              throw new Error(`请输入第${item.idx}题标题`)
            }
          } else if (item.type === QUESTION_TYPE.TEXT) { // 文本题只有内容
            if (!item.content) {
              id = item.question_id
              throw new Error('请输入文本描述题内容')
            }
          } else {
            const options = item.option_list
            if (!item.title) {
              id = item.question_id
              throw new Error(`请输入第${item.idx}题标题`)
            }
            if (!options.length) {
              id = item.question_id
              throw new Error(`请为第${item.idx}题设置选项`)
            }
            options.forEach((_item, _index) => {
              if (!_item.title) {
                id = item.question_id
                throw new Error(`请为第${item.idx}题第${_index + 1}个选项输入内容`)
              }
            })
          }
        })
        return true
      } catch (error) {
        const scollContainer = getScroller(this.$refs.umSqContent)
        const dom = document.getElementById(`${id}`)
        scrollTo(scollContainer, dom.offsetTop - 20, 500)
        this.$message.warning(error)
        return false
      }
    },
    // 预览
    async preview() {
      try {
        await this.save(2200091, true)
        this.isPreview = true
      } catch (error) {
        console.log(error)
      }
    },
    async changeHeadTab(index) {
      if (!this.config.pages.length && index !== this.tab) {
        this.$message.warning('请添加题目后再设置逻辑！')
        return
      }
      if (!this.checkEmpty()) {
        return
      }
      if (index === 1) {
        this.config.sortPages()
      }
      this.tab = index
    },
    editTitleBlur() {
      if (!this.editTitle) {
        this.$message.warning('请输入问卷标题！')
        return
      }
      this.isEditTitle = false
      if (this.title === this.editTitle) return
      this.title = this.editTitle
      this.canEditTitle = false
    },
    // 获取指标树
    getTargetTree() {
      getTargetTree({ disableFlag: null }).then(res => {
        const arr = res.data || []
        this.pointList = arr
      })
    },
    // 删除题目
    async delQuestion(idx, item) {
      try {
        await this.config.checkDelete(item.question_id)
        this.config.pages.splice(idx, 1)
      } catch (error) {
        this.$message.error(error)
      }
    },
    getQuesTemplateDetail() {
      return getQuesTemplateDetail({ quesTemplateId: this.quesTemplateId })
    },
    // 根据问卷id查询所有问题
    getQuestionListByQuesTemplateId() {
      getQuestionListByQuesTemplateId({
        quesTemplateId: this.quesTemplateId
      }).then(res => {
        const arr = res.data || []
        arr.forEach(item => {
          if (item.targetDeleteFlag) {
            const index = this.config.pages.findIndex(_item => _item.question_id === item.clientQuestionId)
            index !== -1 && (this.config.pages[index].targetName = item.targetName)
            index !== -1 && (this.config.pages[index].showTip = true)
          }
        })
      })
    }
  }
}
</script>
