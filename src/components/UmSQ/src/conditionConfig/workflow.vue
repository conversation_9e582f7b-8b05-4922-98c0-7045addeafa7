<template>
  <div>
    <svg ref="svg" width="600" height="800"><g /></svg>
    <div ref="tips" class="tooltip">
      <ul class="ul">
        <li v-for="(item,idx) in formatMsg(condition)" :key="idx">
          <span v-if="item.xh" class="xh">{{ item.xh }}.</span>
          <span class="question_name">{{ item.question_name }}</span>
          <span class="type">{{ item.type }}</span>
          <span class="option_names">{{ item.option_names.join(',') }}</span>
          <span class="target">{{ item.target }}</span>
          <span class="target_question_name">{{ item.target_question_name }}</span>
        </li>
      </ul>
    </div>
  </div>
</template>

<script>
import { htmlToText } from '../utils'
import { createPopper } from '@popperjs/core'
import { internalCondition, endPageList } from '../core/factory'
const TYPE_ENUM = {
  1: '选中'
}
const TARGET_ENUM = {
  1: '跳转',
  2: '显示',
  3: '隐藏'
}
export default {
  name: 'Workflow',
  props: {
    pages: { // 题目
      type: Array,
      default: () => []
    },
    conditionEffect: { // 有效的条件
      type: Array,
      default: () => []
    },
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      internalCondition,
      endPageList,
      condition: null
    }
  },
  watch: {
    conditionEffect(val) {
      this.draw()
    }
  },
  created() {},
  mounted() {
    this.config.sortPages()
  },
  methods: {
    // 格式化条件信息
    formatMsg(condition) {
      if (!condition) return []
      // 处理条件文案
      const list = []
      const pages = this.pages.concat(internalCondition, endPageList)
      condition.forEach(item => {
        const { question_id, question_value, type, target, target_question_id } = item
        const question = pages.find(i => i.question_id === question_id)
        const target_question = pages.find(i => target_question_id.includes(i.question_id))
        const option_list = question.option_list.filter(item => {
          return question_value.includes(item.option_id)
        })
        const option_names = option_list.map(i => htmlToText(i.title))
        const question_name = htmlToText(question.title)
        const target_question_name = htmlToText(
          target_question.type === 5 ? target_question.content : target_question.title
        )
        list.push({
          xh: question.xh,
          question_name,
          type: TYPE_ENUM[type],
          target: TARGET_ENUM[target],
          target_question_name,
          option_names
        })
      })
      return list
    },
    draw() {
      const conditionEffect = this.conditionEffect
      let arr = []
      conditionEffect.forEach(item => {
        // 提前结束
        if (item.target_question_id == 'AbortEnd') {
          arr.push(endPageList[0])
        }
        if (item.target_question_id == 'NormalEnd') {
          arr.push(endPageList[1])
        }
      })
      arr = Array.from(new Set(arr))
      const pages = this.pages.concat(arr)
      // eslint-disable-next-line no-undef
      const g = new dagreD3.graphlib.Graph().setGraph({
        rankdir: 'TB',
        edgesep: 10,
        ranksep: 48,
        marginx: 48,
        marginy: 28
      })
      pages.forEach(item => {
        let title = ''
        let label = ''
        if (item.type === 5) {
          title = htmlToText(item.content)
        } else {
          title = htmlToText(item.title)
        }
        if (item.xh) {
          label = `${item.xh}.${title}`
        } else {
          label = `${title}`
        }

        // 查到当前题有没有被设置为条件
        const condition = conditionEffect.find(i => i.target_question_id.includes(item.question_id))
        g.setNode(item.question_id, {
          label: 12 * label.length > 258 ? ''.concat(label.slice(0, 21), '...') : label,
          width: 258,
          height: 21,
          paddingLeft: 17,
          paddingRight: 17,
          class: `question-node-item question-node-item_${condition && condition.target}`,
          data: {
            label: label
          },
          rx: 2,
          ry: 2,
          labelStyle: '',
          id: 'logic_preview_qid_'.concat(item.question_id)
        })
      })

      // 默认顺序
      pages.reduce((prev, current) => {
        if (prev) {
          g.setEdge(
            prev.question_id, current.question_id,
            {
              class: 'path',
              style: 'stroke: #aaaaaa; fill: none; stroke-width: 2px;fill-opacity:1;stroke-opacity:1;opacity:1',
              arrowheadStyle: 'fill: #aaaaaa;',
              curve: d3.curveBasis
            }
          )
        }
        return current
      }, null)

      // 增加条件
      conditionEffect.forEach(item => {
        // 跳转逻辑
        if (item.target === 1) {
          g.setEdge(
            item.question_id, item.target_question_id, {
              class: 'path',
              style: 'stroke: #aaaaaa; fill: none; stroke-width: 2px;fill-opacity:1;stroke-opacity:1;opacity:1',
              arrowheadStyle: 'fill: #aaaaaa;',
              curve: d3.curveBasis
            }
          )
        }
      })

      // Add edges to the graph.

      const svg = d3.select(this.$refs.svg)
      const inner = svg.select('g')
      const zoom = d3.zoom().on('zoom', function() {
        inner.attr('transform', d3.event.transform)
      })
      svg.call(zoom)
      // eslint-disable-next-line no-undef
      const render = new dagreD3.render()
      // Run the renderer. This is what draws the final graph.
      render(inner, g)

      // Center the graph
      const initialScale = 1
      svg.call(zoom.transform, d3.zoomIdentity.translate((svg.attr('width') - g.graph().width * initialScale) / 2, 20).scale(initialScale))

      svg.attr('height', g.graph().height * initialScale + 40)

      inner.selectAll('g.node').each((item, idx, NodeList) => {
        this.showTips(item, idx, NodeList, 'node')
      })
      inner.selectAll('g.path').each((item, idx, NodeList) => {
        this.showTips(item, idx, NodeList, 'path')
      })
    },
    showTips(item, idx, NodeList, type) {
      let condition = []
      let option = {}
      const conditionEffect = this.conditionEffect
      if (type === 'node') {
        condition = conditionEffect.filter(i => i.target_question_id.includes(item))
        option = {
          placement: 'bottom',
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 10]
              }
            }
          ]
        }
      } else {
        condition = conditionEffect.filter(i => i.target_question_id.includes(item.w) && i.question_id === item.v)
        option = {
          placement: 'left',
          modifiers: [
            {
              name: 'offset',
              options: {
                offset: [0, 0]
              }
            }
          ]
        }
      }
      if (condition.length === 0) return false
      const popperInstance = createPopper(NodeList[idx], this.$refs.tips, option)

      NodeList[idx].addEventListener('mouseenter', (event) => {
        const rect = event.target.getBoundingClientRect()
        console.log(event, rect)
        // Make the tooltip visible
        this.$refs.tips.setAttribute('data-show', '')
        this.condition = condition

        // Enable the event listeners
        popperInstance.setOptions((options) => ({
          ...options,
          modifiers: [
            ...options.modifiers,
            {
              name: 'offset',
              options: {
                offset:
                  type === 'node' ? [0, 10] : [event.y - rect.y - rect.height / 2, rect.x - event.x + 20]
              }
            },
            { name: 'eventListeners', enabled: true }
          ]
        }))

        // Update its position
        popperInstance.update()
      })
      NodeList[idx].addEventListener('mouseleave', (...arg) => {
        // Hide the tooltip
        this.$refs.tips.removeAttribute('data-show')

        // Disable the event listeners
        popperInstance.setOptions((options) => ({
          ...options,
          modifiers: [
            ...options.modifiers,
            { name: 'eventListeners', enabled: false }
          ]
        }))
      })
    }
  }
}
</script>
<style lang="scss" scoped>
.tooltip {
  color: #484848;
  background: #fff;
  box-shadow: 0 2px 4px 0 rgba(0,111,242,.15);
  border: 1px solid #97bbff;
  //display: none;
  opacity: 0;
  transition:opacity 300ms ;
  padding: 10px;
  border-radius: 4px;
  word-break: break-word;
  .ul{
    list-style: none;
    margin: 0;
    span{
      display: inline-block;
      vertical-align: middle;
    }
  }
  .question_name,.target_question_name{
    max-width: 150px;
    overflow: hidden;
    text-overflow: ellipsis;
    white-space: nowrap;
  }
  .type,.target{
    color:$--color-primary;
    margin: 0 4px;
  }
}

.tooltip[data-show] {
  //display: block;
  opacity: 1;
}
</style>
<style lang="scss" >
 .question-node-item {
   fill: #484848;
   font-size: 12px;
   font-weight: lighter;
   font-family: PingFangSC-Regular;
   cursor: pointer;
}
 .question-node-item rect {
  stroke: #aaa;
  border-radius: 2px;
  fill: #fff;
  font-size: 18px;
  user-select: none
}
 .question-node-item_2 rect,.question-node-item_3 rect{
   stroke: $--color-primary;
   stroke-dasharray: 3px;
 }

 .edgePath path {
  cursor: pointer;
  stroke-dasharray: 3px;
}

 .edgePath path:focus, :focus {
  outline: none
}

</style>
