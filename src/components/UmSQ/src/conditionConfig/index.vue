<template>
  <div v-if="config" class="um-sq_condition">
    <div class="um-sq_condition_left">
      <div class="m-head">
        <div class="u-title">逻辑设置</div>
        <el-button type="primary" icon="el-icon-circle-plus-outline" @click="addHandler">添加逻辑设置</el-button>
      </div>
      <div class="m-content">
        <ul class="m-cond_list">
          <li v-for="(item,idx) in conditionConfig" :key="item.condition_id" class="m-cond_item">
            <div class="m-line1">
              <span class="txt txt_30">如果</span>
              <el-select
                v-model="item.question_id"
                class="option"
                placeholder="请选择题目"
                @change="val=>changeQuestion(val,item)"
              >
                <el-option
                  v-for="(item,idx) in pages"
                  :key="item.question_id"
                  :value="item.question_id"
                  :label="filterHtmlToText(item.title,item.xh)"
                />
                <el-option
                  v-for="(item,idx) in internalCondition"
                  :key="item.question_id"
                  :value="item.question_id"
                  :label="item.title"
                />
              </el-select>
              <span class="txt">选中</span>
              <el-select
                v-model="item.question_value"
                class="option"
                :multiple="true"
                collapse-tags
                placeholder="请选择选项"
                @change="changeOption"
              >
                <template v-if="options[item.question_id]">
                  <el-option
                    v-for="(item,idx) in options[item.question_id].option_list"
                    :key="item.option_id"
                    :label="filterHtmlToText(item.title)"
                    :value="item.option_id"
                  />
                </template>
              </el-select>
            </div>
            <div v-if="item.status===0" class="error-msg"> 逻辑条件与其他条件相同，请重新设置 </div>
            <div class="m-line2">
              <span class="txt txt_30">则</span>
              <el-select v-model="item.target" class="option" @change="changeTarget('target',item)">
                <el-option v-if="!['hasClub','isHfOperate','decorationType'].includes(item.question_id)" :value="1" label="跳转" />
                <el-option :value="2" label="显示" />
                <el-option :value="3" label="隐藏" />
                <el-option :value="4" label="创建" />
              </el-select>
              <span class="txt">&nbsp;</span>
              <el-select
                v-if="[2,3].includes(item.target) "
                key="multiple1"
                v-model="item.target_question_id"
                class="option"
                :multiple="true"
                placeholder="请选择题目"
                collapse-tags
                @change="changeTarget('target_question_id',item)"
              >
                <el-option
                  v-for="(option,idx) in config.pages"
                  :key="option.question_id"
                  :value="option.question_id"
                  :disabled="disabledHandler(idx,item.question_id)"
                  :label="filterHtmlToText( option.type===5 ? option.content : option.title,option.xh) "
                />
              </el-select>
              <el-select
                v-if="[1].includes(item.target) "
                key="multiple2"
                v-model="item.target_question_id"
                class="option"
                :multiple="false"
                placeholder="请选择题目"
                collapse-tags
                @change="changeTarget('target_question_id',item)"
              >
                <el-option
                  v-for="(option,idx) in config.pages"
                  :key="option.question_id"
                  :value="option.question_id"
                  :disabled="disabledHandler(idx,item.question_id)"
                  :label="filterHtmlToText( option.type===5 ? option.content : option.title,option.xh) "
                />
                <el-option
                  v-for="(option,idx) in endPageList"
                  :key="option.question_id"
                  :value="option.question_id"
                  :label="option.title"
                />
              </el-select>
              <span class="txt txt-tip">{{ TARGET_TIPS[item.target] }}</span>
            </div>
            <div class="u-delete" @click="removeHandler(idx)">
              <i class="um_iconfont">&#xe680;</i>
            </div>
          </li>
        </ul>
        <div v-if="conditionConfig.length===0" key="none" class="m-none">
          您还未设置任何逻辑
        </div>
      </div>
    </div>
    <div class="um-sq_condition_right">
      <div class="m-head">
        <div class="u-title">逻辑预览</div>
      </div>
      <div class="m-content">
        <workflow :condition-effect="conditionEffect" :pages="config.pages" :config="config" />
      </div>
    </div>
  </div>
</template>

<script>
import { htmlToText } from '../utils'
import workflow from './workflow'
import { endPageList, internalCondition } from '@/components/UmSQ/src/core/factory'

const TARGET_TIPS = {
  1: '否则正常进入下一题',
  2: '否则不显示',
  3: '否则显示',
  4: '满意度工单'
}
export default {
  name: 'ConditionConfig',
  components: { workflow },
  filters: {
  },
  props: {
    config: {
      type: Object,
      default: null
    }
  },
  data() {
    return {
      internalCondition, // 内置条件
      endPageList, // 新增两种结束规则
      htmlToText,
      TARGET_TIPS,
      conditionEffect: [] // 有效的问题
    }
  },
  computed: {
    conditionConfig() {
      return this.config.conditionConfig
    },
    // 题目和选项的map 结构
    options() {
      const map = {}
      // 处理内置条件
      this.internalCondition.forEach(item => {
        map[item.question_id] = {
          ...item
        }
      })
      // 处理问卷条件
      this.pages.forEach((item, xh) => {
        map[item.question_id] = {
          ...item,
          xh: xh + 1
        }
      })
      return map
    },
    // 过滤之后的题目
    pages() {
      if (this.config) {
        const pages = this.config.pages
        // 单选 多选 打分题 可以做为选项
        return pages.filter(i => [1, 2, 4].includes(i.type))
      }
      return []
    }
  },
  mounted() {
    window.config = this.config
    // 对题目进行排序
    this.config.sortPages()
    this.conditionEffect = this.config.getConditionEffect()
  },
  methods: {
    // 处理选项是否禁用
    disabledHandler(idx, question_id) {
      if (!question_id) return true
      const index = this.config.pages.findIndex(i => i.question_id === question_id)
      return idx <= index
    },
    // 处理html转换文字
    filterHtmlToText(val, idx) {
      let text = htmlToText(val)
      if (idx !== undefined && idx !== null) {
        text = `${idx}.${text}`
      }
      if (text.length > 20) {
        return text.slice(0, 20) + '...'
      }
      return text
    },
    // 题目变更
    changeQuestion(val, item) {
      // 清除选项
      if (this.options[item.question_id] && this.options[item.question_id].type === 2) {
        item.question_value = []
      } else {
        item.question_value = ''
      }
      // 清除目标题目
      item.target = ''
      item.target_question_id = ''
    },
    // 选项变更
    changeOption() {
      this.config.conditionValidate()
      this.conditionEffect = this.config.getConditionEffect() // 重新绘制流程图
    },
    // 目标题目变更
    changeTarget(field, item) {
      if (field === 'target') {
        item.target_question_id = ''
        this.$forceUpdate()
      }
      this.conditionEffect = this.config.getConditionEffect() // 重新绘制流程图
    },
    // 添加逻辑
    addHandler() {
      this.config.addCondition()
    },
    // 删除逻辑
    removeHandler(idx) {
      this.conditionConfig.splice(idx, 1)
      this.conditionEffect = this.config.getConditionEffect() // 重新绘制流程图
    }
  }
}
</script>
<style lang="scss">
@import "../style/condition";
</style>
