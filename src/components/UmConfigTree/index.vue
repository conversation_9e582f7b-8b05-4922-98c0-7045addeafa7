<template>
  <div>
    <el-tree
      v-if="treeData.length"
      ref="tree"
      :node-key="defaultProps.value"
      :draggable="draggable"
      :expand-all="expandAll"
      :data="treeData"
      :allow-drop="allowDrop"
      :allow-drag="allowDrag"
      :props="defaultProps"
      :highlight-current="true"
      :default-expanded-keys="defaultExpandedKeys"
      :default-checked-keys="[currentId]"
      :current-node-key="currentId"
      @node-expand="nodeExpand"
      @node-collapse="nodeCollapse"
      @node-click="handleNodeClick"
      @node-drop="handleNodeDrop"
    >
      <span slot-scope="{ node, data }" class="custom-tree-node">
        <span class="custom-tree-node__l">
          <span class="tree-name row_1" :title="node.label">
            {{ node.label }}
          </span>
        </span>
        <div v-if="showMenu" class="custom-tree-node__r">
          <slot name="menu" :data="data" />
        </div>
      </span>
    </el-tree>
    <UmEmpty v-else />
  </div>
</template>

<script>
export default {
  name: 'UmConfigTree',
  props: {
    treeData: {
      type: Array,
      default: () => []
    },
    expandAll: {
      type: Boolean,
      default: false
    },
    currentNode: {
      type: Object,
      default: () => ({})
    },
    // el-tree 的props配置
    defaultProps: {
      type: Object,
      default: () => ({
        children: 'children',
        label: 'label',
        value: 'value'
      })
    },
    // 是否允许托拽
    draggable: {
      type: Boolean,
      default: false
    },
    // 是否显示菜单
    showMenu: {
      type: Boolean,
      default: false
    },
    currentId: {
      type: [Number, String],
      default: null
    }
  },
  data() {
    return {
      defaultExpandedKeys: []
    }
  },
  watch: {
    currentId: {
      immediate: true,
      handler(v) {
        if (!v) return
        this.$nextTick(() => {
          this.$refs.tree && this.$refs.tree.setCurrentKey(v)
        })
      }
    },
    treeData: {
      immediate: true,
      handler(v) {
        if (!v.length) return
        this.$nextTick(() => {
          this.currentId && this.$refs.tree && this.$refs.tree.setCurrentKey(this.currentId)
        })
      }
    }
  },
  methods: {
    // allowDrop(draggingNode, dropNode, type) {
    //   return type !== 'inner' && this.draggable
    // },
    allowDrop(draggingNode, dropNode, type) {
      // 注掉的是同级拖拽
      if (draggingNode.level === dropNode.level) {
        return type === 'prev' || type === 'next'
      } else {
        // 不同级进行处理
        return false
      }
    },
    handleNodeDrop(before, after, inner, event) {
      this.$emit('dropSort', { before, after, inner, event })
    },
    allowDrag(draggingNode) {
      return this.draggable
    },
    getAllNodes(node = [], arr = []) {
      for (const item of node) {
        arr.push(item.id)
        const parentArr = []
        if (item.children) parentArr.push(...item.children)
        if (parentArr && parentArr.length) this.getAllNodes(parentArr, arr)
      }
      return arr
    },
    handleNodeClick(data) {
      if (data[this.defaultProps.value] === this.currentId) return
      const node = this.$refs.tree.getNode(data)
      const path = this.iterator(node)

      this.$emit('node-click', data, path.reverse())
    },
    iterator(obj, arr = []) {
      if (!obj || !obj.parent) {
        return arr
      }
      arr.push(obj.data[this.defaultProps.label])
      this.iterator(obj.parent, arr)
      return arr
    },
    // 节点展开
    nodeExpand(data) {
      this.defaultExpandedKeys.push(data[this.defaultProps.value])
    },
    // 节点收起
    nodeCollapse(data) {
      const index = this.defaultExpandedKeys.findIndex(item => item === data[this.defaultProps.value])
      this.defaultExpandedKeys.splice(index, 1)
    }
  }
}
</script>
<style lang="scss" scoped>
  ::v-deep {
    .el-tree-node__content {
      height: 42px;
    }
  }
  .custom-tree-node {
    display: flex;
    width: 100%;
    line-height: 42px;
    overflow: hidden;
    padding: 0 12px 0 0px;
    cursor: pointer;
    color: #333;
    font-size: 14px;
    font-weight: 500;
    &:hover {
      color: $--color-primary;
    }
    &__l {
      flex: 1;
      display: flex;
      align-items: center;
      overflow: hidden;
      .tree-name {
        width: auto;
        font-size: 14px;
      }
    }
    &__r {
      margin-left: 5px;
      .tree-move {
        margin-right: 8px;
      }
      .tree-more:hover {
        color: $--color-primary;
      }
    }
  }
</style>
