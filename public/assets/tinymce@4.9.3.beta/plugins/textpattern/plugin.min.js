!function(){"use strict";var t,n,e,r,a,o=function(t){var n=t,e=function(){return n};return{get:e,set:function(t){n=t},clone:function(){return o(e())}}},i=tinymce.util.Tools.resolve("tinymce.PluginManager"),f=function(t){return function(){return t}},u=f(!1),s=f(!0),c=u,l=s,d=function(){return g},g=(r={fold:function(t,n){return t()},is:c,isSome:c,isNone:l,getOr:e=function(t){return t},getOrThunk:n=function(t){return t()},getOrDie:function(t){throw new Error(t||"error: getOrDie called on none.")},getOrNull:function(){return null},getOrUndefined:function(){return undefined},or:e,orThunk:n,map:d,ap:d,each:function(){},bind:d,flatten:d,exists:c,forall:l,filter:d,equals:t=function(t){return t.isNone()},equals_:t,toArray:function(){return[]},toString:f("none()")},Object.freeze&&Object.freeze(r),r),m=function(e){var t=function(){return e},n=function(){return a},r=function(t){return t(e)},a={fold:function(t,n){return n(e)},is:function(t){return e===t},isSome:l,isNone:c,getOr:t,getOrThunk:t,getOrDie:t,getOrNull:t,getOrUndefined:t,or:n,orThunk:n,map:function(t){return m(t(e))},ap:function(t){return t.fold(d,function(t){return m(t(e))})},each:function(t){t(e)},bind:r,flatten:t,exists:r,forall:r,filter:function(t){return t(e)?a:g},equals:function(t){return t.is(e)},equals_:function(t,n){return t.fold(c,function(t){return n(e,t)})},toArray:function(){return[e]},toString:function(){return"some("+e+")"}};return a},h={some:m,none:d,from:function(t){return null===t||t===undefined?g:m(t)}},p=(a="function",function(t){return function(t){if(null===t)return"null";var n=typeof t;return"object"===n&&Array.prototype.isPrototypeOf(t)?"array":"object"===n&&String.prototype.isPrototypeOf(t)?"string":n}(t)===a}),v=function(t,n){for(var e=[],r=0,a=t.length;r<a;r++){var o=t[r];n(o,r,t)&&e.push(o)}return e},O=Array.prototype.slice,y=(p(Array.from)&&Array.from,Object.hasOwnProperty),P=function(t,n){return y.call(t,n)},x=function(t){return P(t,"start")&&P(t,"end")},T=function(t){return!P(t,"end")&&!P(t,"replacement")},b=function(t){return P(t,"replacement")},k=function(t){return n=t,e=function(t,n){return t.start.length===n.start.length?0:t.start.length>n.start.length?-1:1},(r=O.call(n,0)).sort(e),r;var n,e,r},C=function(t){return{inlinePatterns:k(v(t,x)),blockPatterns:k(v(t,T)),replacementPatterns:v(t,b)}},D=function(n){return{setPatterns:function(t){n.set(C(t))},getPatterns:function(){return n.get().inlinePatterns.concat(n.get().blockPatterns,n.get().replacementPatterns)}}},S=[{start:"*",end:"*",format:"italic"},{start:"**",end:"**",format:"bold"},{start:"***",end:"***",format:["bold","italic"]},{start:"#",format:"h1"},{start:"##",format:"h2"},{start:"###",format:"h3"},{start:"####",format:"h4"},{start:"#####",format:"h5"},{start:"######",format:"h6"},{start:"1. ",cmd:"InsertOrderedList"},{start:"* ",cmd:"InsertUnorderedList"},{start:"- ",cmd:"InsertUnorderedList"}],A=function(t){var n,e,r=(n=t,e="textpattern_patterns",P(n,e)?h.some(n[e]):h.none()).getOr(S);return C(r)},N=tinymce.util.Tools.resolve("tinymce.util.Delay"),R=tinymce.util.Tools.resolve("tinymce.util.VK"),I=tinymce.util.Tools.resolve("tinymce.dom.TreeWalker"),j=tinymce.util.Tools.resolve("tinymce.util.Tools"),w=function(t,n){for(var e=0;e<t.length;e++){var r=t[e];if(0===n.indexOf(r.start)&&(!r.end||n.lastIndexOf(r.end)===n.length-r.end.length))return r}},E=function(t,n,e){if(!1!==n.collapsed){var r=n.startContainer,a=r.data,o=!0===e?1:0;if(3===r.nodeType){var i=function(t,n,e,r){var a,o,i,f,u,s;for(o=0;o<t.length;o++)if((a=t[o]).end!==undefined&&(f=a,u=e,s=r,n.substr(u-f.end.length-s,f.end.length)===f.end)&&0<e-r-(i=a).end.length-i.start.length)return a}(t,a,n.startOffset,o);if(i!==undefined){var f=a.lastIndexOf(i.end,n.startOffset-o),u=a.lastIndexOf(i.start,f-i.end.length);if(f=a.indexOf(i.end,u+i.start.length),-1!==u){var s=document.createRange();s.setStart(r,u),s.setEnd(r,f+i.end.length);var c=w(t,s.toString());if(!(i===undefined||c!==i||r.data.length<=i.start.length+i.end.length))return{pattern:i,startOffset:u,endOffset:f}}}}}},q=function(t){return t&&3===t.nodeType},L=function(t,n,e){var r=t.dom.createRng();r.setStart(n,e),r.setEnd(n,e),t.selection.setRng(r)},M=function(n,t,e){var r=n.selection.getRng();return h.from(E(t,r,e)).map(function(t){return function(a,o,i,f){var u=j.isArray(i.pattern.format)?i.pattern.format:[i.pattern.format];if(0!==j.grep(u,function(t){var n=a.formatter.get(t);return n&&n[0].inline}).length)return a.undoManager.transact(function(){var t,n,e,r;t=o,n=i.pattern,e=i.endOffset,r=i.startOffset,(t=0<r?t.splitText(r):t).splitText(e-r+n.end.length),t.deleteData(0,n.start.length),t.deleteData(t.data.length-n.end.length,n.end.length),o=t,f&&a.selection.setCursorLocation(o.nextSibling,1),u.forEach(function(t){a.formatter.apply(t,{},o)})}),o}(n,r.startContainer,t,e)})},U=function(s,t,c){var n=s.selection.getRng(),l=n.startContainer;n.collapsed&&q(l)&&function(t,n,e){for(var r=0;r<t.length;r++){var a=e.lastIndexOf(t[r].start,n);if(-1!==a)return h.some({pattern:t[r],startOffset:a})}return h.none()}(t,n.startOffset,l.data).each(function(t){var n,e,r,a,o,i,f,u=c?h.some((n=l,r=(e=t).startOffset+e.pattern.start.length,a=n.data.slice(r,r+1),n.deleteData(r,1),a)):h.none();o=s,f=t,(i=l).deleteData(f.startOffset,f.pattern.start.length),o.insertContent(f.pattern.replacement),h.from(i.nextSibling).filter(q).each(function(t){t.insertData(0,i.data),o.dom.remove(i)}),u.each(function(t){return function(t,n){var e=t.selection.getRng(),r=e.startContainer;if(q(r)){var a=e.startOffset;r.insertData(a,n),L(t,r,a+n.length)}else{var o=t.dom.doc.createTextNode(n);e.insertNode(o),L(t,o,o.length)}}(s,t)})})},_=function(t,n,e){for(var r=0;r<t.length;r++)if(e(t[r],n))return!0},z=function(t,n){var e,r,a,o;e=t,r=n.replacementPatterns,U(e,r,!1),a=t,o=n.inlinePatterns,M(a,o,!1).each(function(t){L(a,t,t.data.length)}),function(t,n){var e,r,a,o,i,f,u,s,c,l,d;if(e=t.selection,r=t.dom,e.isCollapsed()&&(u=r.getParent(e.getStart(),"p"))){for(c=new I(u,u);i=c.next();)if(q(i)){o=i;break}if(o){if(!(s=w(n,o.data)))return;if(a=(l=e.getRng(!0)).startContainer,d=l.startOffset,o===a&&(d=Math.max(0,d-s.start.length)),j.trim(o.data).length===s.start.length)return;s.format&&(f=t.formatter.get(s.format))&&f[0].block&&(o.deleteData(0,s.start.length),t.formatter.apply(s.format,{},o),l.setStart(a,d),l.collapse(!0),e.setRng(l)),s.cmd&&t.undoManager.transact(function(){o.deleteData(0,s.start.length),t.execCommand(s.cmd)})}}}(t,n.blockPatterns)},K=function(t,n){var e,r,a,o;e=t,r=n.replacementPatterns,U(e,r,!0),a=t,o=n.inlinePatterns,M(a,o,!0).each(function(t){var n=t.data.slice(-1);if(/[\u00a0 ]/.test(n)){t.deleteData(t.data.length-1,1);var e=a.dom.doc.createTextNode(n);a.dom.insertAfter(e,t.parentNode),L(a,e,1)}})},V=function(t,n){return _(t,n,function(t,n){return t.charCodeAt(0)===n.charCode})},W=function(t,n){return _(t,n,function(t,n){return t===n.keyCode&&!1===R.modifierPressed(n)})},B=function(n,e){var r=[",",".",";",":","!","?"],a=[32];n.on("keydown",function(t){13!==t.keyCode||R.modifierPressed(t)||z(n,e.get())},!0),n.on("keyup",function(t){W(a,t)&&K(n,e.get())}),n.on("keypress",function(t){V(r,t)&&N.setEditorTimeout(n,function(){K(n,e.get())})})};i.add("textpattern",function(t){var n=o(A(t.settings));return B(t,n),D(n)})}();