!function(){"use strict";var e=tinymce.util.Tools.resolve("tinymce.PluginManager"),o=tinymce.util.Tools.resolve("tinymce.util.Tools"),c=tinymce.util.Tools.resolve("tinymce.util.XHR"),t=tinymce.util.Tools.resolve("tinymce.dom.DOMUtils"),i=function(e){return e.getParam("template_cdate_classes","cdate")},s=function(e){return e.getParam("template_mdate_classes","mdate")},u=function(e){return e.getParam("template_selected_content_classes","selcontent")},p=function(e){return e.getParam("template_preview_replace_values")},m=function(e){return e.getParam("template_replace_values")},a=function(e){return e.templates},d=function(e){return e.getParam("template_cdate_format",e.getLang("template.cdate_format"))},f=function(e){return e.getParam("template_mdate_format",e.getLang("template.mdate_format"))},g=function(e){return e.getParam("template_popup_width",600)},h=function(e){return Math.min(t.DOM.getViewPort().h,e.getParam("template_popup_height",500))},y=function(e,t){if((e=""+e).length<t)for(var n=0;n<t-e.length;n++)e="0"+e;return e},v=function(e,t,n){var a="Sun Mon Tue Wed Thu Fri Sat Sun".split(" "),r="Sunday Monday Tuesday Wednesday Thursday Friday Saturday Sunday".split(" "),l="Jan Feb Mar Apr May Jun Jul Aug Sep Oct Nov Dec".split(" "),c="January February March April May June July August September October November December".split(" ");return n=n||new Date,t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=(t=t.replace("%D","%m/%d/%Y")).replace("%r","%I:%M:%S %p")).replace("%Y",""+n.getFullYear())).replace("%y",""+n.getYear())).replace("%m",y(n.getMonth()+1,2))).replace("%d",y(n.getDate(),2))).replace("%H",""+y(n.getHours(),2))).replace("%M",""+y(n.getMinutes(),2))).replace("%S",""+y(n.getSeconds(),2))).replace("%I",""+((n.getHours()+11)%12+1))).replace("%p",n.getHours()<12?"AM":"PM")).replace("%B",""+e.translate(c[n.getMonth()]))).replace("%b",""+e.translate(l[n.getMonth()]))).replace("%A",""+e.translate(r[n.getDay()]))).replace("%a",""+e.translate(a[n.getDay()]))).replace("%%","%")},M=function(e,n,t){return o.each(t,function(e,t){"function"==typeof e&&(e=e(t)),n=n.replace(new RegExp("\\{\\$"+t+"\\}","g"),e)}),n},_=function(e,t){var a=e.dom,r=m(e);o.each(a.select("*",t),function(n){o.each(r,function(e,t){a.hasClass(n,t)&&"function"==typeof r[t]&&r[t](n)})})},b=function(e,t){return new RegExp("\\b"+t+"\\b","g").test(e.className)},n=function(t,n){return function(){var e=a(t);"function"!=typeof e?"string"==typeof e?c.send({url:e,success:function(e){n(JSON.parse(e))}}):n(e):e(n)}},T=M,r=_,x=function(t,e,n){var a,r,l=t.dom,c=t.selection.getContent();n=M(0,n,m(t)),a=l.create("div",null,n),(r=l.select(".mceTmpl",a))&&0<r.length&&(a=l.create("div",null)).appendChild(r[0].cloneNode(!0)),o.each(l.select("*",a),function(e){b(e,i(t).replace(/\s+/g,"|"))&&(e.innerHTML=v(t,d(t))),b(e,s(t).replace(/\s+/g,"|"))&&(e.innerHTML=v(t,f(t))),b(e,u(t).replace(/\s+/g,"|"))&&(e.innerHTML=c)}),_(t,a),t.execCommand("mceInsertContent",!1,a.innerHTML),t.addVisual()},l=function(e){e.addCommand("mceInsertTemplate",function(a){for(var r=[],e=1;e<arguments.length;e++)r[e-1]=arguments[e];return function(){for(var e=[],t=0;t<arguments.length;t++)e[t]=arguments[t];var n=r.concat(e);return a.apply(null,n)}}(x,e))},P=function(a){a.on("PreProcess",function(e){var t=a.dom,n=f(a);o.each(t.select("div",e.node),function(e){t.hasClass(e,"mceTmpl")&&(o.each(t.select("*",e),function(e){t.hasClass(e,a.getParam("template_mdate_classes","mdate").replace(/\s+/g,"|"))&&(e.innerHTML=v(a,n))}),r(a,e))})})},S=function(t,e,n){if(-1===n.indexOf("<html>")){var a="";o.each(t.contentCSS,function(e){a+='<link type="text/css" rel="stylesheet" href="'+t.documentBaseURI.toAbsolute(e)+'">'});var r=t.settings.body_class||"";-1!==r.indexOf("=")&&(r=(r=t.getParam("body_class","","hash"))[t.id]||""),n="<!DOCTYPE html><html><head>"+a+'</head><body class="'+r+'">'+n+"</body></html>"}n=T(t,n,p(t));var l=e.find("iframe")[0].getEl().contentWindow.document;l.open(),l.write(n),l.close()},D=function(n,e){var a,r,t=[];if(e&&0!==e.length)o.each(e,function(e){t.push({selected:!t.length,text:e.title,value:{url:e.url,content:e.content,description:e.description}})}),(a=n.windowManager.open({title:"Insert template",layout:"flex",direction:"column",align:"stretch",padding:15,spacing:10,items:[{type:"form",flex:0,padding:0,items:[{type:"container",label:"Templates",items:{type:"listbox",label:"Templates",name:"template",values:t,onselect:function(e){var t=e.control.value();t.url?c.send({url:t.url,success:function(e){S(n,a,r=e)}}):(r=t.content,S(n,a,r)),a.find("#description")[0].text(e.control.value().description)}}}]},{type:"label",name:"description",label:"Description",text:"\xa0"},{type:"iframe",flex:1,border:1}],onsubmit:function(){x(n,!1,r)},minWidth:g(n),minHeight:h(n)})).find("listbox")[0].fire("select");else{var l=n.translate("No templates defined.");n.notificationManager.open({text:l,type:"info"})}},H=function(t){return function(e){D(t,e)}},w=function(e){e.addButton("template",{title:"Insert template",onclick:n(e.settings,H(e))}),e.addMenuItem("template",{text:"Template",onclick:n(e.settings,H(e)),icon:"template",context:"insert"})};e.add("template",function(e){w(e),l(e),P(e)})}();